using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace HR_InvoiceArchiver.Tests.Models
{
    public class MultiPaymentModelTests
    {
        [Fact]
        public void MultiPaymentModel_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var multiPayment = new MultiPaymentModel();

            // Assert
            multiPayment.Id.Should().Be(0);
            multiPayment.SupplierId.Should().Be(0);
            multiPayment.SupplierName.Should().Be(string.Empty);
            multiPayment.ReceiptNumber.Should().Be(string.Empty);
            multiPayment.PaymentDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
            multiPayment.TotalInvoicesAmount.Should().Be(0);
            multiPayment.DiscountAmount.Should().Be(0);
            multiPayment.DiscountPercentage.Should().Be(0);
            multiPayment.FinalAmount.Should().Be(0);
            multiPayment.PaymentMethod.Should().Be("نقدي");
            multiPayment.Notes.Should().Be(string.Empty);
            multiPayment.AttachmentPath.Should().Be(string.Empty);
            multiPayment.SelectedInvoices.Should().NotBeNull();
            multiPayment.SelectedInvoices.Should().BeEmpty();
        }

        [Fact]
        public void MultiPaymentModel_PropertyChanged_ShouldBeRaisedWhenPropertyChanges()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            var propertyChangedRaised = false;
            string? changedPropertyName = null;

            multiPayment.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            multiPayment.SupplierName = "مورد جديد";

            // Assert
            propertyChangedRaised.Should().BeTrue();
            changedPropertyName.Should().Be(nameof(MultiPaymentModel.SupplierName));
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithThreeInvoices_ShouldApplyOnePercentDiscount()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1000, PaymentAmount = 1000 });
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 2000, PaymentAmount = 2000 });
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1500, PaymentAmount = 1500 });

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(4500);
            multiPayment.DiscountPercentage.Should().Be(1); // 3 فواتير = 1%
            multiPayment.DiscountAmount.Should().Be(45); // 4500 * 1%
            multiPayment.FinalAmount.Should().Be(4455); // 4500 - 45
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithFiveInvoices_ShouldApplyThreePercentDiscount()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            for (int i = 0; i < 5; i++)
            {
                multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1000, PaymentAmount = 1000 });
            }

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(5000);
            multiPayment.DiscountPercentage.Should().Be(3); // 5 فواتير = 3%
            multiPayment.DiscountAmount.Should().Be(150); // 5000 * 3%
            multiPayment.FinalAmount.Should().Be(4850); // 5000 - 150
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithTenInvoices_ShouldApplyFivePercentDiscount()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            for (int i = 0; i < 10; i++)
            {
                multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1000, PaymentAmount = 1000 });
            }

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(10000);
            multiPayment.DiscountPercentage.Should().Be(5); // 10 فواتير = 5%
            multiPayment.DiscountAmount.Should().Be(500); // 10000 * 5%
            multiPayment.FinalAmount.Should().Be(9500); // 10000 - 500
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithHighAmountAndManyInvoices_ShouldApplyMaxDiscount()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            for (int i = 0; i < 10; i++)
            {
                multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 2000000, PaymentAmount = 2000000 });
            }

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(20000000);
            // خصم الفواتير: 5% (10 فواتير)
            // خصم المبلغ: 3% (أكثر من 10 مليون)
            // إجمالي الخصم: 8%
            multiPayment.DiscountPercentage.Should().Be(8);
            multiPayment.DiscountAmount.Should().Be(1600000); // 20000000 * 8%
            multiPayment.FinalAmount.Should().Be(18400000); // 20000000 - 1600000
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithVeryHighAmountAndManyInvoices_ShouldNotExceedMaxDiscount()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            for (int i = 0; i < 15; i++) // أكثر من 10 فواتير
            {
                multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 5000000, PaymentAmount = 5000000 });
            }

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(75000000);
            // خصم الفواتير: 5% (10+ فواتير)
            // خصم المبلغ: 3% (أكثر من 10 مليون)
            // إجمالي الخصم: 8% (لكن الحد الأقصى 15%)
            multiPayment.DiscountPercentage.Should().Be(8);
            multiPayment.DiscountAmount.Should().Be(6000000); // 75000000 * 8%
            multiPayment.FinalAmount.Should().Be(69000000); // 75000000 - 6000000
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithNoInvoices_ShouldHaveZeroValues()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(0);
            multiPayment.DiscountPercentage.Should().Be(0);
            multiPayment.DiscountAmount.Should().Be(0);
            multiPayment.FinalAmount.Should().Be(0);
        }

        [Fact]
        public void MultiPaymentModel_CalculateDiscount_WithTwoInvoices_ShouldHaveNoDiscount()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel();
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1000, PaymentAmount = 1000 });
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 2000, PaymentAmount = 2000 });

            // Act
            multiPayment.CalculateDiscount();

            // Assert
            multiPayment.TotalInvoicesAmount.Should().Be(3000);
            multiPayment.DiscountPercentage.Should().Be(0); // أقل من 3 فواتير = لا يوجد خصم
            multiPayment.DiscountAmount.Should().Be(0);
            multiPayment.FinalAmount.Should().Be(3000);
        }

        [Fact]
        public void MultiPaymentModel_IsValid_WithValidData_ShouldReturnTrue()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel
            {
                ReceiptNumber = "MP-001",
                SupplierId = 1,
                PaymentMethod = "نقدي"
            };
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1000, PaymentAmount = 1000 });

            // Act
            var isValid = multiPayment.IsValid;

            // Assert
            isValid.Should().BeTrue();
        }

        [Theory]
        [InlineData("", 1, "نقدي")] // رقم وصل فارغ
        [InlineData("MP-001", 0, "نقدي")] // معرف مورد غير صحيح
        [InlineData("MP-001", 1, "")] // طريقة دفع فارغة
        public void MultiPaymentModel_IsValid_WithInvalidData_ShouldReturnFalse(string receiptNumber, int supplierId, string paymentMethod)
        {
            // Arrange
            var multiPayment = new MultiPaymentModel
            {
                ReceiptNumber = receiptNumber,
                SupplierId = supplierId,
                PaymentMethod = paymentMethod
            };
            multiPayment.SelectedInvoices.Add(new MultiPaymentInvoiceModel { InvoiceAmount = 1000, PaymentAmount = 1000 });

            // Act
            var isValid = multiPayment.IsValid;

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void MultiPaymentModel_IsValid_WithNoSelectedInvoices_ShouldReturnFalse()
        {
            // Arrange
            var multiPayment = new MultiPaymentModel
            {
                ReceiptNumber = "MP-001",
                SupplierId = 1,
                PaymentMethod = "نقدي"
            };

            // Act
            var isValid = multiPayment.IsValid;

            // Assert
            isValid.Should().BeFalse();
        }
    }

    public class MultiPaymentInvoiceModelTests
    {
        [Fact]
        public void MultiPaymentInvoiceModel_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var invoiceModel = new MultiPaymentInvoiceModel();

            // Assert
            invoiceModel.InvoiceId.Should().Be(0);
            invoiceModel.InvoiceNumber.Should().Be(string.Empty);
            invoiceModel.InvoiceDate.Should().Be(default(DateTime));
            invoiceModel.InvoiceAmount.Should().Be(0);
            invoiceModel.PaidAmount.Should().Be(0);
            invoiceModel.RemainingAmount.Should().Be(0);
            invoiceModel.PaymentAmount.Should().Be(0);
            invoiceModel.IsSelected.Should().BeFalse();
        }

        [Fact]
        public void MultiPaymentInvoiceModel_DisplayText_ShouldFormatCorrectly()
        {
            // Arrange
            var invoiceModel = new MultiPaymentInvoiceModel
            {
                InvoiceNumber = "INV-001",
                InvoiceDate = new DateTime(2024, 1, 15),
                RemainingAmount = 1500
            };

            // Act
            var displayText = invoiceModel.DisplayText;

            // Assert
            displayText.Should().Be("فاتورة #INV-001 - 2024/01/15 - 1,500 د.ع");
        }

        [Fact]
        public void MultiPaymentInvoiceModel_StatusText_WithFullRemaining_ShouldReturnUnpaid()
        {
            // Arrange
            var invoiceModel = new MultiPaymentInvoiceModel
            {
                InvoiceAmount = 1000,
                RemainingAmount = 1000
            };

            // Act
            var statusText = invoiceModel.StatusText;

            // Assert
            statusText.Should().Be("غير مدفوعة");
        }

        [Fact]
        public void MultiPaymentInvoiceModel_StatusText_WithPartialRemaining_ShouldReturnPartiallyPaid()
        {
            // Arrange
            var invoiceModel = new MultiPaymentInvoiceModel
            {
                InvoiceAmount = 1000,
                RemainingAmount = 500
            };

            // Act
            var statusText = invoiceModel.StatusText;

            // Assert
            statusText.Should().Be("مدفوعة جزئياً");
        }

        [Fact]
        public void MultiPaymentInvoiceModel_PropertyChanged_ShouldBeRaisedWhenPropertyChanges()
        {
            // Arrange
            var invoiceModel = new MultiPaymentInvoiceModel();
            var propertyChangedRaised = false;
            string? changedPropertyName = null;

            invoiceModel.PropertyChanged += (sender, e) =>
            {
                propertyChangedRaised = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            invoiceModel.IsSelected = true;

            // Assert
            propertyChangedRaised.Should().BeTrue();
            changedPropertyName.Should().Be(nameof(MultiPaymentInvoiceModel.IsSelected));
        }
    }
}
