using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using System;
using System.Threading.Tasks;
using Moq;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class SecurityServiceTests
    {
        private readonly SecurityService _securityService;
        private readonly Mock<IEncryptionService> _mockEncryptionService;
        private readonly Mock<ILoggingService> _mockLoggingService;
        private readonly Mock<ISettingsService> _mockSettingsService;

        public SecurityServiceTests()
        {
            _mockEncryptionService = new Mock<IEncryptionService>();
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSettingsService = new Mock<ISettingsService>();

            // Setup default settings
            var defaultSettings = new HR_InvoiceArchiver.Models.SettingsModel();
            defaultSettings.ResetToDefaults();
            _mockSettingsService.Setup(x => x.LoadSettingsAsync())
                .ReturnsAsync(defaultSettings);

            _securityService = new SecurityService(
                _mockEncryptionService.Object,
                _mockLoggingService.Object,
                _mockSettingsService.Object);
        }

        [Fact]
        public async Task LoginAsync_WithValidCredentials_ShouldReturnSuccessfulResult()
        {
            // Arrange
            var username = "admin";
            var password = "admin123";

            _mockEncryptionService.Setup(x => x.CreateHashAsync(password, null))
                .ReturnsAsync("hashed_password");
            _mockEncryptionService.Setup(x => x.CreateHashAsync("admin123", null))
                .ReturnsAsync("hashed_password");

            // Act
            var result = await _securityService.LoginAsync(username, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeTrue();
            result.User.Should().NotBeNull();
            result.User!.Username.Should().Be(username);
            result.SessionToken.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task LoginAsync_WithInvalidCredentials_ShouldReturnFailedResult()
        {
            // Arrange
            var username = "admin";
            var password = "wrongpassword";

            _mockEncryptionService.Setup(x => x.CreateHashAsync("admin123", null))
                .ReturnsAsync("correct_hash");
            _mockEncryptionService.Setup(x => x.CreateHashAsync(password, null))
                .ReturnsAsync("wrong_hash");

            // Act
            var result = await _securityService.LoginAsync(username, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeFalse();
            result.ErrorMessage.Should().NotBeNullOrEmpty();
            result.User.Should().BeNull();
        }

        [Fact]
        public async Task LoginAsync_WithNonExistentUser_ShouldReturnFailedResult()
        {
            // Arrange
            var username = "nonexistent";
            var password = "password";

            // Act
            var result = await _securityService.LoginAsync(username, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeFalse();
            result.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task LoginAsync_WithLockedAccount_ShouldReturnFailedResult()
        {
            // Arrange
            var username = "admin";
            var password = "admin123";

            // Lock the account first
            await _securityService.LockAccountAsync(username, TimeSpan.FromMinutes(15));

            // Act
            var result = await _securityService.LoginAsync(username, password);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccessful.Should().BeFalse();
            result.ErrorMessage.Should().Contain("مقفل");
        }

        [Fact]
        public async Task IsSessionValidAsync_WithoutLogin_ShouldReturnFalse()
        {
            // Act
            var isValid = await _securityService.IsSessionValidAsync();

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public async Task IsSessionValidAsync_AfterSuccessfulLogin_ShouldReturnTrue()
        {
            // Arrange
            var username = "admin";
            var password = "admin123";

            _mockEncryptionService.Setup(x => x.CreateHashAsync(password, null))
                .ReturnsAsync("hashed_password");
            _mockEncryptionService.Setup(x => x.CreateHashAsync("admin123", null))
                .ReturnsAsync("hashed_password");

            await _securityService.LoginAsync(username, password);

            // Act
            var isValid = await _securityService.IsSessionValidAsync();

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public async Task LogoutAsync_AfterLogin_ShouldInvalidateSession()
        {
            // Arrange
            var username = "admin";
            var password = "admin123";

            _mockEncryptionService.Setup(x => x.CreateHashAsync(password, null))
                .ReturnsAsync("hashed_password");
            _mockEncryptionService.Setup(x => x.CreateHashAsync("admin123", null))
                .ReturnsAsync("hashed_password");

            await _securityService.LoginAsync(username, password);

            // Act
            await _securityService.LogoutAsync();

            // Assert
            var isValid = await _securityService.IsSessionValidAsync();
            isValid.Should().BeFalse();
        }

        [Fact]
        public async Task LockAccountAsync_ShouldLockAccount()
        {
            // Arrange
            var username = "testuser";
            var lockDuration = TimeSpan.FromMinutes(30);

            // Act
            await _securityService.LockAccountAsync(username, lockDuration);

            // Assert
            var isLocked = await _securityService.IsAccountLockedAsync(username);
            isLocked.Should().BeTrue();
        }

        [Fact]
        public async Task UnlockAccountAsync_ShouldUnlockAccount()
        {
            // Arrange
            var username = "testuser";
            await _securityService.LockAccountAsync(username, TimeSpan.FromMinutes(30));

            // Act
            await _securityService.UnlockAccountAsync(username);

            // Assert
            var isLocked = await _securityService.IsAccountLockedAsync(username);
            isLocked.Should().BeFalse();
        }

        [Fact]
        public async Task IsAccountLockedAsync_WithExpiredLock_ShouldReturnFalse()
        {
            // Arrange
            var username = "testuser";
            await _securityService.LockAccountAsync(username, TimeSpan.FromMilliseconds(1));

            // Wait for lock to expire
            await Task.Delay(10);

            // Act
            var isLocked = await _securityService.IsAccountLockedAsync(username);

            // Assert
            isLocked.Should().BeFalse();
        }

        [Fact]
        public async Task HasPermissionAsync_WithoutLogin_ShouldReturnFalse()
        {
            // Act
            var hasPermission = await _securityService.HasPermissionAsync("TestPermission");

            // Assert
            hasPermission.Should().BeFalse();
        }

        [Fact]
        public async Task HasPermissionAsync_WithAdminRole_ShouldReturnTrue()
        {
            // Arrange
            var username = "admin";
            var password = "admin123";

            _mockEncryptionService.Setup(x => x.CreateHashAsync(password, null))
                .ReturnsAsync("hashed_password");
            _mockEncryptionService.Setup(x => x.CreateHashAsync("admin123", null))
                .ReturnsAsync("hashed_password");

            await _securityService.LoginAsync(username, password);

            // Act
            var hasPermission = await _securityService.HasPermissionAsync("AnyPermission");

            // Assert
            hasPermission.Should().BeTrue();
        }

        [Fact]
        public async Task GetCurrentUserAsync_WithoutLogin_ShouldReturnNull()
        {
            // Act
            var user = await _securityService.GetCurrentUserAsync();

            // Assert
            user.Should().BeNull();
        }

        [Fact]
        public async Task GetCurrentUserAsync_AfterLogin_ShouldReturnUser()
        {
            // Arrange
            var username = "admin";
            var password = "admin123";

            _mockEncryptionService.Setup(x => x.CreateHashAsync(password, null))
                .ReturnsAsync("hashed_password");
            _mockEncryptionService.Setup(x => x.CreateHashAsync("admin123", null))
                .ReturnsAsync("hashed_password");

            await _securityService.LoginAsync(username, password);

            // Act
            var user = await _securityService.GetCurrentUserAsync();

            // Assert
            user.Should().NotBeNull();
            user!.Username.Should().Be(username);
        }

        [Fact]
        public async Task LogSecurityEventAsync_ShouldAddEventToList()
        {
            // Act
            await _securityService.LogSecurityEventAsync(
                SecurityEventType.LoginSuccess, 
                "Test security event");

            // Assert
            var events = await _securityService.GetRecentSecurityEventsAsync();
            events.Should().NotBeEmpty();
            events.Should().Contain(e => e.Description == "Test security event");
        }

        [Fact]
        public async Task GetRecentSecurityEventsAsync_ShouldReturnEventsInDescendingOrder()
        {
            // Arrange
            await _securityService.LogSecurityEventAsync(SecurityEventType.LoginSuccess, "Event 1");
            await Task.Delay(10); // Ensure different timestamps
            await _securityService.LogSecurityEventAsync(SecurityEventType.LoginSuccess, "Event 2");

            // Act
            var events = await _securityService.GetRecentSecurityEventsAsync();

            // Assert
            events.Should().HaveCountGreaterOrEqualTo(2);
            events[0].Description.Should().Be("Event 2");
            events[1].Description.Should().Be("Event 1");
        }

        [Fact]
        public async Task EncryptSensitiveDataAsync_ShouldCallEncryptionService()
        {
            // Arrange
            var data = "sensitive data";
            var expectedEncrypted = "encrypted_data";
            
            _mockEncryptionService.Setup(x => x.EncryptSensitiveDataAsync(data))
                .ReturnsAsync(expectedEncrypted);

            // Act
            var result = await _securityService.EncryptSensitiveDataAsync(data);

            // Assert
            result.Should().Be(expectedEncrypted);
            _mockEncryptionService.Verify(x => x.EncryptSensitiveDataAsync(data), Times.Once);
        }

        [Fact]
        public async Task DecryptSensitiveDataAsync_ShouldCallEncryptionService()
        {
            // Arrange
            var encryptedData = "encrypted_data";
            var expectedDecrypted = "decrypted data";
            
            _mockEncryptionService.Setup(x => x.DecryptSensitiveDataAsync(encryptedData))
                .ReturnsAsync(expectedDecrypted);

            // Act
            var result = await _securityService.DecryptSensitiveDataAsync(encryptedData);

            // Assert
            result.Should().Be(expectedDecrypted);
            _mockEncryptionService.Verify(x => x.DecryptSensitiveDataAsync(encryptedData), Times.Once);
        }

        [Fact]
        public async Task CreateDataSignatureAsync_ShouldCallEncryptionService()
        {
            // Arrange
            var data = "data to sign";
            var expectedSignature = "signature";
            
            _mockEncryptionService.Setup(x => x.CreateHashAsync(data, null))
                .ReturnsAsync(expectedSignature);

            // Act
            var result = await _securityService.CreateDataSignatureAsync(data);

            // Assert
            result.Should().Be(expectedSignature);
            _mockEncryptionService.Verify(x => x.CreateHashAsync(data, null), Times.Once);
        }

        [Fact]
        public async Task VerifyDataIntegrityAsync_WithValidSignature_ShouldReturnTrue()
        {
            // Arrange
            var data = "data to verify";
            var signature = "valid_signature";
            
            _mockEncryptionService.Setup(x => x.CreateHashAsync(data, null))
                .ReturnsAsync(signature);

            // Act
            var result = await _securityService.VerifyDataIntegrityAsync(data, signature);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task VerifyDataIntegrityAsync_WithInvalidSignature_ShouldReturnFalse()
        {
            // Arrange
            var data = "data to verify";
            var validSignature = "valid_signature";
            var invalidSignature = "invalid_signature";
            
            _mockEncryptionService.Setup(x => x.CreateHashAsync(data, null))
                .ReturnsAsync(validSignature);

            // Act
            var result = await _securityService.VerifyDataIntegrityAsync(data, invalidSignature);

            // Assert
            result.Should().BeFalse();
        }
    }
}
