# نظام أرشفة فواتير الموارد البشرية (HR Invoice Archiver)

## نظرة عامة
نظام أرشفة فواتير الموارد البشرية هو تطبيق WPF مبني بتقنية .NET 8 لإدارة وأرشفة فواتير الموارد البشرية مع دعم النسخ الاحتياطي السحابي.

## بنية المشروع

```
arshafa/
├── Config/                     # ملفات التكوين والإعدادات
│   ├── credentials.json        # بيانات اعتماد Google Drive API
│   └── token.json/            # رموز المصادقة
├── Data/                      # قاعدة البيانات
│   └── invoices.db           # قاعدة بيانات SQLite
├── Documentation/            # التوثيق والأدلة
│   ├── Guides/              # أدلة المطورين والمستخدمين
│   ├── Setup/               # أدلة الإعداد والتكوين
│   └── Updates/             # ملخصات التحديثات والتحسينات
├── HR_InvoiceArchiver/      # المشروع الرئيسي
│   ├── Controls/            # عناصر التحكم المخصصة
│   ├── Converters/          # محولات البيانات
│   ├── Data/               # سياق قاعدة البيانات
│   ├── Models/             # نماذج البيانات
│   ├── Pages/              # صفحات التطبيق
│   ├── Services/           # الخدمات والمنطق التجاري
│   ├── Styles/             # أنماط Material Design
│   ├── UI/                 # مكونات واجهة المستخدم
│   ├── Utils/              # أدوات مساعدة
│   └── Windows/            # النوافذ الرئيسية
└── .gitignore              # ملف استثناءات Git
```

## التقنيات المستخدمة

- **.NET 8**: الإطار الأساسي للتطبيق
- **WPF (Windows Presentation Foundation)**: تقنية الواجهة
- **Entity Framework Core**: لإدارة قاعدة البيانات
- **Material Design in XAML**: للتصميم الحديث
- **Google Drive API**: للنسخ الاحتياطي السحابي
- **SQLite**: قاعدة البيانات المحلية

## المتطلبات

- Windows 10/11
- .NET 8 Runtime
- Visual Studio 2022 (للتطوير)

## التثبيت والتشغيل

1. **استنساخ المشروع:**
   ```bash
   git clone [repository-url]
   cd arshafa
   ```

2. **بناء المشروع:**
   ```bash
   dotnet build HR_InvoiceArchiver/HR_InvoiceArchiver.csproj
   ```

3. **تشغيل التطبيق:**
   ```bash
   dotnet run --project HR_InvoiceArchiver/HR_InvoiceArchiver.csproj
   ```

## الإعداد الأولي

1. **إعداد Google Drive API:**
   - ضع ملف `credentials.json` في مجلد `Config/`
   - راجع `Documentation/Setup/GOOGLE_DRIVE_SETUP.md` للتفاصيل

2. **قاعدة البيانات:**
   - سيتم إنشاء قاعدة البيانات تلقائياً في `Data/invoices.db`
   - يمكن استيراد بيانات أولية باستخدام `SeedData.cs`

## الميزات الرئيسية

- ✅ إدارة فواتير الموارد البشرية
- ✅ أرشفة وتصنيف الفواتير
- ✅ نسخ احتياطي تلقائي إلى Google Drive
- ✅ واجهة مستخدم حديثة بتصميم Material Design
- ✅ نظام تنبيهات متقدم
- ✅ تصدير واستيراد البيانات
- ✅ تحسينات الأداء والذاكرة

## التطوير

راجع `Documentation/Guides/DEVELOPER_GUIDE.md` للحصول على:
- دليل المطور الشامل
- معمارية التطبيق
- إرشادات المساهمة
- أمثلة الكود

## الدعم والمساعدة

- **التوثيق:** `Documentation/`
- **أدلة الإعداد:** `Documentation/Setup/`
- **سجل التغييرات:** `HR_InvoiceArchiver/CHANGELOG.md`

## الترخيص

هذا المشروع مرخص تحت [اسم الترخيص] - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المطور قبل المساهمة.

---

**ملاحظة:** تم تنظيف وتحسين بنية المشروع لتحسين الأداء وسهولة الصيانة.
