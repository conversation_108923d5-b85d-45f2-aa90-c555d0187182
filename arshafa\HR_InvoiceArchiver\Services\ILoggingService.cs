using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// مستويات السجلات
    /// </summary>
    public enum LogLevel
    {
        Trace = 0,
        Debug = 1,
        Information = 2,
        Warning = 3,
        Error = 4,
        Critical = 5
    }

    /// <summary>
    /// فئات السجلات
    /// </summary>
    public enum LogCategory
    {
        General = 0,
        Database = 1,
        UI = 2,
        Business = 3,
        Security = 4,
        Performance = 5,
        CloudSync = 6,
        Payment = 7,
        Invoice = 8,
        Supplier = 9
    }

    /// <summary>
    /// نموذج إدخال السجل
    /// </summary>
    public class LogEntry
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public LogLevel Level { get; set; }
        public LogCategory Category { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Details { get; set; }
        public Exception? Exception { get; set; }
        public string? UserId { get; set; }
        public string? SessionId { get; set; }
        public string? SourceMethod { get; set; }
        public string? SourceFile { get; set; }
        public int? SourceLine { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// معايير البحث في السجلات
    /// </summary>
    public class LogSearchCriteria
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public LogLevel? MinLevel { get; set; }
        public LogLevel? MaxLevel { get; set; }
        public LogCategory? Category { get; set; }
        public string? SearchText { get; set; }
        public string? UserId { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    /// <summary>
    /// نتيجة البحث في السجلات
    /// </summary>
    public class LogSearchResult
    {
        public List<LogEntry> Entries { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    /// <summary>
    /// واجهة خدمة السجلات المتقدمة
    /// </summary>
    public interface ILoggingService
    {
        /// <summary>
        /// تسجيل رسالة معلومات
        /// </summary>
        Task LogAsync(LogLevel level, LogCategory category, string message, 
            Exception? exception = null, object? additionalData = null);

        /// <summary>
        /// تسجيل رسالة معلومات
        /// </summary>
        Task LogInformationAsync(string message, LogCategory category = LogCategory.General, 
            object? additionalData = null);

        /// <summary>
        /// تسجيل تحذير
        /// </summary>
        Task LogWarningAsync(string message, LogCategory category = LogCategory.General, 
            object? additionalData = null);

        /// <summary>
        /// تسجيل خطأ
        /// </summary>
        Task LogErrorAsync(string message, Exception? exception = null, 
            LogCategory category = LogCategory.General, object? additionalData = null);

        /// <summary>
        /// تسجيل خطأ حرج
        /// </summary>
        Task LogCriticalAsync(string message, Exception? exception = null, 
            LogCategory category = LogCategory.General, object? additionalData = null);

        /// <summary>
        /// تسجيل معلومات الأداء
        /// </summary>
        Task LogPerformanceAsync(string operation, TimeSpan duration, 
            object? additionalData = null);

        /// <summary>
        /// تسجيل أحداث الأمان
        /// </summary>
        Task LogSecurityEventAsync(string eventType, string description, 
            string? userId = null, object? additionalData = null);

        /// <summary>
        /// تسجيل عمليات قاعدة البيانات
        /// </summary>
        Task LogDatabaseOperationAsync(string operation, string tableName, 
            bool success, TimeSpan? duration = null, object? additionalData = null);

        /// <summary>
        /// البحث في السجلات
        /// </summary>
        Task<LogSearchResult> SearchLogsAsync(LogSearchCriteria criteria);

        /// <summary>
        /// الحصول على إحصائيات السجلات
        /// </summary>
        Task<Dictionary<LogLevel, int>> GetLogStatisticsAsync(DateTime? fromDate = null, 
            DateTime? toDate = null);

        /// <summary>
        /// تنظيف السجلات القديمة
        /// </summary>
        Task CleanupOldLogsAsync(TimeSpan maxAge);

        /// <summary>
        /// تصدير السجلات
        /// </summary>
        Task<string> ExportLogsAsync(LogSearchCriteria criteria, string format = "csv");

        /// <summary>
        /// الحصول على السجلات الحديثة
        /// </summary>
        Task<List<LogEntry>> GetRecentLogsAsync(int count = 100, LogLevel? minLevel = null);

        /// <summary>
        /// تسجيل بداية عملية
        /// </summary>
        IDisposable BeginScope(string operationName, LogCategory category = LogCategory.General);
    }

    /// <summary>
    /// نطاق العملية للسجلات
    /// </summary>
    public interface ILogScope : IDisposable
    {
        /// <summary>
        /// إضافة خاصية للنطاق
        /// </summary>
        void AddProperty(string key, object value);

        /// <summary>
        /// تسجيل رسالة في النطاق
        /// </summary>
        Task LogAsync(LogLevel level, string message, Exception? exception = null);

        /// <summary>
        /// تحديد نتيجة العملية
        /// </summary>
        void SetResult(bool success, string? message = null);
    }
}
