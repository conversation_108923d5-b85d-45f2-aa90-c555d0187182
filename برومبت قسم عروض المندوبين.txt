
برومبت كتابي: تصميم قسم "عروض المندوبين"

🧠 المطلوب:
قم بإنشاء قسم جديد في النظام بعنوان: "عروض المندوبين"، الهدف منه هو تسجيل وتحليل عروض المندوبين المختلفة لاختيار أفضل عرض لكل مادة علمية.

1. قاعدة البيانات (Database)
صمّم جدولًا جديدًا باسم Offers يحتوي على الحقول التالية:

| اسم الحقل              | نوع البيانات       | الوصف                                        |
|------------------------|--------------------|-----------------------------------------------|
| Id                     | int (PK, Identity) | رقم تعريفي تلقائي                             |
| ScientificOffice       | nvarchar(100)      | اسم المكتب العلمي                             |
| RepresentativeName     | nvarchar(100)      | اسم المندوب                                   |
| RepresentativePhone    | nvarchar(20)       | رقم المندوب                                   |
| ScientificName         | nvarchar(100)      | اسم المادة العلمية                            |
| TradeName              | nvarchar(100)      | اسم المادة التجارية                           |
| Price                  | decimal(18,2)      | السعر                                          |
| BonusOrDiscount        | nvarchar(100)      | البونص أو الخصم (يمكن أن يكون رقم أو وصف)     |
| Notes                  | nvarchar(max)      | ملاحظات إضافية                                |
| Attachments            | varbinary(max) OR filepath | المرفقات (كملف مرفق أو رابط مسار)     |
| CreatedAt              | datetime           | تاريخ الإدخال                                 |

ملاحظة: يجب وجود جدول منفصل أو نظام حفظ للقيم الفريدة للمادة العلمية ScientificName، لتقديم اقتراحات تلقائية عند الإدخال (auto-suggest/autocomplete).

2. الواجهة (User Interface)
قم بتصميم واجهة لإدخال البيانات تشمل:
- حقل نصي لـ:  
  - اسم المكتب العلمي  
  - اسم المندوب  
  - رقم المندوب  
  - اسم المادة العلمية (مع خاصية auto-suggest من القيم المخزنة مسبقًا)
  - اسم المادة التجارية  
  - السعر  
  - البونص أو الخصم  
  - الملاحظات  
- إمكانية رفع مرفقات (PDF, صورة، Excel...إلخ)
- زر لإضافة العرض إلى قاعدة البيانات
- زر لتفريغ الحقول
- إشعار تأكيد بعد الإضافة

3. البحث والفلاتر (Search & Filtering)
أنشئ واجهة بحث تحتوي على:
- حقل للبحث حسب:
  - اسم المكتب العلمي
  - اسم المندوب
  - اسم المادة العلمية
  - اسم المادة التجارية
- فلاتر حسب:
  - السعر (الأقل أو الأعلى)
  - وجود بونص أو خصم
  - أفضل عرض للمادة العلمية (أقل سعر + أعلى بونص)

4. الاستعلام والتحليل
أضف استعلام متقدم (Advanced Query) لاستخراج أفضل عرض لكل مادة علمية. التعريف المبدئي لأفضل عرض:
- أقل سعر لنفس اسم المادة العلمية
- مع مراعاة وجود خصم أو بونص إن وُجد

5. الملاحظات الخاصة بالتنفيذ
- عند إضافة عرض جديد باسم مادة علمية جديدة، يتم تخزين اسم المادة في جدول منفصل (أو في قائمة فريدة)، ويتم عرضها تلقائيًا في قائمة الاقتراحات عند الإضافة لاحقًا.
- واجهات البرنامج يجب أن تدعم العربية بشكل كامل.
- يجب تأمين عملية رفع المرفقات والتحقق من الصيغ المسموح بها.
- يفضل تصميم الواجهة بأسلوب عصري وتفاعلي (Modern UI).
