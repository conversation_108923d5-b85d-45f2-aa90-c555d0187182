using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace HR_InvoiceArchiver.Models
{
    public class MultiPaymentModel : INotifyPropertyChanged
    {
        private int _id;
        private int _supplierId;
        private string _supplierName = string.Empty;
        private string _receiptNumber = string.Empty;
        private DateTime _paymentDate = DateTime.Now;
        private decimal _totalInvoicesAmount;
        private decimal _discountAmount;
        private decimal _discountPercentage;
        private decimal _finalAmount;
        private string _paymentMethod = "نقدي";
        private string _notes = string.Empty;
        private string _attachmentPath = string.Empty;
        private List<MultiPaymentInvoiceModel> _selectedInvoices = new();

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public int SupplierId
        {
            get => _supplierId;
            set { _supplierId = value; OnPropertyChanged(nameof(SupplierId)); }
        }

        public string SupplierName
        {
            get => _supplierName;
            set { _supplierName = value; OnPropertyChanged(nameof(SupplierName)); }
        }

        public string ReceiptNumber
        {
            get => _receiptNumber;
            set { _receiptNumber = value; OnPropertyChanged(nameof(ReceiptNumber)); }
        }

        public DateTime PaymentDate
        {
            get => _paymentDate;
            set { _paymentDate = value; OnPropertyChanged(nameof(PaymentDate)); }
        }

        public decimal TotalInvoicesAmount
        {
            get => _totalInvoicesAmount;
            set { _totalInvoicesAmount = value; OnPropertyChanged(nameof(TotalInvoicesAmount)); }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set 
            { 
                _discountAmount = value; 
                OnPropertyChanged(nameof(DiscountAmount));
                CalculateFinalAmount();
                CalculateDiscountPercentage();
            }
        }

        public decimal DiscountPercentage
        {
            get => _discountPercentage;
            set 
            { 
                _discountPercentage = value; 
                OnPropertyChanged(nameof(DiscountPercentage));
                CalculateDiscountAmount();
                CalculateFinalAmount();
            }
        }

        public decimal FinalAmount
        {
            get => _finalAmount;
            set { _finalAmount = value; OnPropertyChanged(nameof(FinalAmount)); }
        }

        public string PaymentMethod
        {
            get => _paymentMethod;
            set { _paymentMethod = value; OnPropertyChanged(nameof(PaymentMethod)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        public string AttachmentPath
        {
            get => _attachmentPath;
            set { _attachmentPath = value; OnPropertyChanged(nameof(AttachmentPath)); }
        }

        public List<MultiPaymentInvoiceModel> SelectedInvoices
        {
            get => _selectedInvoices;
            set 
            { 
                _selectedInvoices = value; 
                OnPropertyChanged(nameof(SelectedInvoices));
                CalculateTotalAmount();
            }
        }

        // خصائص محسوبة
        public int InvoicesCount => SelectedInvoices?.Count ?? 0;
        public string PaymentSummary => $"{InvoicesCount} فاتورة - المجموع: {TotalInvoicesAmount:N0} د.ع";
        public string DiscountSummary => $"خصم {DiscountPercentage:F1}% = {DiscountAmount:N0} د.ع";
        public string FinalAmountSummary => $"المبلغ النهائي: {FinalAmount:N0} د.ع";

        // الطرق المساعدة
        private void CalculateTotalAmount()
        {
            TotalInvoicesAmount = SelectedInvoices?.Sum(i => i.InvoiceAmount) ?? 0;
            CalculateFinalAmount();
        }

        private void CalculateDiscountAmount()
        {
            if (TotalInvoicesAmount > 0)
            {
                _discountAmount = TotalInvoicesAmount * (DiscountPercentage / 100);
                OnPropertyChanged(nameof(DiscountAmount));
            }
        }

        private void CalculateDiscountPercentage()
        {
            if (TotalInvoicesAmount > 0)
            {
                _discountPercentage = (DiscountAmount / TotalInvoicesAmount) * 100;
                OnPropertyChanged(nameof(DiscountPercentage));
            }
        }

        private void CalculateFinalAmount()
        {
            FinalAmount = TotalInvoicesAmount - DiscountAmount;
        }

        // حساب الخصم الذكي بناءً على عدد الفواتير والمبلغ
        public void CalculateSmartDiscount()
        {
            if (SelectedInvoices == null || !SelectedInvoices.Any()) return;

            decimal baseDiscountPercentage = 0;

            // خصم بناءً على عدد الفواتير
            if (InvoicesCount >= 10)
                baseDiscountPercentage += 5; // 5% للفواتير 10+
            else if (InvoicesCount >= 5)
                baseDiscountPercentage += 3; // 3% للفواتير 5-9
            else if (InvoicesCount >= 3)
                baseDiscountPercentage += 1; // 1% للفواتير 3-4

            // خصم إضافي بناءً على المبلغ الإجمالي
            if (TotalInvoicesAmount >= 10000000) // 10 مليون
                baseDiscountPercentage += 3;
            else if (TotalInvoicesAmount >= 5000000) // 5 مليون
                baseDiscountPercentage += 2;
            else if (TotalInvoicesAmount >= 1000000) // مليون
                baseDiscountPercentage += 1;

            // تحديد الحد الأقصى للخصم
            DiscountPercentage = Math.Min(baseDiscountPercentage, 15); // حد أقصى 15%
        }

        // طريقة حساب الخصم المطلوبة في الاختبارات
        public decimal CalculateDiscount()
        {
            return TotalInvoicesAmount * (DiscountPercentage / 100);
        }

        // خاصية التحقق من صحة البيانات
        public bool IsValid =>
            !string.IsNullOrEmpty(ReceiptNumber) &&
            SupplierId > 0 &&
            SelectedInvoices != null &&
            SelectedInvoices.Any() &&
            TotalInvoicesAmount > 0;

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class MultiPaymentInvoiceModel : INotifyPropertyChanged
    {
        private int _invoiceId;
        private string _invoiceNumber = string.Empty;
        private DateTime _invoiceDate;
        private decimal _invoiceAmount;
        private decimal _remainingAmount;
        private bool _isSelected;
        private string _supplierName = string.Empty;
        private decimal _paymentAmount;
        private decimal _paidAmount;

        public int InvoiceId
        {
            get => _invoiceId;
            set { _invoiceId = value; OnPropertyChanged(nameof(InvoiceId)); }
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set { _invoiceNumber = value; OnPropertyChanged(nameof(InvoiceNumber)); }
        }

        public DateTime InvoiceDate
        {
            get => _invoiceDate;
            set { _invoiceDate = value; OnPropertyChanged(nameof(InvoiceDate)); }
        }

        public decimal InvoiceAmount
        {
            get => _invoiceAmount;
            set { _invoiceAmount = value; OnPropertyChanged(nameof(InvoiceAmount)); }
        }

        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set { _remainingAmount = value; OnPropertyChanged(nameof(RemainingAmount)); }
        }

        public bool IsSelected
        {
            get => _isSelected;
            set { _isSelected = value; OnPropertyChanged(nameof(IsSelected)); }
        }

        public string SupplierName
        {
            get => _supplierName;
            set { _supplierName = value; OnPropertyChanged(nameof(SupplierName)); }
        }

        public decimal PaymentAmount
        {
            get => _paymentAmount;
            set { _paymentAmount = value; OnPropertyChanged(nameof(PaymentAmount)); }
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set { _paidAmount = value; OnPropertyChanged(nameof(PaidAmount)); }
        }

        // خصائص محسوبة
        public string DisplayText => $"فاتورة #{InvoiceNumber} - {InvoiceDate:yyyy/MM/dd} - {RemainingAmount:N0} د.ع";
        public string StatusText => RemainingAmount < InvoiceAmount ? "مدفوعة جزئياً" : "غير مدفوعة";

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
