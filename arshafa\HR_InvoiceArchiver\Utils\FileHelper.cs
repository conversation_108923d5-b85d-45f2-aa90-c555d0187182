using System.IO;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Utils
{
    public static class FileHelper
    {
        private static readonly string[] AllowedImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff" };
        private static readonly string[] AllowedDocumentExtensions = { ".pdf", ".doc", ".docx", ".txt", ".rtf" };
        private static readonly long MaxFileSize = 10 * 1024 * 1024; // 10 MB

        public static string GetAttachmentsDirectory()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var attachmentsPath = Path.Combine(appDataPath, "HR_InvoiceArchiver", "Attachments");
            
            if (!Directory.Exists(attachmentsPath))
            {
                Directory.CreateDirectory(attachmentsPath);
            }
            
            return attachmentsPath;
        }

        public static string GetInvoiceAttachmentsDirectory()
        {
            var attachmentsPath = GetAttachmentsDirectory();
            var invoicesPath = Path.Combine(attachmentsPath, "Invoices");
            
            if (!Directory.Exists(invoicesPath))
            {
                Directory.CreateDirectory(invoicesPath);
            }
            
            return invoicesPath;
        }

        public static string GetPaymentAttachmentsDirectory()
        {
            var attachmentsPath = GetAttachmentsDirectory();
            var paymentsPath = Path.Combine(attachmentsPath, "Payments");
            
            if (!Directory.Exists(paymentsPath))
            {
                Directory.CreateDirectory(paymentsPath);
            }
            
            return paymentsPath;
        }

        public static string? SelectFile(string title = "اختر ملف", string filter = "جميع الملفات|*.*")
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = title,
                Filter = filter,
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                return openFileDialog.FileName;
            }

            return null;
        }

        public static string? SelectInvoiceFile()
        {
            var filter = "ملفات PDF|*.pdf|ملفات الصور|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff|جميع الملفات|*.*";
            return SelectFile("اختر ملف الفاتورة", filter);
        }

        public static string? SelectPaymentFile()
        {
            var filter = "ملفات PDF|*.pdf|ملفات الصور|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff|جميع الملفات|*.*";
            return SelectFile("اختر ملف الوصل", filter);
        }

        public static async Task<string?> SaveAttachmentAsync(string sourceFilePath, string targetDirectory, string prefix = "")
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    return null;

                var fileInfo = new FileInfo(sourceFilePath);

                // Check file size
                if (fileInfo.Length > MaxFileSize)
                    throw new InvalidOperationException($"حجم الملف كبير جداً. الحد الأقصى المسموح {MaxFileSize / (1024 * 1024)} ميجابايت");

                // Check file extension
                var extension = fileInfo.Extension.ToLower();
                if (!IsAllowedExtension(extension))
                    throw new InvalidOperationException("نوع الملف غير مدعوم");

                // Ensure target directory exists
                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                // Generate unique filename
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"{prefix}{timestamp}_{Guid.NewGuid().ToString("N")[..8]}{extension}";
                var targetPath = Path.Combine(targetDirectory, fileName);

                // Ensure unique filename
                int counter = 1;
                while (File.Exists(targetPath))
                {
                    fileName = $"{prefix}{timestamp}_{Guid.NewGuid().ToString("N")[..8]}_{counter}{extension}";
                    targetPath = Path.Combine(targetDirectory, fileName);
                    counter++;
                }

                // Copy file
                await Task.Run(() => File.Copy(sourceFilePath, targetPath));

                return fileName; // Return only filename, not full path
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في حفظ الملف: {ex.Message}");
            }
        }

        public static async Task<string?> SaveInvoiceAttachmentAsync(string sourceFilePath, string invoiceNumber)
        {
            var targetDirectory = GetInvoiceAttachmentsDirectory();
            var prefix = $"INV_{invoiceNumber}_";
            return await SaveAttachmentAsync(sourceFilePath, targetDirectory, prefix);
        }

        public static async Task<string?> SavePaymentAttachmentAsync(string sourceFilePath, string receiptNumber)
        {
            var targetDirectory = GetPaymentAttachmentsDirectory();
            var prefix = $"PAY_{receiptNumber}_";
            return await SaveAttachmentAsync(sourceFilePath, targetDirectory, prefix);
        }

        public static bool DeleteAttachment(string? filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return false;

                File.Delete(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public static bool FileExists(string? filePath)
        {
            return !string.IsNullOrEmpty(filePath) && File.Exists(filePath);
        }

        public static string GetFullAttachmentPath(string? relativePath, string attachmentType = "Payments")
        {
            if (string.IsNullOrEmpty(relativePath))
                return string.Empty;

            var baseDirectory = attachmentType.ToLower() switch
            {
                "payments" => GetPaymentAttachmentsDirectory(),
                "invoices" => GetInvoiceAttachmentsDirectory(),
                _ => GetAttachmentsDirectory()
            };

            return Path.Combine(baseDirectory, relativePath);
        }

        public static bool AttachmentExists(string? relativePath, string attachmentType = "Payments")
        {
            if (string.IsNullOrEmpty(relativePath))
                return false;

            var fullPath = GetFullAttachmentPath(relativePath, attachmentType);
            return File.Exists(fullPath);
        }

        public static void OpenAttachment(string? relativePath, string attachmentType = "Payments")
        {
            if (string.IsNullOrEmpty(relativePath))
                throw new ArgumentException("مسار المرفق فارغ");

            var fullPath = GetFullAttachmentPath(relativePath, attachmentType);

            if (!File.Exists(fullPath))
                throw new FileNotFoundException("المرفق غير موجود في المسار المحدد");

            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = fullPath,
                UseShellExecute = true
            });
        }

        public static void InitializeAttachmentDirectories()
        {
            try
            {
                GetAttachmentsDirectory();
                GetInvoiceAttachmentsDirectory();
                GetPaymentAttachmentsDirectory();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في إنشاء مجلدات المرفقات: {ex.Message}");
            }
        }

        public static void OpenFile(string? filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    throw new FileNotFoundException("الملف غير موجود");

                var processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                };

                System.Diagnostics.Process.Start(processStartInfo);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في فتح الملف: {ex.Message}");
            }
        }

        public static string GetFileSize(string? filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return "غير محدد";

                var fileInfo = new FileInfo(filePath);
                var sizeInBytes = fileInfo.Length;

                if (sizeInBytes < 1024)
                    return $"{sizeInBytes} بايت";
                else if (sizeInBytes < 1024 * 1024)
                    return $"{sizeInBytes / 1024:F1} كيلوبايت";
                else
                    return $"{sizeInBytes / (1024 * 1024):F1} ميجابايت";
            }
            catch
            {
                return "غير محدد";
            }
        }

        public static string GetFileName(string? filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return "غير محدد";

                return Path.GetFileName(filePath);
            }
            catch
            {
                return "غير محدد";
            }
        }

        public static bool IsImageFile(string? filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLower();
            return AllowedImageExtensions.Contains(extension);
        }

        public static bool IsDocumentFile(string? filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLower();
            return AllowedDocumentExtensions.Contains(extension);
        }

        private static bool IsAllowedExtension(string extension)
        {
            return AllowedImageExtensions.Contains(extension) || AllowedDocumentExtensions.Contains(extension);
        }

        public static string GetExportsDirectory()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var exportsPath = Path.Combine(appDataPath, "HR_InvoiceArchiver", "Exports");
            
            if (!Directory.Exists(exportsPath))
            {
                Directory.CreateDirectory(exportsPath);
            }
            
            return exportsPath;
        }

        public static void OpenDirectory(string directoryPath)
        {
            try
            {
                if (Directory.Exists(directoryPath))
                {
                    System.Diagnostics.Process.Start("explorer.exe", directoryPath);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في فتح المجلد: {ex.Message}");
            }
        }

        public static long GetDirectorySize(string directoryPath)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return 0;

                var directoryInfo = new DirectoryInfo(directoryPath);
                return directoryInfo.GetFiles("*", SearchOption.AllDirectories).Sum(file => file.Length);
            }
            catch
            {
                return 0;
            }
        }

        public static void CleanupOldFiles(string directoryPath, int daysOld = 30)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-daysOld);
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // Ignore errors when deleting individual files
                        }
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }
}
