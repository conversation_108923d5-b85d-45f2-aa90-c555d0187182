using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة التصدير والاستيراد
    /// </summary>
    public interface IImportExportService
    {
        /// <summary>
        /// تصدير الفواتير
        /// </summary>
        Task<ExportResult> ExportInvoicesAsync(ExportOptions options);

        /// <summary>
        /// تصدير الموردين
        /// </summary>
        Task<ExportResult> ExportSuppliersAsync(ExportOptions options);

        /// <summary>
        /// تصدير المدفوعات
        /// </summary>
        Task<ExportResult> ExportPaymentsAsync(ExportOptions options);

        /// <summary>
        /// تصدير جميع البيانات
        /// </summary>
        Task<ExportResult> ExportAllDataAsync(ExportOptions options);

        /// <summary>
        /// استيراد الفواتير
        /// </summary>
        Task<ImportResult> ImportInvoicesAsync(ImportOptions options);

        /// <summary>
        /// استيراد الموردين
        /// </summary>
        Task<ImportResult> ImportSuppliersAsync(ImportOptions options);

        /// <summary>
        /// استيراد المدفوعات
        /// </summary>
        Task<ImportResult> ImportPaymentsAsync(ImportOptions options);

        /// <summary>
        /// استيراد جميع البيانات
        /// </summary>
        Task<ImportResult> ImportAllDataAsync(ImportOptions options);

        /// <summary>
        /// التحقق من صحة ملف الاستيراد
        /// </summary>
        Task<ValidationResult> ValidateImportFileAsync(string filePath, ImportFormat format);

        /// <summary>
        /// الحصول على معاينة البيانات المستوردة
        /// </summary>
        Task<PreviewResult> PreviewImportDataAsync(string filePath, ImportFormat format, int maxRows = 100);

        /// <summary>
        /// إنشاء قالب للاستيراد
        /// </summary>
        Task<string> CreateImportTemplateAsync(DataType dataType, ExportFormat format);

        /// <summary>
        /// الحصول على تقرير التصدير/الاستيراد
        /// </summary>
        Task<List<ImportExportHistory>> GetImportExportHistoryAsync(int days = 30);

        /// <summary>
        /// حذف ملفات التصدير القديمة
        /// </summary>
        Task<int> CleanupOldExportFilesAsync(int daysToKeep = 30);
    }

    /// <summary>
    /// خيارات التصدير
    /// </summary>
    public class ExportOptions
    {
        public ExportFormat Format { get; set; } = ExportFormat.Excel;
        public string FilePath { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<int> SelectedIds { get; set; } = new();
        public bool IncludeAttachments { get; set; } = false;
        public bool CompressOutput { get; set; } = false;
        public string Password { get; set; } = string.Empty;
        public List<string> ColumnsToExport { get; set; } = new();
        public bool ExportAll { get; set; } = true;
    }

    /// <summary>
    /// خيارات الاستيراد
    /// </summary>
    public class ImportOptions
    {
        public string FilePath { get; set; } = string.Empty;
        public ImportFormat Format { get; set; } = ImportFormat.Excel;
        public ImportMode Mode { get; set; } = ImportMode.Insert;
        public bool ValidateData { get; set; } = true;
        public bool SkipDuplicates { get; set; } = true;
        public bool CreateBackup { get; set; } = true;
        public Dictionary<string, string> ColumnMapping { get; set; } = new();
        public int StartRow { get; set; } = 2; // تخطي رأس الجدول
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة التصدير
    /// </summary>
    public class ExportResult
    {
        public bool Success { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public int RecordsExported { get; set; }
        public long FileSizeBytes { get; set; }

        // Alias for compatibility
        public long FileSize => FileSizeBytes;

        public TimeSpan Duration { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Warnings { get; set; } = new();
        public ExportFormat Format { get; set; }
        public DateTime ExportDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نتيجة الاستيراد
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public int RecordsImported { get; set; }
        public int RecordsSkipped { get; set; }
        public int RecordsWithErrors { get; set; }
        public TimeSpan Duration { get; set; }
        public string? ErrorMessage { get; set; }
        public List<ImportError> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string? BackupFilePath { get; set; }
        public DateTime ImportDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نتيجة التحقق من صحة الملف
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public int TotalRows { get; set; }
        public List<string> DetectedColumns { get; set; } = new();
        public ImportFormat DetectedFormat { get; set; }
    }

    /// <summary>
    /// نتيجة معاينة البيانات
    /// </summary>
    public class PreviewResult
    {
        public List<Dictionary<string, object>> SampleData { get; set; } = new();
        public List<string> Columns { get; set; } = new();
        public int TotalRows { get; set; }
        public ValidationResult Validation { get; set; } = new();
    }

    /// <summary>
    /// خطأ في الاستيراد
    /// </summary>
    public class ImportError
    {
        public int RowNumber { get; set; }
        public string ColumnName { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// تاريخ عمليات التصدير والاستيراد
    /// </summary>
    public class ImportExportHistory
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Date { get; set; }
        public OperationType Operation { get; set; }
        public DataType DataType { get; set; }
        public ExportFormat Format { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public int RecordCount { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string UserName { get; set; } = string.Empty;
    }

    /// <summary>
    /// صيغ التصدير
    /// </summary>
    public enum ExportFormat
    {
        Excel = 0,
        CSV = 1,
        JSON = 2,
        XML = 3,
        PDF = 4
    }

    /// <summary>
    /// صيغ الاستيراد
    /// </summary>
    public enum ImportFormat
    {
        Excel = 0,
        CSV = 1,
        JSON = 2,
        XML = 3
    }

    /// <summary>
    /// أنواع البيانات
    /// </summary>
    public enum DataType
    {
        Invoices = 0,
        Suppliers = 1,
        Payments = 2,
        All = 3
    }

    /// <summary>
    /// أنواع العمليات
    /// </summary>
    public enum OperationType
    {
        Export = 0,
        Import = 1
    }

    /// <summary>
    /// أنماط الاستيراد
    /// </summary>
    public enum ImportMode
    {
        Insert = 0,      // إدراج فقط
        Update = 1,      // تحديث فقط
        Upsert = 2       // إدراج أو تحديث
    }
}
