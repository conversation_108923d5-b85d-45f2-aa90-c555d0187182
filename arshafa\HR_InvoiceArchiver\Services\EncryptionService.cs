using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// تطبيق خدمة التشفير
    /// </summary>
    public class EncryptionService : IEncryptionService
    {
        private readonly string _defaultKey;
        private readonly ILoggingService? _loggingService;
        
        // قائمة كلمات المرور الشائعة (مبسطة)
        private readonly string[] _commonPasswords = {
            "password", "123456", "password123", "admin", "qwerty", "letmein",
            "welcome", "monkey", "1234567890", "abc123", "111111", "123123"
        };

        public EncryptionService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
            _defaultKey = GenerateDefaultKey();
        }

        public async Task<string> EncryptAsync(string plainText, string? key = null)
        {
            try
            {
                if (string.IsNullOrEmpty(plainText))
                    return string.Empty;

                var keyToUse = key ?? _defaultKey;
                var keyBytes = Encoding.UTF8.GetBytes(keyToUse.PadRight(32).Substring(0, 32));
                
                using var aes = Aes.Create();
                aes.Key = keyBytes;
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
                using var swEncrypt = new StreamWriter(csEncrypt);
                
                await swEncrypt.WriteAsync(plainText);
                await swEncrypt.FlushAsync();
                await csEncrypt.FlushFinalBlockAsync();

                var iv = aes.IV;
                var encrypted = msEncrypt.ToArray();
                var result = new byte[iv.Length + encrypted.Length];
                
                Buffer.BlockCopy(iv, 0, result, 0, iv.Length);
                Buffer.BlockCopy(encrypted, 0, result, iv.Length, encrypted.Length);

                return Convert.ToBase64String(result);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في تشفير البيانات", ex)!;
                throw;
            }
        }

        public async Task<string> DecryptAsync(string encryptedText, string? key = null)
        {
            try
            {
                if (string.IsNullOrEmpty(encryptedText))
                    return string.Empty;

                var keyToUse = key ?? _defaultKey;
                var keyBytes = Encoding.UTF8.GetBytes(keyToUse.PadRight(32).Substring(0, 32));
                var fullCipher = Convert.FromBase64String(encryptedText);

                using var aes = Aes.Create();
                aes.Key = keyBytes;
                
                var iv = new byte[aes.BlockSize / 8];
                var cipher = new byte[fullCipher.Length - iv.Length];
                
                Buffer.BlockCopy(fullCipher, 0, iv, 0, iv.Length);
                Buffer.BlockCopy(fullCipher, iv.Length, cipher, 0, cipher.Length);
                
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(cipher);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);
                
                return await srDecrypt.ReadToEndAsync();
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في فك تشفير البيانات", ex)!;
                throw;
            }
        }

        public async Task<string> EncryptSensitiveDataAsync(string sensitiveData)
        {
            // استخدام مفتاح أقوى للبيانات الحساسة
            var strongKey = await GenerateKeyAsync(256);
            return await EncryptAsync(sensitiveData, strongKey);
        }

        public async Task<string> DecryptSensitiveDataAsync(string encryptedData)
        {
            // محاولة فك التشفير بالمفتاح الافتراضي أولاً
            try
            {
                return await DecryptAsync(encryptedData);
            }
            catch
            {
                // إذا فشل، قد تحتاج لمفتاح مخصص
                throw new UnauthorizedAccessException("فشل في فك تشفير البيانات الحساسة");
            }
        }

        public async Task<string> CreateHashAsync(string data, string? salt = null)
        {
            try
            {
                var saltToUse = salt ?? await GenerateSaltAsync();
                var saltBytes = Encoding.UTF8.GetBytes(saltToUse);
                var dataBytes = Encoding.UTF8.GetBytes(data);

                using var pbkdf2 = new Rfc2898DeriveBytes(dataBytes, saltBytes, 10000, HashAlgorithmName.SHA256);
                var hash = pbkdf2.GetBytes(32);
                
                var hashBytes = new byte[saltBytes.Length + hash.Length];
                Buffer.BlockCopy(saltBytes, 0, hashBytes, 0, saltBytes.Length);
                Buffer.BlockCopy(hash, 0, hashBytes, saltBytes.Length, hash.Length);

                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في إنشاء hash", ex)!;
                throw;
            }
        }

        public async Task<bool> VerifyHashAsync(string data, string hash, string? salt = null)
        {
            try
            {
                var hashBytes = Convert.FromBase64String(hash);
                var saltBytes = new byte[16]; // افتراض حجم salt = 16 بايت
                var storedHash = new byte[hashBytes.Length - saltBytes.Length];
                
                Buffer.BlockCopy(hashBytes, 0, saltBytes, 0, saltBytes.Length);
                Buffer.BlockCopy(hashBytes, saltBytes.Length, storedHash, 0, storedHash.Length);

                var dataBytes = Encoding.UTF8.GetBytes(data);
                using var pbkdf2 = new Rfc2898DeriveBytes(dataBytes, saltBytes, 10000, HashAlgorithmName.SHA256);
                var computedHash = pbkdf2.GetBytes(32);

                return storedHash.SequenceEqual(computedHash);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في التحقق من hash", ex)!;
                return false;
            }
        }

        public async Task<string> GenerateKeyAsync(int keySize = 256)
        {
            try
            {
                var keyBytes = new byte[keySize / 8];
                using var rng = RandomNumberGenerator.Create();
                rng.GetBytes(keyBytes);
                return Convert.ToBase64String(keyBytes);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في إنشاء مفتاح التشفير", ex)!;
                throw;
            }
        }

        public async Task<string> GenerateSaltAsync(int saltSize = 128)
        {
            try
            {
                var saltBytes = new byte[saltSize / 8];
                using var rng = RandomNumberGenerator.Create();
                rng.GetBytes(saltBytes);
                return Convert.ToBase64String(saltBytes);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في إنشاء salt", ex)!;
                throw;
            }
        }

        public async Task<bool> EncryptFileAsync(string inputFilePath, string outputFilePath, string? key = null)
        {
            try
            {
                if (!File.Exists(inputFilePath))
                    return false;

                var keyToUse = key ?? _defaultKey;
                var keyBytes = Encoding.UTF8.GetBytes(keyToUse.PadRight(32).Substring(0, 32));

                using var aes = Aes.Create();
                aes.Key = keyBytes;
                aes.GenerateIV();

                using var inputFile = File.OpenRead(inputFilePath);
                using var outputFile = File.Create(outputFilePath);
                
                // كتابة IV في بداية الملف
                await outputFile.WriteAsync(aes.IV, 0, aes.IV.Length);

                using var encryptor = aes.CreateEncryptor();
                using var cryptoStream = new CryptoStream(outputFile, encryptor, CryptoStreamMode.Write);
                
                await inputFile.CopyToAsync(cryptoStream);
                await cryptoStream.FlushFinalBlockAsync();

                await _loggingService?.LogInformationAsync($"تم تشفير الملف: {inputFilePath}")!;
                return true;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync($"فشل في تشفير الملف: {inputFilePath}", ex)!;
                return false;
            }
        }

        public async Task<bool> DecryptFileAsync(string inputFilePath, string outputFilePath, string? key = null)
        {
            try
            {
                if (!File.Exists(inputFilePath))
                    return false;

                var keyToUse = key ?? _defaultKey;
                var keyBytes = Encoding.UTF8.GetBytes(keyToUse.PadRight(32).Substring(0, 32));

                using var aes = Aes.Create();
                aes.Key = keyBytes;

                using var inputFile = File.OpenRead(inputFilePath);
                
                // قراءة IV من بداية الملف
                var iv = new byte[aes.BlockSize / 8];
                await inputFile.ReadAsync(iv, 0, iv.Length);
                aes.IV = iv;

                using var outputFile = File.Create(outputFilePath);
                using var decryptor = aes.CreateDecryptor();
                using var cryptoStream = new CryptoStream(inputFile, decryptor, CryptoStreamMode.Read);
                
                await cryptoStream.CopyToAsync(outputFile);

                await _loggingService?.LogInformationAsync($"تم فك تشفير الملف: {inputFilePath}")!;
                return true;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync($"فشل في فك تشفير الملف: {inputFilePath}", ex)!;
                return false;
            }
        }

        public async Task<PasswordStrength> CheckPasswordStrengthAsync(string password)
        {
            try
            {
                var info = await GetPasswordStrengthInfoAsync(password);
                return info.Strength;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في فحص قوة كلمة المرور", ex)!;
                return PasswordStrength.VeryWeak;
            }
        }

        public async Task<string> GenerateStrongPasswordAsync(int length = 12, bool includeSymbols = true)
        {
            try
            {
                const string lowercase = "abcdefghijklmnopqrstuvwxyz";
                const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
                const string numbers = "0123456789";
                const string symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";

                var chars = lowercase + uppercase + numbers;
                if (includeSymbols)
                    chars += symbols;

                var password = new StringBuilder();
                using var rng = RandomNumberGenerator.Create();

                // ضمان وجود حرف من كل نوع
                password.Append(GetRandomChar(lowercase, rng));
                password.Append(GetRandomChar(uppercase, rng));
                password.Append(GetRandomChar(numbers, rng));
                if (includeSymbols)
                    password.Append(GetRandomChar(symbols, rng));

                // إكمال باقي الطول
                var remainingLength = length - password.Length;
                for (int i = 0; i < remainingLength; i++)
                {
                    password.Append(GetRandomChar(chars, rng));
                }

                // خلط الأحرف
                return ShuffleString(password.ToString(), rng);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في إنشاء كلمة مرور قوية", ex)!;
                throw;
            }
        }

        private async Task<PasswordStrengthInfo> GetPasswordStrengthInfoAsync(string password)
        {
            return await Task.Run(() =>
            {
                var info = new PasswordStrengthInfo();
                var score = 0;
                var suggestions = new List<string>();

                // فحص الطول
                info.HasMinimumLength = password.Length >= 8;
                if (info.HasMinimumLength)
                    score += 1;
                else
                    suggestions.Add("استخدم على الأقل 8 أحرف");

                // فحص الأحرف الكبيرة
                info.HasUpperCase = password.Any(char.IsUpper);
                if (info.HasUpperCase)
                    score += 1;
                else
                    suggestions.Add("أضف أحرف كبيرة");

                // فحص الأحرف الصغيرة
                info.HasLowerCase = password.Any(char.IsLower);
                if (info.HasLowerCase)
                    score += 1;
                else
                    suggestions.Add("أضف أحرف صغيرة");

                // فحص الأرقام
                info.HasNumbers = password.Any(char.IsDigit);
                if (info.HasNumbers)
                    score += 1;
                else
                    suggestions.Add("أضف أرقام");

                // فحص الرموز
                info.HasSymbols = password.Any(c => !char.IsLetterOrDigit(c));
                if (info.HasSymbols)
                    score += 1;
                else
                    suggestions.Add("أضف رموز خاصة");

                // فحص كلمات المرور الشائعة
                info.IsCommonPassword = _commonPasswords.Contains(password.ToLower());
                if (info.IsCommonPassword)
                {
                    score -= 2;
                    suggestions.Add("تجنب كلمات المرور الشائعة");
                }

                // فحص التكرار
                if (HasRepeatingPatterns(password))
                {
                    score -= 1;
                    suggestions.Add("تجنب الأنماط المتكررة");
                }

                info.Score = Math.Max(0, score);
                info.Suggestions = suggestions.ToArray();

                // تحديد مستوى القوة
                info.Strength = info.Score switch
                {
                    0 or 1 => PasswordStrength.VeryWeak,
                    2 => PasswordStrength.Weak,
                    3 => PasswordStrength.Medium,
                    4 => PasswordStrength.Strong,
                    _ => PasswordStrength.VeryStrong
                };

                return info;
            });
        }

        private string GenerateDefaultKey()
        {
            // مفتاح افتراضي ثابت للتطبيق (يجب تغييره في الإنتاج)
            return "HR_InvoiceArchiver_DefaultKey_2024";
        }

        private char GetRandomChar(string chars, RandomNumberGenerator rng)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var randomIndex = Math.Abs(BitConverter.ToInt32(bytes, 0)) % chars.Length;
            return chars[randomIndex];
        }

        private string ShuffleString(string input, RandomNumberGenerator rng)
        {
            var array = input.ToCharArray();
            for (int i = array.Length - 1; i > 0; i--)
            {
                var bytes = new byte[4];
                rng.GetBytes(bytes);
                var j = Math.Abs(BitConverter.ToInt32(bytes, 0)) % (i + 1);
                (array[i], array[j]) = (array[j], array[i]);
            }
            return new string(array);
        }

        private bool HasRepeatingPatterns(string password)
        {
            // فحص بسيط للأنماط المتكررة
            for (int i = 0; i < password.Length - 2; i++)
            {
                var pattern = password.Substring(i, 3);
                if (password.IndexOf(pattern, i + 1) != -1)
                    return true;
            }
            return false;
        }
    }
}
