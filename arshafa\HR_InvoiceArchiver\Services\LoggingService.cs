using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// تطبيق خدمة السجلات المتقدمة
    /// </summary>
    public class LoggingService : ILoggingService, IDisposable
    {
        private readonly string _logDirectory;
        private readonly string _logFileName;
        private readonly SemaphoreSlim _writeSemaphore;
        private readonly List<LogEntry> _logBuffer;
        private readonly Timer _flushTimer;
        private readonly int _bufferSize;
        private readonly TimeSpan _flushInterval;
        private bool _disposed = false;

        public LoggingService(string? logDirectory = null)
        {
            _logDirectory = logDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            _logFileName = $"app-{DateTime.Now:yyyy-MM-dd}.log";
            _writeSemaphore = new SemaphoreSlim(1, 1);
            _logBuffer = new List<LogEntry>();
            _bufferSize = 100;
            _flushInterval = TimeSpan.FromSeconds(30);

            // إنشاء مجلد السجلات إذا لم يكن موجوداً
            Directory.CreateDirectory(_logDirectory);

            // تشغيل مؤقت التفريغ
            _flushTimer = new Timer(FlushBufferCallback, null, _flushInterval, _flushInterval);
        }

        public async Task LogAsync(LogLevel level, LogCategory category, string message, 
            Exception? exception = null, object? additionalData = null)
        {
            var entry = new LogEntry
            {
                Level = level,
                Category = category,
                Message = message,
                Exception = exception,
                Details = additionalData?.ToString()
            };

            // إضافة معلومات المصدر
            var stackTrace = new StackTrace(true);
            var frame = stackTrace.GetFrame(2); // تخطي الإطارات الحالية
            if (frame != null)
            {
                entry.SourceMethod = frame.GetMethod()?.Name;
                entry.SourceFile = Path.GetFileName(frame.GetFileName());
                entry.SourceLine = frame.GetFileLineNumber();
            }

            await WriteLogEntryAsync(entry);
        }

        public async Task LogInformationAsync(string message, LogCategory category = LogCategory.General, 
            object? additionalData = null)
        {
            await LogAsync(LogLevel.Information, category, message, null, additionalData);
        }

        public async Task LogWarningAsync(string message, LogCategory category = LogCategory.General, 
            object? additionalData = null)
        {
            await LogAsync(LogLevel.Warning, category, message, null, additionalData);
        }

        public async Task LogErrorAsync(string message, Exception? exception = null, 
            LogCategory category = LogCategory.General, object? additionalData = null)
        {
            await LogAsync(LogLevel.Error, category, message, exception, additionalData);
        }

        public async Task LogCriticalAsync(string message, Exception? exception = null, 
            LogCategory category = LogCategory.General, object? additionalData = null)
        {
            await LogAsync(LogLevel.Critical, category, message, exception, additionalData);
        }

        public async Task LogPerformanceAsync(string operation, TimeSpan duration, 
            object? additionalData = null)
        {
            var performanceData = new
            {
                Operation = operation,
                DurationMs = duration.TotalMilliseconds,
                AdditionalData = additionalData
            };

            await LogAsync(LogLevel.Information, LogCategory.Performance, 
                $"عملية {operation} استغرقت {duration.TotalMilliseconds:F2} مللي ثانية", 
                null, performanceData);
        }

        public async Task LogSecurityEventAsync(string eventType, string description, 
            string? userId = null, object? additionalData = null)
        {
            var securityData = new
            {
                EventType = eventType,
                UserId = userId,
                AdditionalData = additionalData
            };

            await LogAsync(LogLevel.Warning, LogCategory.Security, 
                $"حدث أمني: {eventType} - {description}", null, securityData);
        }

        public async Task LogDatabaseOperationAsync(string operation, string tableName, 
            bool success, TimeSpan? duration = null, object? additionalData = null)
        {
            var dbData = new
            {
                Operation = operation,
                TableName = tableName,
                Success = success,
                DurationMs = duration?.TotalMilliseconds,
                AdditionalData = additionalData
            };

            var level = success ? LogLevel.Information : LogLevel.Error;
            var message = $"عملية قاعدة البيانات: {operation} على الجدول {tableName} - " +
                         $"{(success ? "نجحت" : "فشلت")}";

            if (duration.HasValue)
            {
                message += $" في {duration.Value.TotalMilliseconds:F2} مللي ثانية";
            }

            await LogAsync(level, LogCategory.Database, message, null, dbData);
        }

        public async Task<LogSearchResult> SearchLogsAsync(LogSearchCriteria criteria)
        {
            var allLogs = await ReadAllLogsAsync();
            
            var filteredLogs = allLogs.Where(log =>
            {
                if (criteria.FromDate.HasValue && log.Timestamp < criteria.FromDate.Value)
                    return false;
                
                if (criteria.ToDate.HasValue && log.Timestamp > criteria.ToDate.Value)
                    return false;
                
                if (criteria.MinLevel.HasValue && log.Level < criteria.MinLevel.Value)
                    return false;
                
                if (criteria.MaxLevel.HasValue && log.Level > criteria.MaxLevel.Value)
                    return false;
                
                if (criteria.Category.HasValue && log.Category != criteria.Category.Value)
                    return false;
                
                if (!string.IsNullOrEmpty(criteria.SearchText) && 
                    !log.Message.Contains(criteria.SearchText, StringComparison.OrdinalIgnoreCase))
                    return false;
                
                if (!string.IsNullOrEmpty(criteria.UserId) && log.UserId != criteria.UserId)
                    return false;
                
                return true;
            }).OrderByDescending(log => log.Timestamp);

            var totalCount = filteredLogs.Count();
            var pagedLogs = filteredLogs
                .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToList();

            return new LogSearchResult
            {
                Entries = pagedLogs,
                TotalCount = totalCount,
                PageNumber = criteria.PageNumber,
                PageSize = criteria.PageSize
            };
        }

        public async Task<Dictionary<LogLevel, int>> GetLogStatisticsAsync(DateTime? fromDate = null, 
            DateTime? toDate = null)
        {
            var allLogs = await ReadAllLogsAsync();
            
            var filteredLogs = allLogs.Where(log =>
            {
                if (fromDate.HasValue && log.Timestamp < fromDate.Value)
                    return false;
                
                if (toDate.HasValue && log.Timestamp > toDate.Value)
                    return false;
                
                return true;
            });

            return filteredLogs
                .GroupBy(log => log.Level)
                .ToDictionary(group => group.Key, group => group.Count());
        }

        public async Task CleanupOldLogsAsync(TimeSpan maxAge)
        {
            var cutoffDate = DateTime.Now - maxAge;
            var logFiles = Directory.GetFiles(_logDirectory, "*.log");

            foreach (var logFile in logFiles)
            {
                var fileInfo = new FileInfo(logFile);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    try
                    {
                        File.Delete(logFile);
                        await LogInformationAsync($"تم حذف ملف السجل القديم: {Path.GetFileName(logFile)}");
                    }
                    catch (Exception ex)
                    {
                        await LogErrorAsync($"فشل في حذف ملف السجل: {Path.GetFileName(logFile)}", ex);
                    }
                }
            }
        }

        public async Task<string> ExportLogsAsync(LogSearchCriteria criteria, string format = "csv")
        {
            var searchResult = await SearchLogsAsync(criteria);
            var exportPath = Path.Combine(_logDirectory, $"export-{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.{format}");

            if (format.ToLower() == "csv")
            {
                await ExportToCsvAsync(searchResult.Entries, exportPath);
            }
            else if (format.ToLower() == "json")
            {
                await ExportToJsonAsync(searchResult.Entries, exportPath);
            }
            else
            {
                throw new ArgumentException($"تنسيق التصدير غير مدعوم: {format}");
            }

            return exportPath;
        }

        public async Task<List<LogEntry>> GetRecentLogsAsync(int count = 100, LogLevel? minLevel = null)
        {
            var allLogs = await ReadAllLogsAsync();
            
            var filteredLogs = allLogs.Where(log =>
            {
                if (minLevel.HasValue && log.Level < minLevel.Value)
                    return false;
                
                return true;
            });

            return filteredLogs
                .OrderByDescending(log => log.Timestamp)
                .Take(count)
                .ToList();
        }

        public IDisposable BeginScope(string operationName, LogCategory category = LogCategory.General)
        {
            return new LogScope(this, operationName, category);
        }

        private async Task WriteLogEntryAsync(LogEntry entry)
        {
            await _writeSemaphore.WaitAsync();
            try
            {
                _logBuffer.Add(entry);
                
                // تفريغ فوري للأخطاء الحرجة
                if (entry.Level >= LogLevel.Error || _logBuffer.Count >= _bufferSize)
                {
                    await FlushBufferAsync();
                }
            }
            finally
            {
                _writeSemaphore.Release();
            }
        }

        private async Task FlushBufferAsync()
        {
            if (_logBuffer.Count == 0) return;

            var logFilePath = Path.Combine(_logDirectory, _logFileName);
            var logEntries = _logBuffer.ToList();
            _logBuffer.Clear();

            try
            {
                using var writer = new StreamWriter(logFilePath, append: true);
                foreach (var entry in logEntries)
                {
                    var logLine = FormatLogEntry(entry);
                    await writer.WriteLineAsync(logLine);
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل الكتابة، نعيد الإدخالات إلى المخزن المؤقت
                _logBuffer.InsertRange(0, logEntries);
                Console.WriteLine($"فشل في كتابة السجلات: {ex.Message}");
            }
        }

        private void FlushBufferCallback(object? state)
        {
            _ = Task.Run(async () =>
            {
                await _writeSemaphore.WaitAsync();
                try
                {
                    await FlushBufferAsync();
                }
                finally
                {
                    _writeSemaphore.Release();
                }
            });
        }

        private string FormatLogEntry(LogEntry entry)
        {
            var logData = new
            {
                entry.Timestamp,
                Level = entry.Level.ToString(),
                Category = entry.Category.ToString(),
                entry.Message,
                entry.Details,
                Exception = entry.Exception?.ToString(),
                entry.UserId,
                entry.SessionId,
                entry.SourceMethod,
                entry.SourceFile,
                entry.SourceLine,
                entry.Properties
            };

            return JsonSerializer.Serialize(logData, new JsonSerializerOptions 
            { 
                WriteIndented = false,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }

        private async Task<List<LogEntry>> ReadAllLogsAsync()
        {
            var allLogs = new List<LogEntry>();
            var logFiles = Directory.GetFiles(_logDirectory, "*.log");

            foreach (var logFile in logFiles)
            {
                try
                {
                    var lines = await File.ReadAllLinesAsync(logFile);
                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line)) continue;
                        
                        try
                        {
                            var logEntry = JsonSerializer.Deserialize<LogEntry>(line);
                            if (logEntry != null)
                            {
                                allLogs.Add(logEntry);
                            }
                        }
                        catch
                        {
                            // تجاهل الأسطر التي لا يمكن تحليلها
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"فشل في قراءة ملف السجل {logFile}: {ex.Message}");
                }
            }

            return allLogs;
        }

        private async Task ExportToCsvAsync(List<LogEntry> logs, string filePath)
        {
            using var writer = new StreamWriter(filePath);
            
            // كتابة العناوين
            await writer.WriteLineAsync("Timestamp,Level,Category,Message,Details,Exception,UserId,SourceMethod,SourceFile,SourceLine");
            
            // كتابة البيانات
            foreach (var log in logs)
            {
                var line = $"{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.Level},{log.Category}," +
                          $"\"{log.Message}\",\"{log.Details}\",\"{log.Exception?.Message}\"," +
                          $"{log.UserId},{log.SourceMethod},{log.SourceFile},{log.SourceLine}";
                await writer.WriteLineAsync(line);
            }
        }

        private async Task ExportToJsonAsync(List<LogEntry> logs, string filePath)
        {
            var json = JsonSerializer.Serialize(logs, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            
            await File.WriteAllTextAsync(filePath, json);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _flushTimer?.Dispose();
                
                // تفريغ أخير للمخزن المؤقت
                _writeSemaphore.Wait();
                try
                {
                    FlushBufferAsync().Wait();
                }
                finally
                {
                    _writeSemaphore.Release();
                }
                
                _writeSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// نطاق العملية للسجلات
    /// </summary>
    internal class LogScope : ILogScope
    {
        private readonly ILoggingService _loggingService;
        private readonly string _operationName;
        private readonly LogCategory _category;
        private readonly DateTime _startTime;
        private readonly Dictionary<string, object> _properties;
        private bool _disposed = false;
        private bool _success = true;
        private string? _resultMessage;

        public LogScope(ILoggingService loggingService, string operationName, LogCategory category)
        {
            _loggingService = loggingService;
            _operationName = operationName;
            _category = category;
            _startTime = DateTime.Now;
            _properties = new Dictionary<string, object>();

            // تسجيل بداية العملية
            _ = Task.Run(() => _loggingService.LogInformationAsync($"بدء العملية: {operationName}", category));
        }

        public void AddProperty(string key, object value)
        {
            _properties[key] = value;
        }

        public async Task LogAsync(LogLevel level, string message, Exception? exception = null)
        {
            await _loggingService.LogAsync(level, _category, $"[{_operationName}] {message}", exception, _properties);
        }

        public void SetResult(bool success, string? message = null)
        {
            _success = success;
            _resultMessage = message;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                var duration = DateTime.Now - _startTime;
                var resultText = _success ? "نجحت" : "فشلت";
                var message = $"انتهاء العملية: {_operationName} - {resultText} في {duration.TotalMilliseconds:F2} مللي ثانية";
                
                if (!string.IsNullOrEmpty(_resultMessage))
                {
                    message += $" - {_resultMessage}";
                }

                var level = _success ? LogLevel.Information : LogLevel.Error;
                
                _ = Task.Run(() => _loggingService.LogAsync(level, _category, message, null, new
                {
                    OperationName = _operationName,
                    Success = _success,
                    DurationMs = duration.TotalMilliseconds,
                    Properties = _properties
                }));

                _disposed = true;
            }
        }
    }
}
