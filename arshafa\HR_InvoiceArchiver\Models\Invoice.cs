using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HR_InvoiceArchiver.Models
{
    public enum InvoiceStatus
    {
        Unpaid = 0,
        PartiallyPaid = 1,
        Paid = 2,
        PaidWithDiscount = 3,
        Pending = 4
    }

    public class Invoice
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public int SupplierId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        // Alias for Amount to maintain compatibility
        [NotMapped]
        public decimal TotalAmount => Amount;

        // Calculated PaidAmount from Payments
        [NotMapped]
        public decimal CalculatedPaidAmount
        {
            get
            {
                if (Payments == null || !Payments.Any()) return 0;
                return Payments.Where(p => p.IsActive).Sum(p => p.Amount);
            }
        }

        // Calculated DiscountAmount from Payments
        [NotMapped]
        public decimal TotalDiscountAmount
        {
            get
            {
                if (Payments == null || !Payments.Any()) return 0;
                return Payments.Where(p => p.IsActive).Sum(p => p.DiscountAmount);
            }
        }

        // Calculated RefundValue from Payments
        [NotMapped]
        public decimal TotalRefundValue
        {
            get
            {
                if (Payments == null || !Payments.Any()) return 0;
                return Payments.Where(p => p.IsActive).Sum(p => p.RefundValue);
            }
        }

        [Required]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        public DateTime? DueDate { get; set; }

        [Required]
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Unpaid;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(500)]
        public string? AttachmentPath { get; set; }

        /// <summary>
        /// معرف الملف في التخزين السحابي
        /// </summary>
        [StringLength(200)]
        public string? CloudFileId { get; set; }

        /// <summary>
        /// حالة المزامنة مع السحابة
        /// </summary>
        public CloudSyncStatus SyncStatus { get; set; } = CloudSyncStatus.Pending;

        /// <summary>
        /// تاريخ آخر مزامنة
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;

        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        // Calculated Properties
        [NotMapped]
        public decimal RemainingAmount => Amount - CalculatedPaidAmount - TotalDiscountAmount - TotalRefundValue;

        [NotMapped]
        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Now && Status != InvoiceStatus.Paid && Status != InvoiceStatus.PaidWithDiscount;

        [NotMapped]
        public int DaysOverdue => IsOverdue ? (DateTime.Now - DueDate!.Value).Days : 0;

        [NotMapped]
        public string SupplierName => Supplier?.Name ?? "غير محدد";

        [NotMapped]
        public string StatusText => Status switch
        {
            InvoiceStatus.Unpaid => "غير مسددة",
            InvoiceStatus.PartiallyPaid => "تسديد جزئي",
            InvoiceStatus.Paid => "مسددة",
            InvoiceStatus.PaidWithDiscount => "مسددة وبخصم",
            _ => "غير محدد"
        };

        [NotMapped]
        public string StatusColor => Status switch
        {
            InvoiceStatus.Unpaid => "#FF6B6B",
            InvoiceStatus.PartiallyPaid => "#FFD93D",
            InvoiceStatus.Paid => "#6BCF7F",
            InvoiceStatus.PaidWithDiscount => "#9C27B0",
            _ => "#95A5A6"
        };

        [NotMapped]
        public string DisplayText => $"فاتورة #{InvoiceNumber} - {Supplier?.Name ?? "غير محدد"} - {RemainingAmount:N0} د.ع";

        // Methods
        public void UpdateStatus()
        {
            var currentPaidAmount = CalculatedPaidAmount;
            var currentDiscountAmount = TotalDiscountAmount;
            var currentRefundValue = TotalRefundValue;
            var totalSettlement = currentPaidAmount + currentDiscountAmount + currentRefundValue;

            if (currentPaidAmount <= 0 && currentDiscountAmount <= 0 && currentRefundValue <= 0)
                Status = InvoiceStatus.Unpaid;
            else if (totalSettlement >= Amount)
            {
                // إذا كان هناك خصم، فالحالة "مسددة وبخصم"، وإلا "مسددة"
                Status = currentDiscountAmount > 0 ? InvoiceStatus.PaidWithDiscount : InvoiceStatus.Paid;
            }
            else
                Status = InvoiceStatus.PartiallyPaid;

            // Update the stored PaidAmount
            PaidAmount = currentPaidAmount;
        }

        public bool CanAddPayment()
        {
            return Status != InvoiceStatus.Paid && Status != InvoiceStatus.PaidWithDiscount && RemainingAmount > 0;
        }
    }
}
