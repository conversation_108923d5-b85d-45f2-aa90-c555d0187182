<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="HR_InvoiceArchiver.Controls.SupplierStatementControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">

    <UserControl.Resources>
        <!-- Enhanced Slide In Animation -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="400" To="0" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.4"/>
            <DoubleAnimation Storyboard.TargetName="BackgroundOverlay" 
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="0.7" Duration="0:0:0.3"/>
        </Storyboard>
        
        <!-- Enhanced Slide Out Animation -->
        <Storyboard x:Key="SlideOutAnimation" Completed="SlideOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="0" To="400" Duration="0:0:0.4">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
            <DoubleAnimation Storyboard.TargetName="BackgroundOverlay" 
                           Storyboard.TargetProperty="Opacity"
                           From="0.7" To="0" Duration="0:0:0.3"/>
        </Storyboard>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Stat Card Style -->
        <Style x:Key="StatCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </UserControl.Resources>

    <Grid x:Name="RootGrid" Background="Transparent" MouseLeftButtonDown="RootGrid_MouseLeftButtonDown">
        <!-- Background Overlay -->
        <Rectangle x:Name="BackgroundOverlay" 
                   Fill="Black" 
                   Opacity="0" 
                   IsHitTestVisible="True"/>

        <!-- Main Card Container -->
        <materialDesign:Card x:Name="MainCard"
                           Width="1200"
                           Height="800"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           materialDesign:ElevationAssist.Elevation="Dp16"
                           Background="White"
                           Opacity="0">
            
            <materialDesign:Card.RenderTransform>
                <TransformGroup>
                    <TranslateTransform X="400" Y="0"/>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                </TransformGroup>
            </materialDesign:Card.RenderTransform>

            <Grid Margin="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header Section -->
                <Border Grid.Row="0"
                        CornerRadius="20,20,0,0"
                        Padding="30,20">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#667eea" Offset="0"/>
                            <GradientStop Color="#764ba2" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                            <TextBlock Text="كشف حساب المورد" 
                                     FontSize="24" 
                                     FontWeight="Bold" 
                                     Foreground="White"/>
                            <TextBlock x:Name="SupplierNameText" 
                                     Text="اسم المورد" 
                                     FontSize="16" 
                                     Foreground="#E0FFFFFF" 
                                     Margin="0,5,0,0"/>
                        </StackPanel>

                        <Button Grid.Column="1" 
                                x:Name="CloseButton"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Width="40" Height="40"
                                Foreground="White"
                                Click="CloseButton_Click"
                                ToolTip="إغلاق">
                            <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                        </Button>
                    </Grid>
                </Border>

                <!-- Statistics Cards -->
                <UniformGrid Grid.Row="1" Columns="4" Margin="20,20,20,10">
                    <!-- Total Invoices Card -->
                    <materialDesign:Card Style="{StaticResource StatCardStyle}">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#667eea" Offset="0"/>
                                <GradientStop Color="#764ba2" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <StackPanel>
                            <materialDesign:PackIcon Kind="FileDocument" 
                                                   Width="32" Height="32" 
                                                   Foreground="White" 
                                                   HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalInvoicesText" 
                                     Text="0" 
                                     FontSize="24" 
                                     FontWeight="Bold" 
                                     Foreground="White" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,8,0,4"/>
                            <TextBlock Text="إجمالي الفواتير" 
                                     FontSize="12" 
                                     Foreground="#CCFFFFFF" 
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Total Amount Card -->
                    <materialDesign:Card Style="{StaticResource StatCardStyle}">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#11998e" Offset="0"/>
                                <GradientStop Color="#38ef7d" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <StackPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="32" Height="32" 
                                                   Foreground="White" 
                                                   HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalAmountText" 
                                     Text="0 د.ع" 
                                     FontSize="20" 
                                     FontWeight="Bold" 
                                     Foreground="White" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,8,0,4"/>
                            <TextBlock Text="إجمالي المبلغ" 
                                     FontSize="12" 
                                     Foreground="#CCFFFFFF" 
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Paid Amount Card -->
                    <materialDesign:Card Style="{StaticResource StatCardStyle}">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#ffecd2" Offset="0"/>
                                <GradientStop Color="#fcb69f" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <StackPanel>
                            <materialDesign:PackIcon Kind="CashCheck" 
                                                   Width="32" Height="32" 
                                                   Foreground="White" 
                                                   HorizontalAlignment="Center"/>
                            <TextBlock x:Name="PaidAmountText" 
                                     Text="0 د.ع" 
                                     FontSize="20" 
                                     FontWeight="Bold" 
                                     Foreground="White" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,8,0,4"/>
                            <TextBlock Text="المبلغ المسدد" 
                                     FontSize="12" 
                                     Foreground="#CCFFFFFF" 
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Remaining Amount Card -->
                    <materialDesign:Card Style="{StaticResource StatCardStyle}">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#ff9a9e" Offset="0"/>
                                <GradientStop Color="#fecfef" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <StackPanel>
                            <materialDesign:PackIcon Kind="CashMinus" 
                                                   Width="32" Height="32" 
                                                   Foreground="White" 
                                                   HorizontalAlignment="Center"/>
                            <TextBlock x:Name="RemainingAmountText" 
                                     Text="0 د.ع" 
                                     FontSize="20" 
                                     FontWeight="Bold" 
                                     Foreground="White" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,8,0,4"/>
                            <TextBlock Text="المبلغ المتبقي" 
                                     FontSize="12" 
                                     Foreground="#CCFFFFFF" 
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>
                </UniformGrid>

                <!-- Main Content Area -->
                <materialDesign:Card Grid.Row="2" 
                                   Style="{StaticResource ModernCardStyle}" 
                                   Margin="20,10,20,20">
                    <TabControl Background="Transparent" 
                              BorderThickness="0"
                              materialDesign:ColorZoneAssist.Mode="PrimaryMid">
                        
                        <!-- Invoices Tab -->
                        <TabItem Header="الفواتير" FontSize="14" FontWeight="SemiBold">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Invoices Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                    <materialDesign:PackIcon Kind="FileDocument" 
                                                           Width="20" Height="20" 
                                                           Foreground="#2C3E50" 
                                                           VerticalAlignment="Center" 
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="فواتير المورد" 
                                             Style="{StaticResource HeaderTextStyle}" 
                                             Margin="0,0,20,0"/>
                                    <TextBlock x:Name="InvoiceCountText" 
                                             Text="0 فاتورة" 
                                             VerticalAlignment="Center" 
                                             Foreground="#7F8C8D"/>
                                </StackPanel>

                                <!-- Invoices DataGrid -->
                                <DataGrid x:Name="InvoicesDataGrid" 
                                        Grid.Row="1"
                                        AutoGenerateColumns="False"
                                        CanUserAddRows="False"
                                        CanUserDeleteRows="False"
                                        IsReadOnly="True"
                                        materialDesign:DataGridAssist.CellPadding="8"
                                        materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                        GridLinesVisibility="Horizontal"
                                        HeadersVisibility="Column">
                                    
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="رقم الفاتورة" 
                                                          Binding="{Binding InvoiceNumber}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="التاريخ" 
                                                          Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" 
                                                          Width="100"/>
                                        <DataGridTextColumn Header="المبلغ" 
                                                          Binding="{Binding Amount, StringFormat=N0}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="المسدد" 
                                                          Binding="{Binding PaidAmount, StringFormat=N0}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="المتبقي" 
                                                          Binding="{Binding RemainingAmount, StringFormat=N0}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="الحالة" 
                                                          Binding="{Binding StatusText}" 
                                                          Width="100"/>
                                        <DataGridTextColumn Header="الوصف" 
                                                          Binding="{Binding Description}" 
                                                          Width="*"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </TabItem>

                        <!-- Payments Tab -->
                        <TabItem Header="المدفوعات" FontSize="14" FontWeight="SemiBold">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Payments Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                    <materialDesign:PackIcon Kind="CashMultiple" 
                                                           Width="20" Height="20" 
                                                           Foreground="#2C3E50" 
                                                           VerticalAlignment="Center" 
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="مدفوعات المورد" 
                                             Style="{StaticResource HeaderTextStyle}" 
                                             Margin="0,0,20,0"/>
                                    <TextBlock x:Name="PaymentCountText" 
                                             Text="0 دفعة" 
                                             VerticalAlignment="Center" 
                                             Foreground="#7F8C8D"/>
                                </StackPanel>

                                <!-- Payments DataGrid -->
                                <DataGrid x:Name="PaymentsDataGrid" 
                                        Grid.Row="1"
                                        AutoGenerateColumns="False"
                                        CanUserAddRows="False"
                                        CanUserDeleteRows="False"
                                        IsReadOnly="True"
                                        materialDesign:DataGridAssist.CellPadding="8"
                                        materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                                        GridLinesVisibility="Horizontal"
                                        HeadersVisibility="Column">
                                    
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="رقم الإيصال" 
                                                          Binding="{Binding ReceiptNumber}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="التاريخ" 
                                                          Binding="{Binding PaymentDate, StringFormat=yyyy/MM/dd}" 
                                                          Width="100"/>
                                        <DataGridTextColumn Header="المبلغ" 
                                                          Binding="{Binding Amount, StringFormat=N0}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="طريقة الدفع" 
                                                          Binding="{Binding PaymentMethodText}" 
                                                          Width="100"/>
                                        <DataGridTextColumn Header="رقم الفاتورة" 
                                                          Binding="{Binding Invoice.InvoiceNumber}" 
                                                          Width="120"/>
                                        <DataGridTextColumn Header="التفاصيل" 
                                                          Binding="{Binding Details}" 
                                                          Width="*"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </materialDesign:Card>

                <!-- Action Buttons -->
                <Border Grid.Row="3" 
                        Background="#F8F9FA" 
                        CornerRadius="0,0,20,20" 
                        Padding="30,20">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="PrintButton" 
                                Content="طباعة كشف الحساب" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="#667eea"
                                BorderBrush="#667eea"
                                Foreground="White"
                                Margin="0,0,15,0" 
                                Padding="20,10"
                                Click="PrintButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Printer" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                        
                        <Button x:Name="ExportButton" 
                                Content="تصدير إلى Excel" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="#11998e"
                                BorderBrush="#11998e"
                                Foreground="White"
                                Margin="0,0,15,0" 
                                Padding="20,10"
                                Click="ExportButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                        
                        <Button x:Name="RefreshButton" 
                                Content="تحديث البيانات" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="#ffecd2"
                                BorderBrush="#ffecd2"
                                Foreground="#333"
                                Padding="20,10"
                                Click="RefreshButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Indicator -->
        <ProgressBar x:Name="LoadingProgressBar" 
                     Style="{StaticResource MaterialDesignCircularProgressBar}"
                     Width="50" Height="50"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center"
                     IsIndeterminate="True"
                     Visibility="Collapsed"/>
    </Grid>
</UserControl>
