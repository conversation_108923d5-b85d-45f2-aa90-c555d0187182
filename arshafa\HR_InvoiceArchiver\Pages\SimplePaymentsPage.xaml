<UserControl x:Class="HR_InvoiceArchiver.Pages.SimplePaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <materialDesign:Card Grid.Row="0" Margin="20" Padding="30" Background="#007BFF">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CreditCard" Width="40" Height="40" Foreground="White" Margin="0,0,15,0"/>
                <TextBlock Text="إدارة المدفوعات - نسخة مبسطة"
                          FontSize="28"
                          FontWeight="Bold"
                          Foreground="White"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Content -->
        <materialDesign:Card Grid.Row="1" Margin="20" Padding="30">
            <StackPanel>
                <TextBlock Text="مرحباً بك في صفحة المدفوعات!" 
                          FontSize="24" 
                          FontWeight="Bold" 
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,20"/>
                
                <TextBlock Text="هذه نسخة مبسطة لاختبار التنقل" 
                          FontSize="16" 
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,30"/>
                
                <Button Content="اختبار الزر" 
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="#28A745"
                       Foreground="White"
                       Padding="20,12"
                       FontSize="16"
                       HorizontalAlignment="Center"
                       Click="TestButton_Click"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
