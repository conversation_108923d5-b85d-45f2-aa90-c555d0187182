using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HR_InvoiceArchiver.Models
{
    public enum PaymentMethod
    {
        Cash = 0,
        Check = 1,
        BankTransfer = 2,
        CreditCard = 3,
        Other = 4
    }

    public enum PaymentStatus
    {
        FullPayment = 0,      // تسديد كامل
        PartialPayment = 1,   // تسديد جزئي
        PaymentWithDiscount = 2, // تسديد وبخصم
        PaymentWithRefund = 3    // تسديد واسترجاع المتبقي
    }

    public enum CloudSyncStatus
    {
        Pending = 0,    // في انتظار المزامنة
        Syncing = 1,    // جاري المزامنة
        Synced = 2,     // تمت المزامنة
        Failed = 3,     // فشلت المزامنة
        Disabled = 4    // المزامنة معطلة
    }

    public class Payment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string ReceiptNumber { get; set; } = string.Empty;

        [Required]
        public int InvoiceId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal RefundValue { get; set; } = 0;

        [Required]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        public PaymentMethod Method { get; set; } = PaymentMethod.Cash;

        public PaymentStatus Status { get; set; } = PaymentStatus.FullPayment;

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(500)]
        public string? AttachmentPath { get; set; }

        /// <summary>
        /// معرف الملف في التخزين السحابي
        /// </summary>
        [StringLength(200)]
        public string? CloudFileId { get; set; }

        /// <summary>
        /// حالة المزامنة مع السحابة
        /// </summary>
        public CloudSyncStatus SyncStatus { get; set; } = CloudSyncStatus.Pending;

        /// <summary>
        /// تاريخ آخر مزامنة
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;

        // Calculated Properties
        [NotMapped]
        public string MethodText => Method switch
        {
            PaymentMethod.Cash => "نقدي",
            PaymentMethod.Check => "شيك",
            PaymentMethod.BankTransfer => "تحويل بنكي",
            PaymentMethod.CreditCard => "بطاقة ائتمان",
            PaymentMethod.Other => "أخرى",
            _ => "غير محدد"
        };

        [NotMapped]
        public string PaymentMethodText => MethodText;

        // Alias for Method to maintain compatibility
        [NotMapped]
        public PaymentMethod PaymentMethod => Method;

        [NotMapped]
        public string StatusText => Status switch
        {
            PaymentStatus.FullPayment => "تسديد كامل",
            PaymentStatus.PartialPayment => "تسديد جزئي",
            PaymentStatus.PaymentWithDiscount => "تسديد وبخصم",
            PaymentStatus.PaymentWithRefund => "تسديد واسترجاع المتبقي",
            _ => "غير محدد"
        };

        [NotMapped]
        public string Details => Notes ?? string.Empty;

        [NotMapped]
        public string SupplierName => Invoice?.Supplier?.Name ?? string.Empty;

        [NotMapped]
        public string InvoiceNumber => Invoice?.InvoiceNumber ?? string.Empty;

        [NotMapped]
        public decimal TotalSettlement => Amount + RefundValue;

        [NotMapped]
        public string SyncStatusText => SyncStatus switch
        {
            CloudSyncStatus.Pending => "في انتظار المزامنة",
            CloudSyncStatus.Syncing => "جاري المزامنة",
            CloudSyncStatus.Synced => "تمت المزامنة",
            CloudSyncStatus.Failed => "فشلت المزامنة",
            CloudSyncStatus.Disabled => "المزامنة معطلة",
            _ => "غير محدد"
        };

        [NotMapped]
        public bool HasCloudBackup => !string.IsNullOrEmpty(CloudFileId) && SyncStatus == CloudSyncStatus.Synced;

        // Validation Methods
        public bool IsValidAmount()
        {
            return Amount > 0 && Invoice != null && Amount <= Invoice.RemainingAmount;
        }

        public bool IsUniqueReceiptNumber(IEnumerable<Payment> existingPayments)
        {
            return !existingPayments.Any(p => p.Id != Id &&
                                            p.ReceiptNumber.ToLower() == ReceiptNumber.ToLower() &&
                                            p.IsActive);
        }
    }
}
