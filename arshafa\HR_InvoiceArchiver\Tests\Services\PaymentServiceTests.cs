using Xunit;
using FluentAssertions;
using Moq;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class PaymentServiceTests
    {
        private readonly Mock<IPaymentRepository> _mockPaymentRepository;
        private readonly Mock<IInvoiceRepository> _mockInvoiceRepository;
        private readonly PaymentService _paymentService;

        public PaymentServiceTests()
        {
            _mockPaymentRepository = new Mock<IPaymentRepository>();
            _mockInvoiceRepository = new Mock<IInvoiceRepository>();
            _paymentService = new PaymentService(
                _mockPaymentRepository.Object,
                _mockInvoiceRepository.Object
            );
        }

        [Fact]
        public async Task GetAllPaymentsAsync_ShouldReturnAllPayments()
        {
            // Arrange
            var expectedPayments = new List<Payment>
            {
                new Payment { Id = 1, ReceiptNumber = "REC-001" },
                new Payment { Id = 2, ReceiptNumber = "REC-002" }
            };

            _mockPaymentRepository.Setup(x => x.GetAllAsync())
                .ReturnsAsync(expectedPayments);

            // Act
            var result = await _paymentService.GetAllPaymentsAsync();

            // Assert
            result.Should().BeEquivalentTo(expectedPayments);
            _mockPaymentRepository.Verify(x => x.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetPaymentByIdAsync_WithValidId_ShouldReturnPayment()
        {
            // Arrange
            var expectedPayment = new Payment { Id = 1, ReceiptNumber = "REC-001" };
            _mockPaymentRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(expectedPayment);

            // Act
            var result = await _paymentService.GetPaymentByIdAsync(1);

            // Assert
            result.Should().BeEquivalentTo(expectedPayment);
            _mockPaymentRepository.Verify(x => x.GetByIdAsync(1), Times.Once);
        }

        [Fact]
        public async Task GetPaymentByReceiptNumberAsync_WithValidNumber_ShouldReturnPayment()
        {
            // Arrange
            var expectedPayment = new Payment { Id = 1, ReceiptNumber = "REC-001" };
            _mockPaymentRepository.Setup(x => x.GetByReceiptNumberAsync("REC-001"))
                .ReturnsAsync(expectedPayment);

            // Act
            var result = await _paymentService.GetPaymentByReceiptNumberAsync("REC-001");

            // Assert
            result.Should().BeEquivalentTo(expectedPayment);
            _mockPaymentRepository.Verify(x => x.GetByReceiptNumberAsync("REC-001"), Times.Once);
        }

        [Fact]
        public async Task CreatePaymentAsync_WithValidPayment_ShouldReturnCreatedPayment()
        {
            // Arrange
            var invoice = new Invoice
            {
                Id = 1,
                Amount = 1000,
                Payments = new List<Payment>
                {
                    new Payment { Amount = 300, IsActive = true }
                }
            };

            var newPayment = new Payment
            {
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 500
            };

            var createdPayment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 500
            };

            _mockPaymentRepository.Setup(x => x.ExistsByReceiptNumberAsync("REC-001", It.IsAny<int?>()))
                .ReturnsAsync(false);
            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(invoice);
            _mockPaymentRepository.Setup(x => x.AddAsync(It.IsAny<Payment>()))
                .ReturnsAsync(createdPayment);

            // Act
            var result = await _paymentService.CreatePaymentAsync(newPayment);

            // Assert
            result.Should().BeEquivalentTo(createdPayment);
            _mockPaymentRepository.Verify(x => x.AddAsync(It.IsAny<Payment>()), Times.Once);
        }

        [Fact]
        public async Task CreatePaymentAsync_WithDuplicateReceiptNumber_ShouldThrowException()
        {
            // Arrange
            var newPayment = new Payment
            {
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 500
            };

            _mockPaymentRepository.Setup(x => x.ExistsByReceiptNumberAsync("REC-001", It.IsAny<int?>()))
                .ReturnsAsync(true);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _paymentService.CreatePaymentAsync(newPayment));

            exception.Message.Should().Contain("يوجد وصل بنفس الرقم مسبقاً");
            _mockPaymentRepository.Verify(x => x.AddAsync(It.IsAny<Payment>()), Times.Never);
        }

        [Fact]
        public async Task CreatePaymentAsync_WithNonExistentInvoice_ShouldThrowException()
        {
            // Arrange
            var newPayment = new Payment
            {
                ReceiptNumber = "REC-001",
                InvoiceId = 999,
                Amount = 500
            };

            _mockPaymentRepository.Setup(x => x.ExistsByReceiptNumberAsync("REC-001", It.IsAny<int?>()))
                .ReturnsAsync(false);
            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(999))
                .ReturnsAsync((Invoice?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _paymentService.CreatePaymentAsync(newPayment));

            exception.Message.Should().Contain("الفاتورة غير موجودة");
            _mockPaymentRepository.Verify(x => x.AddAsync(It.IsAny<Payment>()), Times.Never);
        }

        [Fact]
        public async Task CreatePaymentAsync_WithAmountExceedingRemaining_ShouldThrowException()
        {
            // Arrange
            var invoice = new Invoice
            {
                Id = 1,
                Amount = 1000,
                Payments = new List<Payment>
                {
                    new Payment { Amount = 800, IsActive = true }
                }
            };

            var newPayment = new Payment
            {
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 500 // أكبر من المتبقي (200)
            };

            _mockPaymentRepository.Setup(x => x.ExistsByReceiptNumberAsync("REC-001", It.IsAny<int?>()))
                .ReturnsAsync(false);
            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(invoice);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _paymentService.CreatePaymentAsync(newPayment));

            exception.Message.Should().Contain("مبلغ الوصل أكبر من المبلغ المتبقي");
            _mockPaymentRepository.Verify(x => x.AddAsync(It.IsAny<Payment>()), Times.Never);
        }

        [Fact]
        public async Task UpdatePaymentAsync_WithValidPayment_ShouldReturnUpdatedPayment()
        {
            // Arrange
            var existingPayment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 500
            };

            var invoice = new Invoice
            {
                Id = 1,
                Amount = 1000,
                Payments = new List<Payment> { existingPayment }
            };

            var updatedPayment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 600
            };

            _mockPaymentRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(existingPayment);
            _mockPaymentRepository.Setup(x => x.ExistsByReceiptNumberAsync("REC-001", 1))
                .ReturnsAsync(false);
            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(invoice);
            _mockPaymentRepository.Setup(x => x.UpdateAsync(It.IsAny<Payment>()))
                .ReturnsAsync(updatedPayment);

            // Act
            var result = await _paymentService.UpdatePaymentAsync(updatedPayment);

            // Assert
            result.Should().BeEquivalentTo(updatedPayment);
            _mockPaymentRepository.Verify(x => x.UpdateAsync(It.IsAny<Payment>()), Times.Once);
        }

        [Fact]
        public async Task DeletePaymentAsync_WithValidId_ShouldReturnTrue()
        {
            // Arrange
            var existingPayment = new Payment
            {
                Id = 1,
                ReceiptNumber = "REC-001"
            };

            _mockPaymentRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(existingPayment);
            _mockPaymentRepository.Setup(x => x.DeleteAsync(1))
                .ReturnsAsync(true);

            // Act
            var result = await _paymentService.DeletePaymentAsync(1);

            // Assert
            result.Should().BeTrue();
            _mockPaymentRepository.Verify(x => x.DeleteAsync(1), Times.Once);
        }

        [Fact]
        public async Task DeletePaymentAsync_WithNonExistentId_ShouldReturnFalse()
        {
            // Arrange
            _mockPaymentRepository.Setup(x => x.GetByIdAsync(999))
                .ReturnsAsync((Payment?)null);

            // Act
            var result = await _paymentService.DeletePaymentAsync(999);

            // Assert
            result.Should().BeFalse();
            _mockPaymentRepository.Verify(x => x.DeleteAsync(It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task ValidatePaymentAsync_WithValidPayment_ShouldReturnTrue()
        {
            // Arrange
            var payment = new Payment
            {
                ReceiptNumber = "REC-001",
                InvoiceId = 1,
                Amount = 500,
                PaymentDate = DateTime.Now
            };

            // Act
            var result = await _paymentService.ValidatePaymentAsync(payment);

            // Assert
            result.Should().BeTrue();
        }

        [Theory]
        [InlineData("", 1, 500)] // رقم وصل فارغ
        [InlineData("REC-001", 0, 500)] // معرف فاتورة غير صحيح
        [InlineData("REC-001", 1, 0)] // مبلغ صفر
        [InlineData("REC-001", 1, -100)] // مبلغ سالب
        public async Task ValidatePaymentAsync_WithInvalidData_ShouldReturnFalse(string receiptNumber, int invoiceId, decimal amount)
        {
            // Arrange
            var payment = new Payment
            {
                ReceiptNumber = receiptNumber,
                InvoiceId = invoiceId,
                Amount = amount,
                PaymentDate = DateTime.Now
            };

            // Act
            var result = await _paymentService.ValidatePaymentAsync(payment);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task GetPaymentsByInvoiceAsync_WithValidInvoiceId_ShouldReturnPayments()
        {
            // Arrange
            var expectedPayments = new List<Payment>
            {
                new Payment { Id = 1, InvoiceId = 1, ReceiptNumber = "REC-001" },
                new Payment { Id = 2, InvoiceId = 1, ReceiptNumber = "REC-002" }
            };

            _mockPaymentRepository.Setup(x => x.GetByInvoiceIdAsync(1))
                .ReturnsAsync(expectedPayments);

            // Act
            var result = await _paymentService.GetPaymentsByInvoiceAsync(1);

            // Assert
            result.Should().BeEquivalentTo(expectedPayments);
            _mockPaymentRepository.Verify(x => x.GetByInvoiceIdAsync(1), Times.Once);
        }

        [Fact]
        public async Task SearchPaymentsAsync_WithSearchTerm_ShouldReturnMatchingPayments()
        {
            // Arrange
            var expectedPayments = new List<Payment>
            {
                new Payment { Id = 1, ReceiptNumber = "REC-001" }
            };

            _mockPaymentRepository.Setup(x => x.SearchAsync("REC-001"))
                .ReturnsAsync(expectedPayments);

            // Act
            var result = await _paymentService.SearchPaymentsAsync("REC-001");

            // Assert
            result.Should().BeEquivalentTo(expectedPayments);
            _mockPaymentRepository.Verify(x => x.SearchAsync("REC-001"), Times.Once);
        }
    }
}
