using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;
using MaterialDesignThemes.Wpf;

namespace HR_InvoiceArchiver.Controls
{
    public partial class PaymentDetailsControl : UserControl
    {
        private Payment _payment = null!;
        private readonly IToastService _toastService;

        public event EventHandler? FormClosed;

        public PaymentDetailsControl()
        {
            InitializeComponent();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
        }

        public void LoadPaymentDetails(Payment payment)
        {
            _payment = payment;
            if (_payment == null) return;

            // Update header
            HeaderTitle.Text = $"وصل دفع رقم: {_payment.ReceiptNumber}";
            HeaderSubtitle.Text = $"المورد: {_payment.SupplierName}";

            // Clear existing content
            ContentPanel.Children.Clear();
            FooterPanel.Children.Clear();

            // Add content cards
            AddPaymentInfoCard();
            AddFinancialDetailsCard();
            AddInvoiceInfoCard();
            AddNotesCard();
            AddSystemInfoCard();

            // Add footer buttons
            AddFooterButtons();
        }

        private void AddPaymentInfoCard()
        {
            var card = CreateInfoCard("معلومات الدفعة", new[]
            {
                new { Icon = "💰", Label = "المبلغ المدفوع", Value = $"{_payment.Amount:N0} د.ع", Color = "#28A745" },
                new { Icon = "📅", Label = "تاريخ الدفع", Value = _payment.PaymentDate.ToString("dd/MM/yyyy"), Color = "#007BFF" },
                new { Icon = "💳", Label = "طريقة الدفع", Value = _payment.PaymentMethodText, Color = "#6F42C1" },
                new { Icon = "📊", Label = "حالة التسديد", Value = _payment.StatusText, Color = "#17A2B8" }
            });

            ContentPanel.Children.Add(card);
        }

        private void AddFinancialDetailsCard()
        {
            if (_payment.DiscountAmount > 0 || _payment.RefundValue > 0)
            {
                var card = CreateInfoCard("التفاصيل المالية", new[]
                {
                    new { Icon = "💸", Label = "مبلغ الخصم", Value = $"{_payment.DiscountAmount:N0} د.ع", Color = "#FFC107" },
                    new { Icon = "↩️", Label = "قيمة الاسترجاع", Value = $"{_payment.RefundValue:N0} د.ع", Color = "#DC3545" },
                    new { Icon = "🧮", Label = "إجمالي التسوية", Value = $"{_payment.TotalSettlement:N0} د.ع", Color = "#28A745" }
                });

                ContentPanel.Children.Add(card);
            }
        }

        private void AddInvoiceInfoCard()
        {
            var card = CreateInfoCard("معلومات الفاتورة", new[]
            {
                new { Icon = "📄", Label = "رقم الفاتورة", Value = _payment.InvoiceNumber, Color = "#007BFF" },
                new { Icon = "💼", Label = "اسم المورد", Value = _payment.SupplierName, Color = "#6C757D" },
                new { Icon = "💰", Label = "المبلغ المتبقي", Value = $"{_payment.Invoice?.RemainingAmount:N0} د.ع", Color = "#FD7E14" }
            });

            ContentPanel.Children.Add(card);
        }

        private void AddNotesCard()
        {
            if (!string.IsNullOrEmpty(_payment.Notes))
            {
                var card = CreateInfoCard("ملاحظات", new[]
                {
                    new { Icon = "📝", Label = "التفاصيل", Value = _payment.Notes, Color = "#6C757D" }
                });

                ContentPanel.Children.Add(card);
            }
        }

        private void AddSystemInfoCard()
        {
            var card = CreateInfoCard("معلومات النظام", new[]
            {
                new { Icon = "🕒", Label = "تاريخ الإنشاء", Value = _payment.CreatedDate.ToString("dd/MM/yyyy HH:mm"), Color = "#6C757D" },
                new { Icon = "✏️", Label = "آخر تحديث", Value = _payment.UpdatedDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث", Color = "#6C757D" }
            });

            ContentPanel.Children.Add(card);
        }

        private void AddFooterButtons()
        {
            // Attachment Button
            if (!string.IsNullOrEmpty(_payment.AttachmentPath))
            {
                var attachmentButton = new Button
                {
                    Content = "📎 عرض المرفق",
                    Style = (Style)FindResource("ModernButtonStyle"),
                    Background = new SolidColorBrush(Color.FromRgb(255, 107, 53)),
                    Foreground = Brushes.White
                };

                attachmentButton.Click += (s, e) => OpenPaymentAttachment();
                FooterPanel.Children.Add(attachmentButton);
            }

            // Close Button
            var closeButton = new Button
            {
                Content = "إغلاق",
                Style = (Style)FindResource("ModernButtonStyle"),
                Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Foreground = Brushes.White
            };

            closeButton.Click += CloseButton_Click;
            FooterPanel.Children.Add(closeButton);
        }

        private MaterialDesignThemes.Wpf.Card CreateInfoCard(string title, dynamic[] items)
        {
            var card = new MaterialDesignThemes.Wpf.Card
            {
                Style = (Style)FindResource("ModernCardStyle")
            };

            var cardPanel = new StackPanel();

            // Card Header
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(0, 0, 0, 1),
                Padding = new Thickness(25, 20, 25, 20),
                CornerRadius = new CornerRadius(12, 12, 0, 0)
            };

            var headerText = new TextBlock
            {
                Text = title,
                Style = (Style)FindResource("HeaderTextStyle")
            };

            headerBorder.Child = headerText;
            cardPanel.Children.Add(headerBorder);

            // Card Content
            var contentPanel = new StackPanel
            {
                Margin = new Thickness(25, 20, 25, 20)
            };

            foreach (var item in items)
            {
                var itemPanel = new StackPanel
                {
                    Style = (Style)FindResource("InfoItemStyle")
                };

                // Icon
                var iconText = new TextBlock
                {
                    Text = item.Icon,
                    Style = (Style)FindResource("IconStyle")
                };

                // Label and Value Container
                var textContainer = new StackPanel
                {
                    VerticalAlignment = VerticalAlignment.Center
                };

                var labelText = new TextBlock
                {
                    Text = item.Label,
                    Style = (Style)FindResource("LabelStyle")
                };

                var valueText = new TextBlock
                {
                    Text = item.Value,
                    Style = (Style)FindResource("ValueStyle"),
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(item.Color))
                };

                textContainer.Children.Add(labelText);
                textContainer.Children.Add(valueText);

                itemPanel.Children.Add(iconText);
                itemPanel.Children.Add(textContainer);

                contentPanel.Children.Add(itemPanel);
            }

            cardPanel.Children.Add(contentPanel);
            card.Content = cardPanel;

            return card;
        }

        private void OpenPaymentAttachment()
        {
            try
            {
                if (string.IsNullOrEmpty(_payment.AttachmentPath))
                {
                    _toastService?.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                    return;
                }

                Utils.FileHelper.OpenAttachment(_payment.AttachmentPath, "Payments");
                _toastService?.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {_payment.ReceiptNumber}");
            }
            catch (FileNotFoundException)
            {
                _toastService?.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في فتح الملف", $"فشل في فتح مرفق الوصل: {ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            FormClosed?.Invoke(this, EventArgs.Empty);
        }
    }
}
