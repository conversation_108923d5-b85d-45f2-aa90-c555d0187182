using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Controls;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة إشعارات النجاح المحسنة - تصميم احترافي وجميل
    /// </summary>
    public interface ISuccessNotificationService
    {
        void ShowSuccess(string title, string message, SuccessNotificationOptions? options = null);
        void ShowOperationSuccess(string operation, string details, SuccessNotificationOptions? options = null);
        void ShowSaveSuccess(string itemName, string additionalInfo = "");
        void ShowDeleteSuccess(string itemName);
        void ShowUpdateSuccess(string itemName);
        void ShowCreateSuccess(string itemName, string additionalInfo = "");
        void SetContainer(Panel container);
    }

    public class SuccessNotificationService : ISuccessNotificationService
    {
        private Panel? _container;

        public void SetContainer(Panel container)
        {
            _container = container;
        }

        public void ShowSuccess(string title, string message, SuccessNotificationOptions? options = null)
        {
            options ??= new SuccessNotificationOptions();
            
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    var container = GetOrCreateContainer();
                    if (container == null) return;

                    var notification = new EnhancedSuccessNotification
                    {
                        Title = title,
                        Message = message,
                        Icon = options.Icon,
                        Duration = options.Duration,
                        ShowActions = options.ShowActions,
                        PrimaryActionText = options.PrimaryActionText,
                        SecondaryActionText = options.SecondaryActionText,
                        NotificationType = options.Type
                    };

                    // إعداد الأحداث
                    if (options.PrimaryAction != null)
                        notification.PrimaryActionClicked += (s, e) => options.PrimaryAction();
                    
                    if (options.SecondaryAction != null)
                        notification.SecondaryActionClicked += (s, e) => options.SecondaryAction();

                    notification.NotificationClosed += (s, e) => container.Children.Remove(notification);

                    // إضافة الإشعار وعرضه
                    container.Children.Add(notification);
                    notification.Show();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في عرض إشعار النجاح: {ex.Message}");
                }
            });
        }

        public void ShowOperationSuccess(string operation, string details, SuccessNotificationOptions? options = null)
        {
            var title = $"✨ تم {operation} بنجاح!";
            ShowSuccess(title, details, options);
        }

        public void ShowSaveSuccess(string itemName, string additionalInfo = "")
        {
            var message = $"تم حفظ {itemName} بنجاح";
            if (!string.IsNullOrEmpty(additionalInfo))
                message += $"\n{additionalInfo}";

            var options = new SuccessNotificationOptions
            {
                Icon = "💾",
                Type = SuccessNotificationType.Save,
                Duration = TimeSpan.FromSeconds(4)
            };

            ShowSuccess("تم الحفظ بنجاح", message, options);
        }

        public void ShowDeleteSuccess(string itemName)
        {
            var options = new SuccessNotificationOptions
            {
                Icon = "🗑️",
                Type = SuccessNotificationType.Delete,
                Duration = TimeSpan.FromSeconds(3)
            };

            ShowSuccess("تم الحذف بنجاح", $"تم حذف {itemName} نهائياً من النظام", options);
        }

        public void ShowUpdateSuccess(string itemName)
        {
            var options = new SuccessNotificationOptions
            {
                Icon = "✏️",
                Type = SuccessNotificationType.Update,
                Duration = TimeSpan.FromSeconds(4)
            };

            ShowSuccess("تم التحديث بنجاح", $"تم تحديث بيانات {itemName} بنجاح", options);
        }

        public void ShowCreateSuccess(string itemName, string additionalInfo = "")
        {
            var message = $"تم إنشاء {itemName} بنجاح وإضافته إلى النظام";
            if (!string.IsNullOrEmpty(additionalInfo))
                message += $"\n{additionalInfo}";

            var options = new SuccessNotificationOptions
            {
                Icon = "➕",
                Type = SuccessNotificationType.Create,
                Duration = TimeSpan.FromSeconds(5),
                ShowActions = true,
                PrimaryActionText = "عرض التفاصيل",
                SecondaryActionText = "إضافة آخر"
            };

            ShowSuccess("تم الإنشاء بنجاح", message, options);
        }

        private Panel? GetOrCreateContainer()
        {
            if (_container != null) return _container;

            // محاولة العثور على الحاوي في النافذة الرئيسية
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow?.Content is Grid mainGrid)
            {
                return FindOrCreateNotificationContainer(mainGrid);
            }

            return null;
        }

        private Panel? FindOrCreateNotificationContainer(Grid mainGrid)
        {
            // البحث عن حاوي موجود
            foreach (UIElement child in mainGrid.Children)
            {
                if (child is Panel panel && panel.Name == "SuccessNotificationContainer")
                {
                    return panel;
                }
            }

            // إنشاء حاوي جديد
            try
            {
                var container = new Canvas
                {
                    Name = "SuccessNotificationContainer",
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch,
                    IsHitTestVisible = false
                };

                Grid.SetRowSpan(container, mainGrid.RowDefinitions.Count > 0 ? mainGrid.RowDefinitions.Count : 1);
                Grid.SetColumnSpan(container, mainGrid.ColumnDefinitions.Count > 0 ? mainGrid.ColumnDefinitions.Count : 1);
                
                mainGrid.Children.Add(container);
                _container = container;
                return container;
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// خيارات إشعار النجاح
    /// </summary>
    public class SuccessNotificationOptions
    {
        public string Icon { get; set; } = "✅";
        public TimeSpan Duration { get; set; } = TimeSpan.FromSeconds(5);
        public bool ShowActions { get; set; } = false;
        public string PrimaryActionText { get; set; } = "عرض";
        public string SecondaryActionText { get; set; } = "إضافة آخر";
        public Action? PrimaryAction { get; set; }
        public Action? SecondaryAction { get; set; }
        public SuccessNotificationType Type { get; set; } = SuccessNotificationType.General;
    }

    /// <summary>
    /// أنواع إشعارات النجاح
    /// </summary>
    public enum SuccessNotificationType
    {
        General,
        Save,
        Delete,
        Update,
        Create,
        Import,
        Export,
        Sync
    }

    /// <summary>
    /// Extension methods لسهولة الاستخدام
    /// </summary>
    public static class SuccessNotificationExtensions
    {
        public static void ShowQuickSuccess(this ISuccessNotificationService service, string message)
        {
            service.ShowSuccess("نجحت العملية", message, new SuccessNotificationOptions
            {
                Duration = TimeSpan.FromSeconds(3)
            });
        }

        public static void ShowDetailedSuccess(this ISuccessNotificationService service, string title, string message, Action? primaryAction = null)
        {
            service.ShowSuccess(title, message, new SuccessNotificationOptions
            {
                ShowActions = primaryAction != null,
                PrimaryAction = primaryAction,
                Duration = TimeSpan.FromSeconds(6)
            });
        }
    }
}
