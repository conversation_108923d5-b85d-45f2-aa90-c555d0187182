﻿#pragma checksum "..\..\..\..\Pages\PerformanceOptimizationPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8385ED5C86833ED3E1BA36C900794618202C57A9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PerformanceOptimizationPage
    /// </summary>
    public partial class PerformanceOptimizationPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 36 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeAllButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl PerformanceTabControl;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnalyzeButton;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PerformanceOverviewGrid;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DatabaseSizeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QueryTimeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IndexesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon ScoreIcon;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ScoreTextBlock;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateIndexesButton;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UpdateStatsButton;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleanupButton;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CompressButton;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeConnectionsButton;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScheduleOptimizationButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl RecommendationsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshMemoryButton;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MemoryUsageGrid;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearCacheButton;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeCacheButton;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SlowQueriesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/performanceoptimizationpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OptimizeAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.OptimizeAllButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PerformanceTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 4:
            this.AnalyzeButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.AnalyzeButton.Click += new System.Windows.RoutedEventHandler(this.AnalyzeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PerformanceOverviewGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.DatabaseSizeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.QueryTimeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.IndexesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ScoreIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 10:
            this.ScoreTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CreateIndexesButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.CreateIndexesButton.Click += new System.Windows.RoutedEventHandler(this.CreateIndexesButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.UpdateStatsButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.UpdateStatsButton.Click += new System.Windows.RoutedEventHandler(this.UpdateStatsButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CleanupButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.CleanupButton.Click += new System.Windows.RoutedEventHandler(this.CleanupButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CompressButton = ((System.Windows.Controls.Button)(target));
            
            #line 228 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.CompressButton.Click += new System.Windows.RoutedEventHandler(this.CompressButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.OptimizeConnectionsButton = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.OptimizeConnectionsButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeConnectionsButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ScheduleOptimizationButton = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.ScheduleOptimizationButton.Click += new System.Windows.RoutedEventHandler(this.ScheduleOptimizationButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.RecommendationsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 18:
            this.RefreshMemoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 317 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.RefreshMemoryButton.Click += new System.Windows.RoutedEventHandler(this.RefreshMemoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.MemoryUsageGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 20:
            this.ClearCacheButton = ((System.Windows.Controls.Button)(target));
            
            #line 342 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.ClearCacheButton.Click += new System.Windows.RoutedEventHandler(this.ClearCacheButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.OptimizeCacheButton = ((System.Windows.Controls.Button)(target));
            
            #line 352 "..\..\..\..\Pages\PerformanceOptimizationPage.xaml"
            this.OptimizeCacheButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeCacheButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.SlowQueriesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 23:
            this.LoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 24:
            this.LoadingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

