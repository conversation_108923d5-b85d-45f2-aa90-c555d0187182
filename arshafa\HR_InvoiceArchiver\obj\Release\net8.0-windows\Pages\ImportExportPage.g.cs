﻿#pragma checksum "..\..\..\..\Pages\ImportExportPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F486CB5876B0D0198A501F49144FB45F5FA51261"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// ImportExportPage
    /// </summary>
    public partial class ImportExportPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 36 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button HistoryButton;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleanupButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExportDataTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExportFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ExportStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ExportEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExportFilePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseExportPathButton;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeAttachmentsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompressOutputCheckBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ImportFilePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseImportFileButton;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ValidateFileButton;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ImportFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ImportModeComboBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StartRowTextBox;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ValidateDataCheckBox;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SkipDuplicatesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CreateBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card PreviewCard;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PreviewDataGrid;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportButton;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Pages\ImportExportPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/importexportpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\ImportExportPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HistoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.HistoryButton.Click += new System.Windows.RoutedEventHandler(this.HistoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CleanupButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.CleanupButton.Click += new System.Windows.RoutedEventHandler(this.CleanupButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportDataTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.ExportFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.ExportStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.ExportEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.ExportFilePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.BrowseExportPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.BrowseExportPathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseExportPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.IncludeAttachmentsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.CompressOutputCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.CreateTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.CreateTemplateButton.Click += new System.Windows.RoutedEventHandler(this.CreateTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 190 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ImportFilePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.BrowseImportFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 241 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.BrowseImportFileButton.Click += new System.Windows.RoutedEventHandler(this.BrowseImportFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ValidateFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.ValidateFileButton.Click += new System.Windows.RoutedEventHandler(this.ValidateFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ImportFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.ImportModeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.StartRowTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.ValidateDataCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.SkipDuplicatesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.CreateBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.PreviewCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 23:
            this.PreviewDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 24:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 356 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ImportButton = ((System.Windows.Controls.Button)(target));
            
            #line 366 "..\..\..\..\Pages\ImportExportPage.xaml"
            this.ImportButton.Click += new System.Windows.RoutedEventHandler(this.ImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.LoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 27:
            this.LoadingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

