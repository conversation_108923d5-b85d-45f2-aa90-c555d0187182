using Xunit;
using FluentAssertions;
using Moq;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class InvoiceServiceTests
    {
        private readonly Mock<IInvoiceRepository> _mockInvoiceRepository;
        private readonly Mock<IPaymentRepository> _mockPaymentRepository;
        private readonly Mock<ISupplierRepository> _mockSupplierRepository;
        private readonly InvoiceService _invoiceService;

        public InvoiceServiceTests()
        {
            _mockInvoiceRepository = new Mock<IInvoiceRepository>();
            _mockPaymentRepository = new Mock<IPaymentRepository>();
            _mockSupplierRepository = new Mock<ISupplierRepository>();
            _invoiceService = new InvoiceService(
                _mockInvoiceRepository.Object,
                _mockSupplierRepository.Object,
                _mockPaymentRepository.Object
            );
        }

        [Fact]
        public async Task GetAllInvoicesAsync_ShouldReturnAllInvoices()
        {
            // Arrange
            var expectedInvoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV-001" },
                new Invoice { Id = 2, InvoiceNumber = "INV-002" }
            };

            _mockInvoiceRepository.Setup(x => x.GetAllAsync())
                .ReturnsAsync(expectedInvoices);

            // Act
            var result = await _invoiceService.GetAllInvoicesAsync();

            // Assert
            result.Should().BeEquivalentTo(expectedInvoices);
            _mockInvoiceRepository.Verify(x => x.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetAllInvoicesBasicAsync_ShouldReturnBasicInvoices()
        {
            // Arrange
            var expectedInvoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV-001" },
                new Invoice { Id = 2, InvoiceNumber = "INV-002" }
            };

            _mockInvoiceRepository.Setup(x => x.GetAllBasicAsync())
                .ReturnsAsync(expectedInvoices);

            // Act
            var result = await _invoiceService.GetAllInvoicesBasicAsync();

            // Assert
            result.Should().BeEquivalentTo(expectedInvoices);
            _mockInvoiceRepository.Verify(x => x.GetAllBasicAsync(), Times.Once);
        }

        [Fact]
        public async Task GetInvoiceByIdAsync_WithValidId_ShouldReturnInvoice()
        {
            // Arrange
            var expectedInvoice = new Invoice { Id = 1, InvoiceNumber = "INV-001" };
            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(expectedInvoice);

            // Act
            var result = await _invoiceService.GetInvoiceByIdAsync(1);

            // Assert
            result.Should().BeEquivalentTo(expectedInvoice);
            _mockInvoiceRepository.Verify(x => x.GetByIdAsync(1), Times.Once);
        }

        [Fact]
        public async Task GetInvoiceByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(999))
                .ReturnsAsync((Invoice?)null);

            // Act
            var result = await _invoiceService.GetInvoiceByIdAsync(999);

            // Assert
            result.Should().BeNull();
            _mockInvoiceRepository.Verify(x => x.GetByIdAsync(999), Times.Once);
        }

        [Fact]
        public async Task GetInvoiceByNumberAsync_WithValidNumber_ShouldReturnInvoice()
        {
            // Arrange
            var expectedInvoice = new Invoice { Id = 1, InvoiceNumber = "INV-001" };
            _mockInvoiceRepository.Setup(x => x.GetByInvoiceNumberAsync("INV-001"))
                .ReturnsAsync(expectedInvoice);

            // Act
            var result = await _invoiceService.GetInvoiceByNumberAsync("INV-001");

            // Assert
            result.Should().BeEquivalentTo(expectedInvoice);
            _mockInvoiceRepository.Verify(x => x.GetByInvoiceNumberAsync("INV-001"), Times.Once);
        }

        [Fact]
        public async Task CreateInvoiceAsync_WithValidInvoice_ShouldReturnCreatedInvoice()
        {
            // Arrange
            var newInvoice = new Invoice
            {
                InvoiceNumber = "INV-001",
                SupplierId = 1,
                Amount = 1000
            };

            var createdInvoice = new Invoice
            {
                Id = 1,
                InvoiceNumber = "INV-001",
                SupplierId = 1,
                Amount = 1000
            };

            _mockInvoiceRepository.Setup(x => x.ExistsByInvoiceNumberAsync("INV-001", null))
                .ReturnsAsync(false);
            _mockSupplierRepository.Setup(x => x.ExistsAsync(1))
                .ReturnsAsync(true);
            _mockInvoiceRepository.Setup(x => x.AddAsync(It.IsAny<Invoice>()))
                .ReturnsAsync(createdInvoice);

            // Act
            var result = await _invoiceService.CreateInvoiceAsync(newInvoice);

            // Assert
            result.Should().BeEquivalentTo(createdInvoice);
            _mockInvoiceRepository.Verify(x => x.AddAsync(It.IsAny<Invoice>()), Times.Once);
        }

        [Fact]
        public async Task CreateInvoiceAsync_WithDuplicateInvoiceNumber_ShouldThrowException()
        {
            // Arrange
            var newInvoice = new Invoice
            {
                InvoiceNumber = "INV-001",
                SupplierId = 1,
                Amount = 1000
            };

            _mockInvoiceRepository.Setup(x => x.ExistsByInvoiceNumberAsync("INV-001", null))
                .ReturnsAsync(true);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _invoiceService.CreateInvoiceAsync(newInvoice));

            exception.Message.Should().Contain("رقم الفاتورة موجود مسبقاً");
            _mockInvoiceRepository.Verify(x => x.AddAsync(It.IsAny<Invoice>()), Times.Never);
        }

        [Fact]
        public async Task CreateInvoiceAsync_WithInvalidSupplierId_ShouldThrowException()
        {
            // Arrange
            var newInvoice = new Invoice
            {
                InvoiceNumber = "INV-001",
                SupplierId = 999,
                Amount = 1000
            };

            _mockInvoiceRepository.Setup(x => x.ExistsByInvoiceNumberAsync("INV-001", null))
                .ReturnsAsync(false);
            _mockSupplierRepository.Setup(x => x.ExistsAsync(999))
                .ReturnsAsync(false);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _invoiceService.CreateInvoiceAsync(newInvoice));

            exception.Message.Should().Contain("المورد غير موجود");
            _mockInvoiceRepository.Verify(x => x.AddAsync(It.IsAny<Invoice>()), Times.Never);
        }

        [Fact]
        public async Task UpdateInvoiceAsync_WithValidInvoice_ShouldReturnUpdatedInvoice()
        {
            // Arrange
            var existingInvoice = new Invoice
            {
                Id = 1,
                InvoiceNumber = "INV-001",
                SupplierId = 1,
                Amount = 1000
            };

            var updatedInvoice = new Invoice
            {
                Id = 1,
                InvoiceNumber = "INV-001",
                SupplierId = 1,
                Amount = 1500
            };

            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(existingInvoice);
            _mockInvoiceRepository.Setup(x => x.ExistsByInvoiceNumberAsync("INV-001", 1))
                .ReturnsAsync(false);
            _mockSupplierRepository.Setup(x => x.ExistsAsync(1))
                .ReturnsAsync(true);
            _mockInvoiceRepository.Setup(x => x.UpdateAsync(It.IsAny<Invoice>()))
                .ReturnsAsync(updatedInvoice);

            // Act
            var result = await _invoiceService.UpdateInvoiceAsync(updatedInvoice);

            // Assert
            result.Should().BeEquivalentTo(updatedInvoice);
            _mockInvoiceRepository.Verify(x => x.UpdateAsync(It.IsAny<Invoice>()), Times.Once);
        }

        [Fact]
        public async Task UpdateInvoiceAsync_WithNonExistentInvoice_ShouldThrowException()
        {
            // Arrange
            var updatedInvoice = new Invoice
            {
                Id = 999,
                InvoiceNumber = "INV-999",
                SupplierId = 1,
                Amount = 1500
            };

            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(999))
                .ReturnsAsync((Invoice?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _invoiceService.UpdateInvoiceAsync(updatedInvoice));

            exception.Message.Should().Contain("الفاتورة غير موجودة");
            _mockInvoiceRepository.Verify(x => x.UpdateAsync(It.IsAny<Invoice>()), Times.Never);
        }

        [Fact]
        public async Task DeleteInvoiceAsync_WithValidId_ShouldReturnTrue()
        {
            // Arrange
            var existingInvoice = new Invoice
            {
                Id = 1,
                InvoiceNumber = "INV-001",
                Payments = new List<Payment>()
            };

            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(existingInvoice);
            _mockInvoiceRepository.Setup(x => x.DeleteAsync(1))
                .ReturnsAsync(true);

            // Act
            var result = await _invoiceService.DeleteInvoiceAsync(1);

            // Assert
            result.Should().BeTrue();
            _mockInvoiceRepository.Verify(x => x.DeleteAsync(1), Times.Once);
        }

        [Fact]
        public async Task DeleteInvoiceAsync_WithInvoiceHavingPayments_ShouldThrowException()
        {
            // Arrange
            var existingInvoice = new Invoice
            {
                Id = 1,
                InvoiceNumber = "INV-001",
                Payments = new List<Payment>
                {
                    new Payment { Id = 1, Amount = 500 }
                }
            };

            _mockInvoiceRepository.Setup(x => x.GetByIdAsync(1))
                .ReturnsAsync(existingInvoice);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _invoiceService.DeleteInvoiceAsync(1));

            exception.Message.Should().Contain("لا يمكن حذف الفاتورة لأنها تحتوي على مدفوعات");
            _mockInvoiceRepository.Verify(x => x.DeleteAsync(It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task InvoiceExistsAsync_WithExistingId_ShouldReturnTrue()
        {
            // Arrange
            _mockInvoiceRepository.Setup(x => x.ExistsAsync(1))
                .ReturnsAsync(true);

            // Act
            var result = await _invoiceService.InvoiceExistsAsync(1);

            // Assert
            result.Should().BeTrue();
            _mockInvoiceRepository.Verify(x => x.ExistsAsync(1), Times.Once);
        }

        [Fact]
        public async Task InvoiceNumberExistsAsync_WithExistingNumber_ShouldReturnTrue()
        {
            // Arrange
            _mockInvoiceRepository.Setup(x => x.ExistsByInvoiceNumberAsync("INV-001", null))
                .ReturnsAsync(true);

            // Act
            var result = await _invoiceService.InvoiceNumberExistsAsync("INV-001");

            // Assert
            result.Should().BeTrue();
            _mockInvoiceRepository.Verify(x => x.ExistsByInvoiceNumberAsync("INV-001", null), Times.Once);
        }

        [Fact]
        public async Task SearchInvoicesAsync_WithSearchTerm_ShouldReturnMatchingInvoices()
        {
            // Arrange
            var expectedInvoices = new List<Invoice>
            {
                new Invoice { Id = 1, InvoiceNumber = "INV-001" }
            };

            _mockInvoiceRepository.Setup(x => x.SearchAsync("INV-001"))
                .ReturnsAsync(expectedInvoices);

            // Act
            var result = await _invoiceService.SearchInvoicesAsync("INV-001");

            // Assert
            result.Should().BeEquivalentTo(expectedInvoices);
            _mockInvoiceRepository.Verify(x => x.SearchAsync("INV-001"), Times.Once);
        }
    }
}
