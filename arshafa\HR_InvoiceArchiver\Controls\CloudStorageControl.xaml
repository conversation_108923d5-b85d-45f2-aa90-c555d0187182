<UserControl x:Class="HR_InvoiceArchiver.Controls.CloudStorageControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Info Item Style -->
        <Style x:Key="InfoItemStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Icon Style -->
        <Style x:Key="IconStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="Margin" Value="0,0,15,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#6C757D"/>
            <Setter Property="Margin" Value="0,0,0,3"/>
        </Style>

        <!-- Value Style -->
        <Style x:Key="ValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <!-- Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,10,0"/>
        </Style>

        <!-- Status Card Style -->
        <Style x:Key="StatusCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="20">
            
            <!-- Header Card -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                <StackPanel Margin="25">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <materialDesign:PackIcon Kind="CloudUpload" 
                                               Width="32" Height="32" 
                                               Foreground="#2196F3"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="التخزين السحابي - Google Drive" 
                                  Style="{StaticResource HeaderTextStyle}"
                                  Margin="15,0,0,0"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                    
                    <TextBlock Text="احفظ نسخة احتياطية آمنة من مرفقاتك في السحابة"
                              FontSize="14" 
                              Foreground="#6C757D"
                              TextWrapping="Wrap"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Connection Status Card -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                <StackPanel Margin="25">
                    <TextBlock Text="حالة الاتصال" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <Border x:Name="StatusCard" Style="{StaticResource StatusCardStyle}">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                <materialDesign:PackIcon x:Name="StatusIcon" 
                                                       Kind="CloudOff" 
                                                       Width="24" Height="24" 
                                                       Foreground="#F44336"
                                                       VerticalAlignment="Center"/>
                                <TextBlock x:Name="StatusText" 
                                          Text="غير متصل"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="#F44336"
                                          Margin="10,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                            
                            <TextBlock x:Name="StatusDescription" 
                                      Text="لم يتم ربط Google Drive بعد"
                                      FontSize="13"
                                      Foreground="#6C757D"/>
                        </StackPanel>
                    </Border>

                    <!-- User Info (Hidden initially) -->
                    <Border x:Name="UserInfoCard" Style="{StaticResource StatusCardStyle}" Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="معلومات الحساب" 
                                      FontSize="14" FontWeight="Bold"
                                      Margin="0,0,0,10"/>
                            
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Account" 
                                                       Width="20" Height="20" 
                                                       Foreground="#2196F3"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="10,0,0,0">
                                    <TextBlock x:Name="UserName" Text="" FontWeight="SemiBold"/>
                                    <TextBlock x:Name="UserEmail" Text="" FontSize="12" Foreground="#666"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                        <Button x:Name="ConnectButton" 
                               Content="ربط Google Drive"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#4CAF50"
                               Click="ConnectButton_Click"/>
                        
                        <Button x:Name="DisconnectButton" 
                               Content="قطع الاتصال"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#F44336"
                               Visibility="Collapsed"
                               Click="DisconnectButton_Click"/>
                        
                        <Button x:Name="RefreshButton" 
                               Content="تحديث"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FF9800"
                               Visibility="Collapsed"
                               Click="RefreshButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Storage Statistics Card -->
            <materialDesign:Card x:Name="StorageStatsCard" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                <StackPanel Margin="25">
                    <TextBlock Text="إحصائيات التخزين" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Total Files -->
                        <Border Grid.Column="0" Style="{StaticResource StatusCardStyle}" Margin="0,0,10,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileMultiple" 
                                                       Width="32" Height="32" 
                                                       Foreground="#2196F3"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalFilesText" 
                                          Text="0"
                                          FontSize="24" FontWeight="Bold"
                                          Foreground="#2196F3"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="إجمالي الملفات"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Total Size -->
                        <Border Grid.Column="1" Style="{StaticResource StatusCardStyle}" Margin="5,0,5,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="HardDisk" 
                                                       Width="32" Height="32" 
                                                       Foreground="#FF9800"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalSizeText" 
                                          Text="0 MB"
                                          FontSize="24" FontWeight="Bold"
                                          Foreground="#FF9800"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="الحجم الإجمالي"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Last Sync -->
                        <Border Grid.Column="2" Style="{StaticResource StatusCardStyle}" Margin="10,0,0,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CloudSync" 
                                                       Width="32" Height="32" 
                                                       Foreground="#4CAF50"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="LastSyncText" 
                                          Text="--"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="#4CAF50"
                                          HorizontalAlignment="Center"
                                          TextWrapping="Wrap"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="آخر مزامنة"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Synced Files Card -->
            <materialDesign:Card x:Name="SyncedFilesCard" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                <StackPanel Margin="25">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="الملفات المتزامنة" Style="{StaticResource HeaderTextStyle}" Margin="0"/>
                        <Button x:Name="RefreshFilesButton"
                               Content="🔄"
                               Width="35" Height="35"
                               FontSize="14"
                               Margin="15,0,0,0"
                               Background="Transparent"
                               BorderThickness="1"
                               BorderBrush="#DDD"
                               Click="RefreshFilesButton_Click"
                               ToolTip="تحديث قائمة الملفات"/>
                    </StackPanel>
                    
                    <Border Background="#F8F9FA" 
                           CornerRadius="8" 
                           Padding="15"
                           MaxHeight="300">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <ListView x:Name="SyncedFilesList" 
                                     Background="Transparent"
                                     BorderThickness="0">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="اسم الملف" Width="250">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="FileDocument" 
                                                                               Width="16" Height="16" 
                                                                               Foreground="#2196F3"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{Binding FileName}" 
                                                                  FontSize="12"
                                                                  Margin="8,0,0,0"
                                                                  VerticalAlignment="Center"
                                                                  ToolTip="{Binding FileName}"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="الحجم" Width="100">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding FileSize}" 
                                                              FontSize="12"
                                                              HorizontalAlignment="Center"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="تاريخ الرفع" Width="150">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding UploadDate}" 
                                                              FontSize="12"
                                                              HorizontalAlignment="Center"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="الحالة" Width="100">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="CheckCircle" 
                                                                               Width="14" Height="14" 
                                                                               Foreground="#4CAF50"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="متزامن" 
                                                                  FontSize="11"
                                                                  Foreground="#4CAF50"
                                                                  Margin="5,0,0,0"
                                                                  VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </ScrollViewer>
                    </Border>
                </StackPanel>
            </materialDesign:Card>

        </StackPanel>
    </ScrollViewer>
</UserControl>
