<UserControl x:Class="HR_InvoiceArchiver.Controls.Dashboard.ChartSectionControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft"
             Background="Transparent">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Chart Container -->
    <materialDesign:Card Style="{StaticResource ModernCardStyle}" Margin="8">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="300"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="الاتجاهات الشهرية"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             Foreground="#1976D2" FontWeight="Bold"/>
                    <TextBlock Text="تحليل الفواتير والمدفوعات الشهرية"
                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                             Opacity="0.7" Margin="0,2,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="RefreshChartButton" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="تحديث المخطط" 
                          Click="RefreshChartButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18"/>
                    </Button>
                    <Button x:Name="ExportChartButton" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="تصدير المخطط" 
                          Click="ExportChartButton_Click" 
                          Margin="8,0,0,0">
                        <materialDesign:PackIcon Kind="Download" Width="18" Height="18"/>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- Chart Controls -->
            <Grid Grid.Row="1" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="الفترة:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ComboBox Grid.Column="1" x:Name="ChartPeriodComboBox" 
                        Width="150" HorizontalAlignment="Left"
                        SelectionChanged="ChartPeriodComboBox_SelectionChanged">
                    <ComboBoxItem Content="آخر 3 أشهر"/>
                    <ComboBoxItem Content="آخر 6 أشهر" IsSelected="True"/>
                    <ComboBoxItem Content="آخر 12 شهر"/>
                </ComboBox>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <CheckBox x:Name="ShowTotalAmountCheckBox" 
                            Content="إجمالي المبلغ" 
                            IsChecked="True"
                            Margin="0,0,16,0" 
                            Checked="ChartOptions_Changed" 
                            Unchecked="ChartOptions_Changed"/>
                    <CheckBox x:Name="ShowPaidAmountCheckBox" 
                            Content="المبلغ المسدد" 
                            IsChecked="True"
                            Checked="ChartOptions_Changed" 
                            Unchecked="ChartOptions_Changed"/>
                </StackPanel>
            </Grid>

            <!-- Chart Container -->
            <Border Grid.Row="2" Background="White" CornerRadius="8" 
                  BorderBrush="#E0E0E0" BorderThickness="1">
                <Grid>
                    <!-- Chart -->
                    <lvc:CartesianChart x:Name="MonthlyChart" 
                                      LegendLocation="Bottom" 
                                      Margin="16"
                                      AnimationsSpeed="0:0:0.3" 
                                      Hoverable="True" 
                                      DataTooltip="{x:Null}">
                        <lvc:CartesianChart.AxisX>
                            <lvc:Axis Title="الشهر" FontSize="11" 
                                    Foreground="#666"
                                    FontFamily="Segoe UI">
                                <lvc:Axis.Separator>
                                    <lvc:Separator StrokeThickness="0.5" 
                                                 StrokeDashArray="2"
                                                 Stroke="#E0E0E0"/>
                                </lvc:Axis.Separator>
                            </lvc:Axis>
                        </lvc:CartesianChart.AxisX>
                        <lvc:CartesianChart.AxisY>
                            <lvc:Axis Title="المبلغ (د.ع)" FontSize="11" 
                                    Foreground="#666"
                                    LabelFormatter="{Binding YFormatter}" 
                                    FontFamily="Segoe UI">
                                <lvc:Axis.Separator>
                                    <lvc:Separator StrokeThickness="0.5" 
                                                 StrokeDashArray="4"
                                                 Stroke="#E0E0E0"/>
                                </lvc:Axis.Separator>
                            </lvc:Axis>
                        </lvc:CartesianChart.AxisY>
                    </lvc:CartesianChart>

                    <!-- Loading Indicator -->
                    <Grid x:Name="ChartLoadingIndicator" 
                        Background="#F0FFFFFF" 
                        Visibility="Collapsed">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar IsIndeterminate="True" 
                                       Width="100" Height="4"
                                       Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                            <TextBlock Text="جاري تحميل البيانات..." 
                                     Margin="0,8,0,0"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </Grid>

                    <!-- No Data Message -->
                    <StackPanel x:Name="NoDataPanel" 
                              HorizontalAlignment="Center" 
                              VerticalAlignment="Center"
                              Visibility="Collapsed">
                        <materialDesign:PackIcon Kind="ChartLineVariant" 
                                               Width="48" Height="48" 
                                               Foreground="#CCC" Margin="0,0,0,8"/>
                        <TextBlock Text="لا توجد بيانات لعرضها" 
                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                 Foreground="#999" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </materialDesign:Card>
</UserControl>
