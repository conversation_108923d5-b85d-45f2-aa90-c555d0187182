using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.RegularExpressions;

namespace HR_InvoiceArchiver.Services
{
    public interface IValidationService
    {
        CustomValidationResult ValidateModel<T>(T model) where T : class;
        bool IsValidEmail(string email);
        bool IsValidPhoneNumber(string phoneNumber);
        bool IsValidAmount(decimal amount);
        bool IsValidInvoiceNumber(string invoiceNumber);
        bool IsValidReceiptNumber(string receiptNumber);
        string SanitizeInput(string input);
        bool IsValidDate(DateTime date);
        bool IsValidDateRange(DateTime startDate, DateTime endDate);
    }

    public class ValidationService : IValidationService
    {
        public CustomValidationResult ValidateModel<T>(T model) where T : class
        {
            var context = new ValidationContext(model);
            var results = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

            bool isValid = Validator.TryValidateObject(model, context, results, true);

            return new CustomValidationResult
            {
                IsValid = isValid,
                Errors = results.Select(r => r.ErrorMessage ?? "خطأ في التحقق").ToList()
            };
        }

        public bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return true; // Email is optional in most cases

            try
            {
                var emailAttribute = new EmailAddressAttribute();
                return emailAttribute.IsValid(email);
            }
            catch
            {
                return false;
            }
        }

        public bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return true; // Phone is optional in most cases

            // Allow international format, local format, and common separators
            var phoneRegex = new Regex(@"^[\+]?[0-9\-\s\(\)]{7,20}$");
            return phoneRegex.IsMatch(phoneNumber);
        }

        public bool IsValidAmount(decimal amount)
        {
            return amount >= 0 && amount <= 999999999.99m;
        }

        public bool IsValidInvoiceNumber(string invoiceNumber)
        {
            if (string.IsNullOrWhiteSpace(invoiceNumber))
                return false;

            // Allow alphanumeric characters, hyphens, and underscores
            var invoiceRegex = new Regex(@"^[a-zA-Z0-9\-_]{1,50}$");
            return invoiceRegex.IsMatch(invoiceNumber);
        }

        public bool IsValidReceiptNumber(string receiptNumber)
        {
            if (string.IsNullOrWhiteSpace(receiptNumber))
                return false;

            // Allow alphanumeric characters, hyphens, and underscores
            var receiptRegex = new Regex(@"^[a-zA-Z0-9\-_]{1,50}$");
            return receiptRegex.IsMatch(receiptNumber);
        }

        public string SanitizeInput(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // Remove potentially dangerous characters
            var sanitized = input.Trim();
            
            // Remove HTML tags
            sanitized = Regex.Replace(sanitized, @"<[^>]*>", string.Empty);
            
            // Remove script tags and their content
            sanitized = Regex.Replace(sanitized, @"<script[^>]*>.*?</script>", string.Empty, RegexOptions.IgnoreCase);
            
            // Remove SQL injection patterns
            var sqlPatterns = new[]
            {
                @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)",
                @"(\b(AND|OR)\b.{1,6}?(=|>|<|\!=|<>|<=|>=))",
                @"(\b(AND|OR)\b.{1,6}?\b(IS( +NOT)?( +NULL)?|(NOT( +)?)?IN|EXISTS|BETWEEN|LIKE|GLOB|MATCH|REGEXP)\b)",
                @"(\b(HAVING|GROUP BY|ORDER BY|LIMIT)\b)",
                @"(\b(CASE|WHEN|THEN|ELSE|END)\b)",
                @"(\*|\%|;|--|\||\|\||&&)"
            };

            foreach (var pattern in sqlPatterns)
            {
                sanitized = Regex.Replace(sanitized, pattern, string.Empty, RegexOptions.IgnoreCase);
            }

            return sanitized;
        }

        public bool IsValidDate(DateTime date)
        {
            // Check if date is within reasonable range
            var minDate = new DateTime(1900, 1, 1);
            var maxDate = DateTime.Now.AddYears(10);
            
            return date >= minDate && date <= maxDate;
        }

        public bool IsValidDateRange(DateTime startDate, DateTime endDate)
        {
            return IsValidDate(startDate) && 
                   IsValidDate(endDate) && 
                   startDate <= endDate &&
                   (endDate - startDate).TotalDays <= 3650; // Max 10 years range
        }
    }

    public class CustomValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        
        public string GetErrorsAsString()
        {
            return string.Join(Environment.NewLine, Errors);
        }
    }
}
