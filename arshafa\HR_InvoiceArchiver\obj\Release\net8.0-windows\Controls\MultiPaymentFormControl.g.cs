﻿#pragma checksum "..\..\..\..\Controls\MultiPaymentFormControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "48A97FD7FDABE4DF31FDFE34478C64B8F8537EB6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Controls {
    
    
    /// <summary>
    /// MultiPaymentFormControl
    /// </summary>
    public partial class MultiPaymentFormControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 175 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectAllButton;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAllButton;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer InvoicesScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl InvoicesItemsControl;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PaymentSummaryPanel;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoicesCountText;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountPercentageTextBox;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiscountAmountText;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FinalAmountText;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PaymentDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReceiptNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PaymentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachmentPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/controls/multipaymentformcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 204 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.SupplierComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SupplierComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SelectAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.SelectAllButton.Click += new System.Windows.RoutedEventHandler(this.SelectAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ClearAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 229 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.ClearAllButton.Click += new System.Windows.RoutedEventHandler(this.ClearAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.InvoicesScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 6:
            this.InvoicesItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 8:
            this.PaymentSummaryPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.InvoicesCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.DiscountPercentageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 318 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.DiscountPercentageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.DiscountPercentage_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 323 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SmartDiscountButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DiscountAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.FinalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.PaymentDetailsPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.ReceiptNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.PaymentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 18:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 19:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.AttachmentPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            
            #line 403 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 434 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 440 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 247 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Checked += new System.Windows.RoutedEventHandler(this.InvoiceCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 248 "..\..\..\..\Controls\MultiPaymentFormControl.xaml"
            ((System.Windows.Controls.CheckBox)(target)).Unchecked += new System.Windows.RoutedEventHandler(this.InvoiceCheckBox_Changed);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

