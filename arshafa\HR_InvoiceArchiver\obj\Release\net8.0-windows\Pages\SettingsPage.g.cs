﻿#pragma checksum "..\..\..\..\Pages\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E79F3174E24DCC005B9064F51E28644C1F49028D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportButton;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl SettingsTabControl;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ApplicationNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableSoundsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabasePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseDatabaseButton;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableDatabaseBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableDatabaseEncryptionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxBackupFilesTextBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableCloudSyncCheckBox;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CloudProviderComboBox;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SyncIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CloudCredentialsPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseCredentialsButton;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SyncOnStartupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 359 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SyncOnShutdownCheckBox;
        
        #line default
        #line hidden
        
        
        #line 401 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAuditLogCheckBox;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableDataEncryptionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SessionTimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxLoginAttemptsTextBox;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RequirePasswordOnStartupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LockoutDurationTextBox;
        
        #line default
        #line hidden
        
        
        #line 473 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnablePerformanceMonitoringCheckBox;
        
        #line default
        #line hidden
        
        
        #line 479 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableCachingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 485 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxLogEntriesTextBox;
        
        #line default
        #line hidden
        
        
        #line 491 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogRetentionDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 497 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CacheExpirationTextBox;
        
        #line default
        #line hidden
        
        
        #line 503 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLazyLoadingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 534 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid SystemInfoGrid;
        
        #line default
        #line hidden
        
        
        #line 554 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 564 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupNowButton;
        
        #line default
        #line hidden
        
        
        #line 575 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 585 "..\..\..\..\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ImportButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Pages\SettingsPage.xaml"
            this.ImportButton.Click += new System.Windows.RoutedEventHandler(this.ImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\Pages\SettingsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Pages\SettingsPage.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SettingsTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 5:
            this.ApplicationNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CompanyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.CompanyAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.CompanyPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.CompanyEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.EnableNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.EnableSoundsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.DatabasePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.BrowseDatabaseButton = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\Pages\SettingsPage.xaml"
            this.BrowseDatabaseButton.Click += new System.Windows.RoutedEventHandler(this.BrowseDatabaseButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.EnableDatabaseBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.EnableDatabaseEncryptionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.BackupIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.MaxBackupFilesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.EnableCloudSyncCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.CloudProviderComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.SyncIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.CloudCredentialsPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.BrowseCredentialsButton = ((System.Windows.Controls.Button)(target));
            
            #line 348 "..\..\..\..\Pages\SettingsPage.xaml"
            this.BrowseCredentialsButton.Click += new System.Windows.RoutedEventHandler(this.BrowseCredentialsButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SyncOnStartupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.SyncOnShutdownCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.EnableAuditLogCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 28:
            this.EnableDataEncryptionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 29:
            this.SessionTimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 30:
            this.MaxLoginAttemptsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.RequirePasswordOnStartupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.LockoutDurationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 33:
            this.EnablePerformanceMonitoringCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 34:
            this.EnableCachingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.MaxLogEntriesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            this.LogRetentionDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 37:
            this.CacheExpirationTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 38:
            this.EnableLazyLoadingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 39:
            this.SystemInfoGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 40:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 557 "..\..\..\..\Pages\SettingsPage.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.BackupNowButton = ((System.Windows.Controls.Button)(target));
            
            #line 566 "..\..\..\..\Pages\SettingsPage.xaml"
            this.BackupNowButton.Click += new System.Windows.RoutedEventHandler(this.BackupNowButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 578 "..\..\..\..\Pages\SettingsPage.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 587 "..\..\..\..\Pages\SettingsPage.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

