# دليل نظام المرفقات - تطبيق أرشيف الفواتير

## نظرة عامة

تم تطوير وتحسين نظام المرفقات في تطبيق أرشيف الفواتير ليوفر إدارة شاملة وآمنة للملفات المرفقة مع المدفوعات والفواتير.

## الميزات الجديدة

### ✅ **المشاكل التي تم حلها:**

1. **إنشاء مجلدات المرفقات تلقائياً** عند بدء التطبيق
2. **توحيد مسارات المرفقات** وإصلاح التضارب بين المسارات المطلقة والنسبية
3. **تحسين واجهة المستخدم** مع إضافة زر معاينة للمرفقات
4. **تحسين التحقق من صحة الملفات** (الحجم، النوع، الوجود)
5. **معالجة أفضل للأخطاء** مع رسائل واضحة للمستخدم

### 🆕 **الميزات المضافة:**

1. **معاينة المرفقات** قبل الحفظ
2. **التحقق من حجم الملف** (حد أقصى 10 ميجابايت)
3. **التحقق من نوع الملف** (أنواع مدعومة محددة)
4. **إدارة محسنة للمسارات** مع دوال مساعدة
5. **اختبارات شاملة** لضمان جودة النظام

## هيكل نظام المرفقات

### مجلدات المرفقات
```
%LocalAppData%/HR_InvoiceArchiver/Attachments/
├── Invoices/          # مرفقات الفواتير
└── Payments/          # مرفقات المدفوعات
```

### أنواع الملفات المدعومة
- **مستندات PDF**: `.pdf`
- **صور**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.tiff`
- **مستندات Word**: `.doc`, `.docx`
- **ملفات نصية**: `.txt`, `.rtf`

### حدود النظام
- **الحد الأقصى لحجم الملف**: 10 ميجابايت
- **أنواع الملفات المحظورة**: ملفات تنفيذية (`.exe`, `.bat`, إلخ)

## كيفية الاستخدام

### إضافة مرفق لمدفوعة جديدة

1. افتح نافذة "إضافة مدفوعة جديدة"
2. في قسم "مرفق الوصل"، اضغط على زر "تصفح"
3. اختر الملف المطلوب
4. استخدم زر "معاينة" لفتح الملف والتأكد منه
5. احفظ المدفوعة

### عرض مرفق مدفوعة موجودة

1. في صفحة المدفوعات، اضغط على "عرض المرفق" للمدفوعة المطلوبة
2. أو استخدم "عرض التفاصيل" ثم اضغط على زر المرفق

### تعديل مرفق مدفوعة

1. افتح نافذة "تعديل المدفوعة"
2. يمكنك:
   - عرض المرفق الحالي باستخدام زر "معاينة"
   - استبدال المرفق بملف جديد
   - إزالة المرفق نهائياً

## الدوال المساعدة الجديدة

### في `FileHelper.cs`

```csharp
// إنشاء مجلدات المرفقات
FileHelper.InitializeAttachmentDirectories();

// التحقق من وجود مرفق
bool exists = FileHelper.AttachmentExists(relativePath, "Payments");

// الحصول على المسار الكامل
string fullPath = FileHelper.GetFullAttachmentPath(relativePath, "Payments");

// فتح مرفق
FileHelper.OpenAttachment(relativePath, "Payments");
```

## معالجة الأخطاء

### رسائل الخطأ الشائعة

| الرسالة | السبب | الحل |
|---------|--------|------|
| "حجم الملف كبير جداً" | الملف أكبر من 10 ميجابايت | اختر ملف أصغر أو ضغط الملف |
| "نوع الملف غير مدعوم" | امتداد الملف غير مسموح | اختر ملف بامتداد مدعوم |
| "الملف غير موجود" | الملف المرفق محذوف أو منقول | أعد رفع الملف |
| "فشل في حفظ الملف" | مشكلة في الأذونات أو المساحة | تحقق من أذونات المجلد والمساحة المتاحة |

## الاختبارات

### تشغيل الاختبارات

```bash
# في Visual Studio
Test Explorer > Run All Tests

# أو باستخدام dotnet CLI
dotnet test HR_InvoiceArchiver.Tests
```

### اختبارات المرفقات المتاحة

1. **اختبار إنشاء المجلدات**: `TestInitializeAttachmentDirectories`
2. **اختبار حفظ المرفقات**: `TestSavePaymentAttachment`
3. **اختبار التحقق من الوجود**: `TestAttachmentExists`
4. **اختبار المسارات**: `TestGetFullAttachmentPath`
5. **اختبار فتح المرفقات**: `TestOpenAttachment_*`
6. **اختبار التحقق من الملفات**: `TestSaveAttachment_*`

## أفضل الممارسات

### للمطورين

1. **استخدم دائماً `FileHelper`** للتعامل مع المرفقات
2. **تحقق من وجود الملف** قبل محاولة فتحه
3. **استخدم المسارات النسبية** في قاعدة البيانات
4. **اختبر جميع سيناريوهات الأخطاء**

### للمستخدمين

1. **استخدم أسماء ملفات واضحة** تصف محتوى المرفق
2. **تأكد من حجم الملف** قبل الرفع
3. **احتفظ بنسخ احتياطية** من الملفات المهمة
4. **استخدم معاينة الملف** للتأكد من صحة المرفق

## التحديثات المستقبلية المقترحة

### ميزات محتملة

1. **ضغط الملفات تلقائياً** للملفات الكبيرة
2. **معاينة مصغرة للصور** في واجهة المستخدم
3. **دعم السحب والإفلات** لرفع الملفات
4. **تشفير المرفقات الحساسة**
5. **نسخ احتياطي تلقائي** للمرفقات

### تحسينات الأداء

1. **تحميل المرفقات بشكل غير متزامن**
2. **ذاكرة تخزين مؤقت للمرفقات المستخدمة بكثرة**
3. **ضغط قاعدة البيانات** لتحسين الأداء

## الدعم والمساعدة

### في حالة مواجهة مشاكل

1. **تحقق من سجل الأخطاء** في التطبيق
2. **تأكد من أذونات المجلدات**
3. **تحقق من المساحة المتاحة** على القرص
4. **أعد تشغيل التطبيق** إذا لزم الأمر

### معلومات تقنية

- **إصدار .NET**: 9.0
- **قاعدة البيانات**: SQLite
- **مكتبات المرفقات**: System.IO, FileHelper مخصص

---

**تم التحديث**: ديسمبر 2024  
**الإصدار**: 2.0  
**المطور**: فريق تطوير نظام أرشيف الفواتير
