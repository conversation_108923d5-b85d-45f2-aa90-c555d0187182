using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using System;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class EncryptionServiceTests
    {
        private readonly EncryptionService _encryptionService;

        public EncryptionServiceTests()
        {
            _encryptionService = new EncryptionService();
        }

        [Fact]
        public async Task EncryptAsync_WithValidText_ShouldReturnEncryptedString()
        {
            // Arrange
            var plainText = "نص للتشفير";

            // Act
            var encrypted = await _encryptionService.EncryptAsync(plainText);

            // Assert
            encrypted.Should().NotBeNullOrEmpty();
            encrypted.Should().NotBe(plainText);
        }

        [Fact]
        public async Task DecryptAsync_WithValidEncryptedText_ShouldReturnOriginalText()
        {
            // Arrange
            var plainText = "نص للتشفير وفك التشفير";
            var encrypted = await _encryptionService.EncryptAsync(plainText);

            // Act
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            decrypted.Should().Be(plainText);
        }

        [Fact]
        public async Task EncryptAsync_WithEmptyString_ShouldReturnEmptyString()
        {
            // Act
            var result = await _encryptionService.EncryptAsync("");

            // Assert
            result.Should().Be("");
        }

        [Fact]
        public async Task DecryptAsync_WithEmptyString_ShouldReturnEmptyString()
        {
            // Act
            var result = await _encryptionService.DecryptAsync("");

            // Assert
            result.Should().Be("");
        }

        [Fact]
        public async Task EncryptAsync_WithCustomKey_ShouldUseCustomKey()
        {
            // Arrange
            var plainText = "نص للتشفير";
            var customKey = "مفتاح مخصص للتشفير";

            // Act
            var encrypted1 = await _encryptionService.EncryptAsync(plainText);
            var encrypted2 = await _encryptionService.EncryptAsync(plainText, customKey);

            // Assert
            encrypted1.Should().NotBe(encrypted2);
        }

        [Fact]
        public async Task CreateHashAsync_WithSameData_ShouldReturnSameHash()
        {
            // Arrange
            var data = "بيانات للتحقق من التطابق";
            var salt = "salt123";

            // Act
            var hash1 = await _encryptionService.CreateHashAsync(data, salt);
            var hash2 = await _encryptionService.CreateHashAsync(data, salt);

            // Assert
            hash1.Should().Be(hash2);
        }

        [Fact]
        public async Task VerifyHashAsync_WithCorrectData_ShouldReturnTrue()
        {
            // Arrange
            var data = "بيانات للتحقق";
            var salt = "salt456";
            var hash = await _encryptionService.CreateHashAsync(data, salt);

            // Act
            var isValid = await _encryptionService.VerifyHashAsync(data, hash, salt);

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public async Task VerifyHashAsync_WithIncorrectData_ShouldReturnFalse()
        {
            // Arrange
            var originalData = "بيانات أصلية";
            var wrongData = "بيانات خاطئة";
            var salt = "salt789";
            var hash = await _encryptionService.CreateHashAsync(originalData, salt);

            // Act
            var isValid = await _encryptionService.VerifyHashAsync(wrongData, hash, salt);

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public async Task GenerateKeyAsync_ShouldReturnValidKey()
        {
            // Act
            var key = await _encryptionService.GenerateKeyAsync();

            // Assert
            key.Should().NotBeNullOrEmpty();
            key.Length.Should().BeGreaterThan(10);
        }

        [Fact]
        public async Task GenerateKeyAsync_WithDifferentSizes_ShouldReturnDifferentLengths()
        {
            // Act
            var key128 = await _encryptionService.GenerateKeyAsync(128);
            var key256 = await _encryptionService.GenerateKeyAsync(256);

            // Assert
            key128.Should().NotBeNullOrEmpty();
            key256.Should().NotBeNullOrEmpty();
            key256.Length.Should().BeGreaterThan(key128.Length);
        }

        [Fact]
        public async Task GenerateSaltAsync_ShouldReturnValidSalt()
        {
            // Act
            var salt = await _encryptionService.GenerateSaltAsync();

            // Assert
            salt.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GenerateSaltAsync_MultipleCalls_ShouldReturnDifferentSalts()
        {
            // Act
            var salt1 = await _encryptionService.GenerateSaltAsync();
            var salt2 = await _encryptionService.GenerateSaltAsync();

            // Assert
            salt1.Should().NotBe(salt2);
        }

        [Theory]
        [InlineData("123456", PasswordStrength.VeryWeak)]
        [InlineData("password", PasswordStrength.VeryWeak)]
        [InlineData("Password123", PasswordStrength.Medium)]
        [InlineData("Password123!", PasswordStrength.Strong)]
        [InlineData("MyVeryStr0ng!P@ssw0rd", PasswordStrength.VeryStrong)]
        public async Task CheckPasswordStrengthAsync_WithVariousPasswords_ShouldReturnCorrectStrength(
            string password, PasswordStrength expectedStrength)
        {
            // Act
            var strength = await _encryptionService.CheckPasswordStrengthAsync(password);

            // Assert
            strength.Should().Be(expectedStrength);
        }

        [Fact]
        public async Task GenerateStrongPasswordAsync_ShouldReturnStrongPassword()
        {
            // Act
            var password = await _encryptionService.GenerateStrongPasswordAsync();

            // Assert
            password.Should().NotBeNullOrEmpty();
            password.Length.Should().BeGreaterOrEqualTo(12);
            
            var strength = await _encryptionService.CheckPasswordStrengthAsync(password);
            strength.Should().BeOneOf(PasswordStrength.Strong, PasswordStrength.VeryStrong);
        }

        [Fact]
        public async Task GenerateStrongPasswordAsync_WithCustomLength_ShouldReturnPasswordWithCorrectLength()
        {
            // Arrange
            var desiredLength = 16;

            // Act
            var password = await _encryptionService.GenerateStrongPasswordAsync(desiredLength);

            // Assert
            password.Length.Should().Be(desiredLength);
        }

        [Fact]
        public async Task GenerateStrongPasswordAsync_WithoutSymbols_ShouldNotContainSymbols()
        {
            // Act
            var password = await _encryptionService.GenerateStrongPasswordAsync(12, false);

            // Assert
            password.Should().NotBeNullOrEmpty();
            password.Should().MatchRegex(@"^[a-zA-Z0-9]+$");
        }

        [Fact]
        public async Task EncryptSensitiveDataAsync_ShouldEncryptData()
        {
            // Arrange
            var sensitiveData = "بيانات حساسة جداً";

            // Act
            var encrypted = await _encryptionService.EncryptSensitiveDataAsync(sensitiveData);

            // Assert
            encrypted.Should().NotBeNullOrEmpty();
            encrypted.Should().NotBe(sensitiveData);
        }

        [Fact]
        public async Task EncryptDecrypt_LargeText_ShouldWorkCorrectly()
        {
            // Arrange
            var largeText = new string('أ', 10000) + "نص كبير للاختبار" + new string('ب', 10000);

            // Act
            var encrypted = await _encryptionService.EncryptAsync(largeText);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            decrypted.Should().Be(largeText);
        }

        [Fact]
        public async Task EncryptDecrypt_SpecialCharacters_ShouldWorkCorrectly()
        {
            // Arrange
            var textWithSpecialChars = "نص يحتوي على رموز خاصة: !@#$%^&*()_+-=[]{}|;':\",./<>?";

            // Act
            var encrypted = await _encryptionService.EncryptAsync(textWithSpecialChars);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            decrypted.Should().Be(textWithSpecialChars);
        }

        [Fact]
        public async Task EncryptDecrypt_UnicodeText_ShouldWorkCorrectly()
        {
            // Arrange
            var unicodeText = "نص عربي مع رموز يونيكود: 🔒🔑🛡️ و أرقام ١٢٣٤٥";

            // Act
            var encrypted = await _encryptionService.EncryptAsync(unicodeText);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            decrypted.Should().Be(unicodeText);
        }
    }
}
