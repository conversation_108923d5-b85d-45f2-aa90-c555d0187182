using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Windows
{
    public partial class PaymentWindow : Window, INotifyPropertyChanged
    {
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<Payment> AllPayments { get; set; } = new();
        public ICollectionView FilteredPayments { get; private set; }

        private Payment? _selectedPayment;
        public Payment? SelectedPayment
        {
            get => _selectedPayment;
            set
            {
                _selectedPayment = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSelectedPayment));
            }
        }

        public bool HasSelectedPayment => SelectedPayment != null;

        private string _searchTerm = string.Empty;
        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                _searchTerm = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public PaymentWindow()
        {
            // Initialize services from DI container
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();

            InitializeComponent();
            DataContext = this;

            // Initialize collection view for filtering
            FilteredPayments = CollectionViewSource.GetDefaultView(AllPayments);
            FilteredPayments.Filter = PaymentFilter;

            // Initialize filter options
            InitializeFilterOptions();

            Loaded += PaymentWindow_Loaded;
        }

        private void InitializeFilterOptions()
        {
            // Add status filter options
            FilterStatusComboBox.Items.Add(new ComboBoxItem { Content = "جميع الحالات", Tag = "" });
            FilterStatusComboBox.Items.Add(new ComboBoxItem { Content = "نقدي", Tag = "Cash" });
            FilterStatusComboBox.Items.Add(new ComboBoxItem { Content = "بطاقة", Tag = "CreditCard" });
            FilterStatusComboBox.SelectedIndex = 0;
        }

        private async void PaymentWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                var payments = await _paymentService.GetAllPaymentsAsync();

                // Ensure UI updates happen on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    AllPayments.Clear();
                    foreach (var payment in payments)
                    {
                        AllPayments.Add(payment);
                    }
                    FilteredPayments.Refresh();
                });

                _toastService.ShowSuccess("نجح العمل", $"تم تحميل {payments.Count()} مدفوعة بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"خطأ في تحميل المدفوعات: {ex.Message}");
            }
        }

        private bool PaymentFilter(object item)
        {
            if (item is not Payment payment) return false;

            // Search term filter
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                if (!payment.ReceiptNumber?.ToLower().Contains(searchLower) == true &&
                    !payment.Invoice?.InvoiceNumber?.ToLower().Contains(searchLower) == true &&
                    !payment.Invoice?.Supplier?.Name?.ToLower().Contains(searchLower) == true &&
                    !payment.Notes?.ToLower().Contains(searchLower) == true)
                {
                    return false;
                }
            }

            // Status filter
            if (FilterStatusComboBox.SelectedItem is ComboBoxItem statusItem &&
                !string.IsNullOrEmpty(statusItem.Tag?.ToString()))
            {
                if (payment.Method.ToString() != statusItem.Tag.ToString())
                    return false;
            }

            // Date filter
            if (FilterDatePicker.SelectedDate.HasValue)
            {
                if (payment.PaymentDate.Date != FilterDatePicker.SelectedDate.Value.Date)
                    return false;
            }

            return true;
        }

        private void ApplyFilters()
        {
            FilteredPayments.Refresh();
        }

        // Event Handlers
        private async void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addEditWindow = new AddEditPaymentWindow();
                if (addEditWindow.ShowDialog() == true)
                {
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"خطأ في فتح نافذة إضافة المدفوعة: {ex.Message}");
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPaymentsAsync();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void FilterStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void FilterDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            FilterStatusComboBox.SelectedIndex = 0;
            FilterDatePicker.SelectedDate = null;
            ApplyFilters();
        }

        private void PaymentsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Selection is handled by binding
        }

        private async void EditPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedPayment == null) return;

            try
            {
                var addEditWindow = new AddEditPaymentWindow(SelectedPayment);
                if (addEditWindow.ShowDialog() == true)
                {
                    await LoadPaymentsAsync();
                    SelectedPayment = null; // Clear selection
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"خطأ في فتح نافذة تعديل المدفوعة: {ex.Message}");
            }
        }

        private async void DeletePaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedPayment == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المدفوعة رقم {SelectedPayment.ReceiptNumber}؟\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _paymentService.DeletePaymentAsync(SelectedPayment.Id);
                    _toastService.ShowSuccess("نجح العمل", "تم حذف المدفوعة بنجاح");
                    await LoadPaymentsAsync();
                    SelectedPayment = null;
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", $"خطأ في حذف المدفوعة: {ex.Message}");
                }
            }
        }

        private void ViewReceiptButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedPayment == null) return;

            try
            {
                // TODO: Implement receipt viewing functionality
                _toastService.ShowInfo("معلومات", "ميزة عرض الوصل ستكون متاحة قريباً");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"خطأ في عرض الوصل: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
