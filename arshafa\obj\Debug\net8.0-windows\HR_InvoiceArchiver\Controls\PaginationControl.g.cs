﻿#pragma checksum "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EA07E15EE93C274635ECE275F3432D4646BB26D6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Controls {
    
    
    /// <summary>
    /// PaginationControl
    /// </summary>
    public partial class PaginationControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 87 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PageSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FirstPageButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousPageButton;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PageNumbersPanel;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextPageButton;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LastPageButton;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageInfoTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TestApp;component/hr_invoicearchiver/controls/paginationcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PageSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 89 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
            this.PageSizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PageSizeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FirstPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
            this.FirstPageButton.Click += new System.Windows.RoutedEventHandler(this.FirstPage_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PreviousPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
            this.PreviousPageButton.Click += new System.Windows.RoutedEventHandler(this.PreviousPage_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PageNumbersPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.NextPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
            this.NextPageButton.Click += new System.Windows.RoutedEventHandler(this.NextPage_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.LastPageButton = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\..\..\HR_InvoiceArchiver\Controls\PaginationControl.xaml"
            this.LastPageButton.Click += new System.Windows.RoutedEventHandler(this.LastPage_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PageInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

