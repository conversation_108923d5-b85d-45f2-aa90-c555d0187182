using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class OptimizedDashboardPage : UserControl
    {
        private readonly IInvoiceService _invoiceService;
        private readonly INavigationService _navigationService;
        private readonly IToastService _toastService;
        private readonly OptimizedDashboardService _dashboardService;
        
        private readonly DispatcherTimer _refreshTimer;
        private bool _isLoading = false;

        public OptimizedDashboardPage(
            IInvoiceService invoiceService,
            INavigationService navigationService,
            IToastService toastService,
            OptimizedDashboardService dashboardService)
        {
            InitializeComponent();

            _invoiceService = invoiceService;
            _navigationService = navigationService;
            _toastService = toastService;
            _dashboardService = dashboardService;

            // Initialize components with services
            StatisticsCards = new Controls.Dashboard.StatisticsCardsControl(_navigationService);
            ChartSection = new Controls.Dashboard.ChartSectionControl(_dashboardService);
            RecentActivities = new Controls.Dashboard.RecentActivitiesControl(
                _invoiceService, _dashboardService, _navigationService);

            // Initialize refresh timer (longer interval for better performance)
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(10) // Reduced frequency
            };
            _refreshTimer.Tick += RefreshTimer_Tick;

            Loaded += OptimizedDashboardPage_Loaded;
        }

        private async void OptimizedDashboardPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDashboardData();
            _refreshTimer.Start();
        }

        public async Task LoadDashboardData()
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                ShowLoadingIndicator(true);

                // Load data in parallel for better performance
                var statisticsTask = _dashboardService.GetDashboardStatisticsAsync();
                var recentInvoicesTask = RecentActivities.LoadRecentInvoices(5);
                var alertsTask = RecentActivities.LoadAlerts();
                var chartTask = ChartSection.LoadChartData();

                // Wait for statistics first (most important)
                var statistics = await statisticsTask;
                await StatisticsCards.UpdateStatistics(statistics);
                UpdateStatusDistribution(statistics);

                // Wait for other tasks
                await Task.WhenAll(recentInvoicesTask, alertsTask, chartTask);

                UpdateWelcomeMessage();
                UpdateLastUpdateTime();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل البيانات", ex.Message);
            }
            finally
            {
                _isLoading = false;
                ShowLoadingIndicator(false);
            }
        }

        private void UpdateStatusDistribution(DashboardStatistics statistics)
        {
            try
            {
                UnpaidCountText.Text = statistics.UnpaidCount.ToString("N0");
                PartiallyPaidCountText.Text = statistics.PartiallyPaidCount.ToString("N0");
                PaidCountText.Text = statistics.PaidCount.ToString("N0");
                PaymentRateText.Text = $"{statistics.PaymentRate:F1}%";
                PaymentRateProgress.Value = statistics.PaymentRate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating status distribution: {ex.Message}");
            }
        }

        private void UpdateWelcomeMessage()
        {
            try
            {
                var hour = DateTime.Now.Hour;
                var greeting = hour switch
                {
                    < 12 => "صباح الخير",
                    < 17 => "مساء الخير",
                    _ => "مساء الخير"
                };

                WelcomeText.Text = $"{greeting} - لوحة التحكم";
                WelcomeSubText.Text = "نظرة شاملة على حالة الفواتير والمدفوعات";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating welcome message: {ex.Message}");
            }
        }

        private void UpdateLastUpdateTime()
        {
            LastUpdateText.Text = $"آخر تحديث: {DateTime.Now:HH:mm}";
        }

        private void ShowLoadingIndicator(bool show)
        {
            LoadingOverlay.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            
            if (show)
            {
                var storyboard = (Storyboard)Resources["LoadingAnimation"];
                storyboard.Begin();
            }
        }

        private async void RefreshTimer_Tick(object? sender, EventArgs e)
        {
            await LoadDashboardData();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear cache for fresh data
            _dashboardService.ClearCache();
            await LoadDashboardData();
        }

        // Navigation event handlers
        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(InvoicesPage), "add");
        }

        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(PaymentsPage), "add");
        }

        private void ViewReportsButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(InvoicesPage), "reports");
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(SearchPage), "");
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
        }
    }
}
