﻿#pragma checksum "..\..\..\..\Windows\SupplierWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8C1FF2109385EACCC091ECB05C78E7A17770CF41"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Windows {
    
    
    /// <summary>
    /// SupplierWindow
    /// </summary>
    public partial class SupplierWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 140 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickAddButton;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FormHeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContactPersonTextBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaxNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteButton;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddNewButton;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SuppliersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSuppliersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveSuppliersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\Windows\SupplierWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/windows/supplierwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\SupplierWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.QuickAddButton = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.QuickAddButton.Click += new System.Windows.RoutedEventHandler(this.AddNewButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FormHeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.ContactPersonTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.AddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.TaxNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 203 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 205 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.DeleteButton.Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SupplierCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 236 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.AddNewButton = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.AddNewButton.Click += new System.Windows.RoutedEventHandler(this.AddNewButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SuppliersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 264 "..\..\..\..\Windows\SupplierWindow.xaml"
            this.SuppliersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SuppliersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TotalSuppliersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ActiveSuppliersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.TotalInvoicesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TotalAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 17:
            
            #line 278 "..\..\..\..\Windows\SupplierWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditButton_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 281 "..\..\..\..\Windows\SupplierWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StatementButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

