<UserControl x:Class="HR_InvoiceArchiver.Pages.ImportExportPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="SwapHorizontal" 
                                       Width="32" Height="32" 
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="التصدير والاستيراد" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="تصدير واستيراد البيانات بصيغ مختلفة" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="HistoryButton" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="HistoryButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="History" Margin="0,0,8,0"/>
                            <TextBlock Text="السجل"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="CleanupButton" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="CleanupButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Margin="0,0,8,0"/>
                            <TextBlock Text="تنظيف"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <TabControl Grid.Row="1" 
                  Style="{StaticResource MaterialDesignTabControl}"
                  Margin="16,0,16,16">

            <!-- Export Tab -->
            <TabItem Header="التصدير">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Margin="0,0,8,0"/>
                            <TextBlock Text="التصدير"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                    <StackPanel>
                        <!-- Export Options -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="خيارات التصدير" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Data Type -->
                                    <ComboBox x:Name="ExportDataTypeComboBox"
                                            Grid.Row="0" Grid.Column="0"
                                            materialDesign:HintAssist.Hint="نوع البيانات"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="0,0,8,16">
                                        <ComboBoxItem Content="الفواتير" Tag="Invoices"/>
                                        <ComboBoxItem Content="الموردين" Tag="Suppliers"/>
                                        <ComboBoxItem Content="المدفوعات" Tag="Payments"/>
                                        <ComboBoxItem Content="جميع البيانات" Tag="All"/>
                                    </ComboBox>

                                    <!-- Export Format -->
                                    <ComboBox x:Name="ExportFormatComboBox"
                                            Grid.Row="0" Grid.Column="1"
                                            materialDesign:HintAssist.Hint="صيغة التصدير"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="8,0,0,16">
                                        <ComboBoxItem Content="Excel (.xlsx)" Tag="Excel"/>
                                        <ComboBoxItem Content="CSV (.csv)" Tag="CSV"/>
                                        <ComboBoxItem Content="JSON (.json)" Tag="JSON"/>
                                        <ComboBoxItem Content="XML (.xml)" Tag="XML"/>
                                    </ComboBox>

                                    <!-- Date Range -->
                                    <DatePicker x:Name="ExportStartDatePicker"
                                              Grid.Row="1" Grid.Column="0"
                                              materialDesign:HintAssist.Hint="من تاريخ"
                                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                              Margin="0,0,8,16"/>

                                    <DatePicker x:Name="ExportEndDatePicker"
                                              Grid.Row="1" Grid.Column="1"
                                              materialDesign:HintAssist.Hint="إلى تاريخ"
                                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                              Margin="8,0,0,16"/>

                                    <!-- File Path -->
                                    <Grid Grid.Row="2" Grid.ColumnSpan="2" Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBox x:Name="ExportFilePathTextBox"
                                               Grid.Column="0"
                                               materialDesign:HintAssist.Hint="مسار الملف (اختياري)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,0"/>
                                        
                                        <Button x:Name="BrowseExportPathButton"
                                              Grid.Column="1"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="BrowseExportPathButton_Click">
                                            <materialDesign:PackIcon Kind="FolderOpen"/>
                                        </Button>
                                    </Grid>

                                    <!-- Options -->
                                    <StackPanel Grid.Row="3" Grid.ColumnSpan="2" Orientation="Horizontal">
                                        <CheckBox x:Name="IncludeAttachmentsCheckBox"
                                                Content="تضمين المرفقات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,16,0"/>

                                        <CheckBox x:Name="CompressOutputCheckBox"
                                                Content="ضغط الملف"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,16,0"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Export Actions -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="إجراءات التصدير" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button x:Name="CreateTemplateButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,8,0"
                                          Click="CreateTemplateButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileDocument" Margin="0,0,8,0"/>
                                            <TextBlock Text="إنشاء قالب"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="ExportButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Margin="8,0,0,0"
                                          Click="ExportButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Export" Margin="0,0,8,0"/>
                                            <TextBlock Text="تصدير"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Import Tab -->
            <TabItem Header="الاستيراد">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Import" Margin="0,0,8,0"/>
                            <TextBlock Text="الاستيراد"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                    <StackPanel>
                        <!-- Import File Selection -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="اختيار الملف" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBox x:Name="ImportFilePathTextBox"
                                           Grid.Column="0"
                                           materialDesign:HintAssist.Hint="مسار الملف"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           IsReadOnly="True"
                                           Margin="0,0,8,0"/>
                                    
                                    <Button x:Name="BrowseImportFileButton"
                                          Grid.Column="1"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,8,0"
                                          Click="BrowseImportFileButton_Click">
                                        <materialDesign:PackIcon Kind="FolderOpen"/>
                                    </Button>

                                    <Button x:Name="ValidateFileButton"
                                          Grid.Column="2"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Click="ValidateFileButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="CheckCircle" Margin="0,0,8,0"/>
                                            <TextBlock Text="تحقق"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Import Options -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="خيارات الاستيراد" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Import Format -->
                                    <ComboBox x:Name="ImportFormatComboBox"
                                            Grid.Row="0" Grid.Column="0"
                                            materialDesign:HintAssist.Hint="صيغة الاستيراد"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="0,0,8,16">
                                        <ComboBoxItem Content="Excel (.xlsx)" Tag="Excel"/>
                                        <ComboBoxItem Content="CSV (.csv)" Tag="CSV"/>
                                        <ComboBoxItem Content="JSON (.json)" Tag="JSON"/>
                                        <ComboBoxItem Content="XML (.xml)" Tag="XML"/>
                                    </ComboBox>

                                    <!-- Import Mode -->
                                    <ComboBox x:Name="ImportModeComboBox"
                                            Grid.Row="0" Grid.Column="1"
                                            materialDesign:HintAssist.Hint="نمط الاستيراد"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="8,0,0,16">
                                        <ComboBoxItem Content="إدراج فقط" Tag="Insert"/>
                                        <ComboBoxItem Content="تحديث فقط" Tag="Update"/>
                                        <ComboBoxItem Content="إدراج أو تحديث" Tag="Upsert"/>
                                    </ComboBox>

                                    <!-- Start Row -->
                                    <TextBox x:Name="StartRowTextBox"
                                           Grid.Row="1" Grid.Column="0"
                                           materialDesign:HintAssist.Hint="بداية الصف"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Text="2"
                                           Margin="0,0,8,16"/>

                                    <!-- Options -->
                                    <StackPanel Grid.Row="2" Grid.ColumnSpan="2" Orientation="Horizontal">
                                        <CheckBox x:Name="ValidateDataCheckBox"
                                                Content="التحقق من البيانات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"
                                                Margin="0,0,16,0"/>

                                        <CheckBox x:Name="SkipDuplicatesCheckBox"
                                                Content="تخطي المكررات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"
                                                Margin="0,0,16,0"/>

                                        <CheckBox x:Name="CreateBackupCheckBox"
                                                Content="إنشاء نسخة احتياطية"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Preview -->
                        <materialDesign:Card x:Name="PreviewCard" Padding="16" Visibility="Collapsed">
                            <StackPanel>
                                <TextBlock Text="معاينة البيانات" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <DataGrid x:Name="PreviewDataGrid"
                                        Style="{StaticResource MaterialDesignDataGrid}"
                                        AutoGenerateColumns="True"
                                        IsReadOnly="True"
                                        MaxHeight="200"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Import Actions -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="إجراءات الاستيراد" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button x:Name="PreviewButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,8,0"
                                          Click="PreviewButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Eye" Margin="0,0,8,0"/>
                                            <TextBlock Text="معاينة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="ImportButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Margin="8,0,0,0"
                                          Click="ImportButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Import" Margin="0,0,8,0"/>
                                            <TextBlock Text="استيراد"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Loading Indicator -->
        <Grid Grid.RowSpan="2" x:Name="LoadingGrid" 
              Background="{DynamicResource MaterialDesignPaper}" 
              Opacity="0.8" 
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"
                           Margin="0,0,0,16"/>
                <TextBlock x:Name="LoadingTextBlock" 
                         Text="جاري المعالجة..."
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
