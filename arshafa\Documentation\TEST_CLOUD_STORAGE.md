# اختبار التخزين السحابي

## خطوات الاختبار:

### 1. تشغيل البرنامج
```bash
dotnet run
```

### 2. فتح نافذة التخزين السحابي
- انقر على زر "التخزين السحابي" في الشريط العلوي أو الجانبي

### 3. اختبار الزر
- يجب أن تظهر نافذة جديدة
- يجب أن تحتوي على UserControl مع الأزرار
- انقر على زر "ربط Google Drive"

### 4. النتائج المتوقعة:
- إذا لم يكن ملف credentials.json موجود: رسالة خطأ واضحة
- إذا كان الملف موجود: فتح متصفح للمصادقة
- بعد المصادقة: تحديث الواجهة لتظهر حالة "متصل"

### 5. اختبار عرض الملفات:
- بعد الاتصال الناجح
- يجب أن تظهر الإحصائيات
- يجب أن تظهر قائمة الملفات المتزامنة

## المشاكل المحتملة:

### إذا لم يعمل الزر:
1. تحقق من وجود ملف credentials.json
2. تحقق من إضافة البريد الإلكتروني في Test users
3. تحقق من تفعيل Google Drive API

### إذا ظهرت أخطاء:
- راجع رسائل الخطأ في النافذة
- تحقق من اتصال الإنترنت
- تأكد من صحة ملف credentials.json
