using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Controls
{
    public partial class InvoiceFormOverlay : UserControl
    {
        public event EventHandler<InvoiceFormEventArgs>? FormClosed;

        public InvoiceFormOverlay()
        {
            InitializeComponent();
        }

        public void ShowAddInvoiceForm()
        {
            // Remove existing form if any
            OverlayContainer.Children.Clear();

            // Create new form
            var newForm = new InvoiceFormControl();
            newForm.FormClosed += InvoiceForm_FormClosed;
            OverlayContainer.Children.Add(newForm);

            ShowOverlay();
        }

        public void ShowEditInvoiceForm(Invoice invoice)
        {
            // Remove existing form if any
            OverlayContainer.Children.Clear();

            // Create new form with invoice data
            var newForm = new InvoiceFormControl(invoice);
            newForm.FormClosed += InvoiceForm_FormClosed;
            OverlayContainer.Children.Add(newForm);

            ShowOverlay();
        }

        private void ShowOverlay()
        {
            Visibility = Visibility.Visible;
            var storyboard = (Storyboard)Resources["FadeInAnimation"];
            storyboard.Begin(this);
        }

        private void HideOverlay()
        {
            var storyboard = (Storyboard)Resources["FadeOutAnimation"];
            storyboard.Begin(this);
        }

        private void InvoiceForm_FormClosed(object? sender, InvoiceFormEventArgs e)
        {
            FormClosed?.Invoke(this, e);
            HideOverlay();
        }

        private void FadeOutAnimation_Completed(object? sender, EventArgs e)
        {
            Visibility = Visibility.Collapsed;
        }
    }
}
