﻿#pragma checksum "..\..\..\..\Windows\AddEditInvoiceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4B8DE03F98E41DCC4EB036C8C24DAB0D2EF6D0DD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Converters;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Windows {
    
    
    /// <summary>
    /// AddEditInvoiceWindow
    /// </summary>
    public partial class AddEditInvoiceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 67 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker InvoiceDatePicker;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TotalAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DueDatePicker;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachmentPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AttachmentPreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AttachmentNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidationMessageTextBlock;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/windows/addeditinvoicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WindowTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 71 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.InvoiceDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.SupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.TotalAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.DueDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.AttachmentPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            
            #line 187 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AttachmentPreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.AttachmentNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            
            #line 215 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ValidationMessageTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            
            #line 243 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\..\Windows\AddEditInvoiceWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

