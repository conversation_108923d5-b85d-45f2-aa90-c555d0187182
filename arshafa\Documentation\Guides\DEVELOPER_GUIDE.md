# دليل المطور - نظام أرشفة فواتير الموارد البشرية

## نظرة عامة على المشروع

نظام أرشفة فواتير الموارد البشرية (HR Invoice Archiver) هو تطبيق WPF مبني بتقنية .NET 8 يستخدم Entity Framework Core لإدارة قاعدة البيانات و Material Design للواجهة.

### التقنيات المستخدمة

- **.NET 8**: الإطار الأساسي للتطبيق
- **WPF (Windows Presentation Foundation)**: تقنية الواجهة
- **Entity Framework Core**: لإدارة قاعدة البيانات
- **Material Design in XAML**: للتصميم الحديث
- **Google Drive API**: للنسخ الاحتياطي السحابي

## نظام التنبيهات والتصميم الحديث

## نظام التنبيهات (Toast Notifications)

### إعداد الخدمة
```csharp
// في App.xaml.cs
services.AddSingleton<IToastService, ToastService>();
```

### استخدام التنبيهات في النوافذ
```csharp
public partial class MyWindow : Window
{
    private readonly IToastService _toastService;

    public MyWindow()
    {
        _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
        InitializeComponent();
        
        // ربط حاوي التنبيهات
        _toastService.SetContainer(ToastContainer);
    }
}
```

### أنواع التنبيهات

#### تنبيه النجاح
```csharp
_toastService.ShowSuccess("تم بنجاح", "تم حفظ البيانات بنجاح");
```

#### تنبيه الخطأ
```csharp
_toastService.ShowError("خطأ", "فشل في حفظ البيانات");
```

#### تنبيه المعلومات
```csharp
_toastService.ShowInfo("معلومات", "يرجى ملء جميع الحقول المطلوبة");
```

#### تنبيه التحذير
```csharp
_toastService.ShowWarning("تحذير", "هذا الإجراء لا يمكن التراجع عنه");
```

## Material Design Components

### البطاقات (Cards)
```xml
<materialDesign:Card Margin="8" 
                     materialDesign:ElevationAssist.Elevation="Dp2">
    <Grid Margin="16">
        <!-- محتوى البطاقة -->
    </Grid>
</materialDesign:Card>
```

### الأزرار
```xml
<!-- زر أساسي -->
<Button Style="{StaticResource MaterialDesignRaisedButton}"
        materialDesign:ButtonAssist.CornerRadius="8"
        Background="#1976D2">
    <StackPanel Orientation="Horizontal">
        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
        <TextBlock Text="إضافة" Margin="8,0,0,0"/>
    </StackPanel>
</Button>

<!-- زر أيقونة -->
<Button Style="{StaticResource MaterialDesignIconButton}">
    <materialDesign:PackIcon Kind="Settings"/>
</Button>
```

### شريط الألوان (ColorZone)
```xml
<materialDesign:ColorZone Mode="PrimaryMid"
                          Padding="16"
                          materialDesign:ElevationAssist.Elevation="Dp4">
    <!-- محتوى الشريط -->
</materialDesign:ColorZone>
```

### جدول البيانات
```xml
<DataGrid Style="{StaticResource MaterialDesignDataGrid}"
          materialDesign:DataGridAssist.CellPadding="8"
          materialDesign:DataGridAssist.ColumnHeaderPadding="8">
    <!-- أعمدة الجدول -->
</DataGrid>
```

## الألوان والأنماط

### الألوان الأساسية
```xml
<SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
<SolidColorBrush x:Key="SecondaryBrush" Color="#424242"/>
<SolidColorBrush x:Key="AccentBrush" Color="#4CAF50"/>
<SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
<SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
<SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
```

### أنماط النصوص
```xml
<!-- عنوان رئيسي -->
<TextBlock Style="{StaticResource MaterialDesignHeadline4TextBlock}"/>

<!-- عنوان فرعي -->
<TextBlock Style="{StaticResource MaterialDesignHeadline6TextBlock}"/>

<!-- نص عادي -->
<TextBlock Style="{StaticResource MaterialDesignBody1TextBlock}"/>
```

## أفضل الممارسات

### 1. استخدام التنبيهات
- استخدم تنبيهات النجاح للعمليات المكتملة بنجاح
- استخدم تنبيهات الخطأ للأخطاء الحرجة
- استخدم تنبيهات المعلومات للإرشادات
- استخدم تنبيهات التحذير للتأكيدات

### 2. تصميم البطاقات
- استخدم مسافات مناسبة (16dp للحواف الداخلية)
- استخدم ارتفاعات مختلفة للتسلسل الهرمي
- تجنب الإفراط في استخدام الظلال

### 3. الأيقونات
- استخدم أيقونات Material Design فقط
- حافظ على حجم موحد للأيقونات (16dp أو 24dp)
- استخدم أيقونات واضحة ومفهومة

### 4. الألوان
- التزم بنظام الألوان المحدد
- استخدم التباين المناسب للنصوص
- تجنب استخدام ألوان متضاربة

## معالجة الأخطاء

### في التنبيهات
```csharp
try
{
    // عملية قد تفشل
    await SomeOperation();
    _toastService.ShowSuccess("نجح", "تمت العملية بنجاح");
}
catch (Exception ex)
{
    _toastService.ShowError("خطأ", ex.Message);
}
```

### في واجهة المستخدم
```csharp
private void Button_Click(object sender, RoutedEventArgs e)
{
    try
    {
        // منطق الزر
    }
    catch (Exception ex)
    {
        _toastService.ShowError("خطأ في العملية", ex.Message);
    }
}
```

## التخصيص

### تخصيص ألوان التنبيهات
```csharp
// في ToastNotification.xaml.cs
private Brush GetBackgroundBrush(ToastType type)
{
    return type switch
    {
        ToastType.Success => new SolidColorBrush(Color.FromRgb(76, 175, 80)),
        ToastType.Error => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
        ToastType.Warning => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
        ToastType.Info => new SolidColorBrush(Color.FromRgb(33, 150, 243)),
        _ => new SolidColorBrush(Colors.Gray)
    };
}
```

### تخصيص مدة العرض
```csharp
// عرض لمدة 10 ثوان
_toastService.ShowSuccess("العنوان", "الرسالة", TimeSpan.FromSeconds(10));
```

## الاختبار

### اختبار التنبيهات
```csharp
[Test]
public void TestToastService()
{
    var toastService = new ToastService();
    var container = new Grid();
    
    toastService.SetContainer(container);
    toastService.ShowSuccess("اختبار", "رسالة اختبار");
    
    Assert.AreEqual(1, container.Children.Count);
}
```

## الأداء

### نصائح لتحسين الأداء
- تجنب إنشاء تنبيهات كثيرة في وقت قصير
- استخدم التنبيهات للمعلومات المهمة فقط
- تأكد من تنظيف الموارد عند إغلاق التنبيهات

### مراقبة الذاكرة
```csharp
// تنظيف التنبيهات المنتهية الصلاحية
private void CleanupExpiredToasts()
{
    var expiredToasts = _activeToasts.Where(t => t.IsExpired).ToList();
    foreach (var toast in expiredToasts)
    {
        RemoveToast(toast);
    }
}
```
