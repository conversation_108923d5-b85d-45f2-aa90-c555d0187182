using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Services;
using System;

namespace HR_InvoiceArchiver.Tests
{
    /// <summary>
    /// إعدادات الاختبارات وحقن التبعيات
    /// </summary>
    public static class TestConfiguration
    {
        /// <summary>
        /// إنشاء مزود الخدمات للاختبارات
        /// </summary>
        public static ServiceProvider CreateTestServiceProvider()
        {
            var services = new ServiceCollection();
            ConfigureTestServices(services);
            return services.BuildServiceProvider();
        }

        /// <summary>
        /// تكوين الخدمات للاختبارات
        /// </summary>
        private static void ConfigureTestServices(IServiceCollection services)
        {
            // Logging للاختبارات
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Warning); // تقليل مستوى السجلات في الاختبارات
            });

            // قاعدة بيانات في الذاكرة للاختبارات
            services.AddDbContext<DatabaseContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

            // المستودعات
            services.AddScoped<IInvoiceRepository, InvoiceRepository>();
            services.AddScoped<ISupplierRepository, SupplierRepository>();
            services.AddScoped<IPaymentRepository, PaymentRepository>();

            // الخدمات
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<IPaymentService, PaymentService>();
            services.AddScoped<IValidationService, ValidationService>();

            // خدمات وهمية للاختبارات
            services.AddScoped<IToastService, MockToastService>();
            services.AddScoped<IErrorHandlingService, MockErrorHandlingService>();
        }

        /// <summary>
        /// إنشاء سياق قاعدة بيانات للاختبارات
        /// </summary>
        public static DatabaseContext CreateTestDbContext()
        {
            var options = new DbContextOptionsBuilder<DatabaseContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            return new DatabaseContext(options);
        }
    }

    /// <summary>
    /// خدمة Toast وهمية للاختبارات
    /// </summary>
    public class MockToastService : IToastService
    {
        public void ShowSuccess(string title, string message, TimeSpan? duration = null) { }
        public void ShowError(string title, string message, TimeSpan? duration = null) { }
        public void ShowWarning(string title, string message, TimeSpan? duration = null) { }
        public void ShowInfo(string title, string message, TimeSpan? duration = null) { }
        public void Clear() { }
        public void SetContainer(System.Windows.Controls.Panel container) { }
    }

    /// <summary>
    /// خدمة معالجة الأخطاء وهمية للاختبارات
    /// </summary>
    public class MockErrorHandlingService : IErrorHandlingService
    {
        public void HandleError(Exception ex, string context, bool showToUser = false) { }
        public void HandleValidationError(string message, string context) { }
        public void HandleCriticalError(Exception ex, string context) { }
        public bool ShouldShowToUser(Exception ex) => false;
    }
}
