<Window x:Class="HR_InvoiceArchiver.Windows.AddEditInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
        Title="إضافة/تعديل فاتورة"
        Height="700" Width="800"
        MinHeight="600" MinWidth="700"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">

    <Window.Resources>
        <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- Card Style -->
        <Style x:Key="FormCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="16,8"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>
        
        <!-- Text Field Style -->
        <Style x:Key="FormTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
        
        <!-- ComboBox Style -->
        <Style x:Key="FormComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
        
        <!-- DatePicker Style -->
        <Style x:Key="FormDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- App Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="FileDocument" Width="24" Height="24" 
                                       VerticalAlignment="Center" Margin="0,0,12,0"/>
                
                <TextBlock x:Name="WindowTitleTextBlock" Grid.Column="1" Text="إضافة فاتورة جديدة" 
                          FontSize="18" FontWeight="Medium" VerticalAlignment="Center"/>
                
                <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" 
                        Click="CloseButton_Click" ToolTip="إغلاق">
                    <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                </Button>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="0,8">
            <StackPanel>
                
                <!-- Basic Information Card -->
                <materialDesign:Card Style="{StaticResource FormCardStyle}">
                    <StackPanel>
                        <TextBlock Text="المعلومات الأساسية" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,0,16"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Invoice Number -->
                            <TextBox x:Name="InvoiceNumberTextBox" Grid.Column="0"
                                    Style="{StaticResource FormTextBoxStyle}"
                                    materialDesign:HintAssist.Hint="رقم الفاتورة *"
                                    Text="{Binding InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"/>
                            
                            <!-- Invoice Date -->
                            <DatePicker x:Name="InvoiceDatePicker" Grid.Column="2"
                                       Style="{StaticResource FormDatePickerStyle}"
                                       materialDesign:HintAssist.Hint="تاريخ الفاتورة *"
                                       SelectedDate="{Binding InvoiceDate, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>
                        
                        <!-- Supplier -->
                        <ComboBox x:Name="SupplierComboBox"
                                 Style="{StaticResource FormComboBoxStyle}"
                                 materialDesign:HintAssist.Hint="المورد *"
                                 ItemsSource="{Binding Suppliers}"
                                 SelectedItem="{Binding SelectedSupplier, UpdateSourceTrigger=PropertyChanged}"
                                 DisplayMemberPath="Name"
                                 SelectedValuePath="Id"/>
                        
                        <!-- Description -->
                        <TextBox x:Name="DescriptionTextBox"
                                Style="{StaticResource FormTextBoxStyle}"
                                materialDesign:HintAssist.Hint="وصف الفاتورة"
                                Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                AcceptsReturn="True"
                                TextWrapping="Wrap"
                                MinLines="3"
                                MaxLines="5"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Financial Information Card -->
                <materialDesign:Card Style="{StaticResource FormCardStyle}">
                    <StackPanel>
                        <TextBlock Text="المعلومات المالية" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,0,16"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Total Amount -->
                            <TextBox x:Name="TotalAmountTextBox" Grid.Column="0"
                                    Style="{StaticResource FormTextBoxStyle}"
                                    materialDesign:HintAssist.Hint="المبلغ الإجمالي (د.ع) *"
                                    Text="{Binding TotalAmount, UpdateSourceTrigger=PropertyChanged}"/>
                            
                            <!-- Due Date -->
                            <DatePicker x:Name="DueDatePicker" Grid.Column="2"
                                       Style="{StaticResource FormDatePickerStyle}"
                                       materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                                       SelectedDate="{Binding DueDate, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>
                        
                        <!-- Status -->
                        <ComboBox x:Name="StatusComboBox"
                                 Style="{StaticResource FormComboBoxStyle}"
                                 materialDesign:HintAssist.Hint="حالة الفاتورة *"
                                 SelectedItem="{Binding Status, UpdateSourceTrigger=PropertyChanged}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Converter={StaticResource StatusToTextConverter}}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Attachments Card -->
                <materialDesign:Card Style="{StaticResource FormCardStyle}">
                    <StackPanel>
                        <TextBlock Text="المرفقات" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,0,16"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="AttachmentPathTextBox" Grid.Column="0"
                                    Style="{StaticResource FormTextBoxStyle}"
                                    materialDesign:HintAssist.Hint="مسار المرفق"
                                    Text="{Binding AttachmentPath, UpdateSourceTrigger=PropertyChanged}"
                                    IsReadOnly="True"/>
                            
                            <Button Grid.Column="1" Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Content="تصفح" Click="BrowseAttachmentButton_Click" 
                                   Margin="8,8,0,8" Padding="16,8">
                                <Button.CommandParameter>
                                    <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16" Margin="0,0,8,0"/>
                                </Button.CommandParameter>
                            </Button>
                        </Grid>
                        
                        <!-- Attachment Preview -->
                        <Border x:Name="AttachmentPreviewBorder" Background="#F5F5F5" 
                               CornerRadius="4" Padding="12" Margin="0,8,0,0"
                               Visibility="{Binding HasAttachment, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <materialDesign:PackIcon Grid.Column="0" Kind="FileDocument" 
                                                       Width="20" Height="20" VerticalAlignment="Center" 
                                                       Margin="0,0,8,0"/>
                                
                                <TextBlock x:Name="AttachmentNameTextBlock" Grid.Column="1" 
                                          Text="{Binding AttachmentName}" 
                                          VerticalAlignment="Center" TextTrimming="CharacterEllipsis"/>
                                
                                <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}"
                                       Click="RemoveAttachmentButton_Click" ToolTip="إزالة المرفق">
                                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                </Button>
                            </Grid>
                        </Border>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="16,8" Padding="16" 
                            materialDesign:ElevationAssist.Elevation="Dp1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Validation Messages -->
                <TextBlock x:Name="ValidationMessageTextBlock" Grid.Column="0" 
                          Text="" Foreground="Red" VerticalAlignment="Center"
                          TextWrapping="Wrap" Visibility="Collapsed"/>
                
                <!-- Cancel Button -->
                <Button Grid.Column="1" Style="{StaticResource MaterialDesignOutlinedButton}"
                       Content="إلغاء" Click="CancelButton_Click" 
                       Margin="0,0,8,0" Padding="24,8" MinWidth="100"/>
                
                <!-- Save Button -->
                <Button x:Name="SaveButton" Grid.Column="2" Style="{StaticResource MaterialDesignRaisedButton}"
                       Content="حفظ" Click="SaveButton_Click" 
                       Padding="24,8" MinWidth="100">
                    <Button.CommandParameter>
                        <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,8,0"/>
                    </Button.CommandParameter>
                </Button>
            </Grid>
        </materialDesign:Card>

    </Grid>
</Window>
