using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;

namespace HR_InvoiceArchiver.Windows
{
    public partial class SupplierWindow : Window, INotifyPropertyChanged
    {
        private readonly ISupplierService _supplierService;
        private readonly IInvoiceService _invoiceService;
        private readonly ISuccessNotificationService _successNotificationService;
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        public ObservableCollection<Supplier> Suppliers { get; set; } = new();
        public ObservableCollection<Supplier> FilteredSuppliers { get; set; } = new();
        
        private Supplier? _currentSupplier;
        private bool _isEditMode = false;
        
        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged(nameof(SearchText));
                FilterSuppliers();
            }
        }
        
        public SupplierWindow()
        {
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _successNotificationService = App.ServiceProvider.GetRequiredService<ISuccessNotificationService>();
            
            InitializeComponent();
            DataContext = this;
            
            Loaded += SupplierWindow_Loaded;
        }
        
        private async void SupplierWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSuppliersAsync();
            UpdateStatistics();
        }
        
        private async Task LoadSuppliersAsync()
        {
            try
            {
                // Ensure UI updates happen on UI thread
                Dispatcher.Invoke(() => StatusTextBlock.Text = "جاري تحميل الموردين...");

                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Clear and populate on UI thread
                await Dispatcher.InvokeAsync(() => Suppliers.Clear());

                foreach (var supplier in suppliers)
                {
                    // Load additional statistics for each supplier
                    var invoices = await _invoiceService.GetInvoicesBySupplierAsync(supplier.Id);
                    var invoicesList = invoices.ToList(); // Convert to list to avoid multiple enumeration
                    supplier.InvoiceCount = invoicesList.Count;
                    supplier.TotalAmount = invoicesList.Sum(i => i.Amount);

                    // Add to collection on UI thread
                    await Dispatcher.InvokeAsync(() => Suppliers.Add(supplier));
                }

                // Update UI on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    FilterSuppliers();
                    UpdateStatistics();
                    StatusTextBlock.Text = $"تم تحميل {Suppliers.Count} مورد";
                });
            }
            catch (Exception ex)
            {
                // Use debug output instead of MessageBox for less intrusive error handling
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الموردين: {ex.Message}");
                StatusTextBlock.Text = "خطأ في التحميل - يرجى المحاولة مرة أخرى";

                // Only show MessageBox for critical errors that require user attention
                if (ex is System.Data.Common.DbException || ex is System.InvalidOperationException)
                {
                    MessageBox.Show($"خطأ في قاعدة البيانات: {ex.Message}", "خطأ حرج",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private void FilterSuppliers()
        {
            FilteredSuppliers.Clear();
            
            var filtered = Suppliers.AsEnumerable();
            
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchTerm = SearchText.ToLower();
                filtered = filtered.Where(s => 
                    s.Name.ToLower().Contains(searchTerm) ||
                    (!string.IsNullOrEmpty(s.ContactPerson) && s.ContactPerson.ToLower().Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(s.Phone) && s.Phone.Contains(searchTerm)) ||
                    (!string.IsNullOrEmpty(s.Email) && s.Email.ToLower().Contains(searchTerm)));
            }
            
            foreach (var supplier in filtered)
            {
                FilteredSuppliers.Add(supplier);
            }
            
            SuppliersDataGrid.ItemsSource = FilteredSuppliers;
            SupplierCountTextBlock.Text = $"{FilteredSuppliers.Count} مورد";
        }
        
        private void UpdateStatistics()
        {
            var totalSuppliers = Suppliers.Count;
            var activeSuppliers = Suppliers.Count(s => s.IsActive);
            var totalInvoices = Suppliers.Sum(s => s.InvoiceCount);
            var totalAmount = Suppliers.Sum(s => s.TotalAmount);
            
            TotalSuppliersTextBlock.Text = $"إجمالي الموردين: {totalSuppliers}";
            ActiveSuppliersTextBlock.Text = $"الموردين النشطين: {activeSuppliers}";
            TotalInvoicesTextBlock.Text = $"إجمالي الفواتير: {totalInvoices}";
            TotalAmountTextBlock.Text = $"إجمالي المبلغ: {CurrencyHelper.FormatForStats(totalAmount)}";
        }
        
        private void AddNewButton_Click(object sender, RoutedEventArgs e)
        {
            ShowSupplierForm();
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                ShowSupplierForm(supplier);
            }
        }

        private void ShowSupplierForm(Supplier? supplier = null)
        {
            try
            {
                var supplierForm = new Controls.SupplierFormControl
                {
                    CurrentSupplier = supplier
                };

                supplierForm.FormClosed += SupplierForm_FormClosed;

                AddSupplierFormToMainWindow(supplierForm);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فتح نموذج المورد: {ex.Message}");
                StatusTextBlock.Text = "خطأ في فتح النموذج";

                // Only show critical errors
                if (ex is OutOfMemoryException || ex is System.ComponentModel.Win32Exception)
                {
                    MessageBox.Show($"خطأ في فتح نموذج المورد: {ex.Message}", "خطأ حرج",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void AddSupplierFormToMainWindow(Controls.SupplierFormControl supplierForm)
        {
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow == null) return;

            Grid? targetGrid = null;

            // Approach 1: DialogHost content
            if (mainWindow.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost)
            {
                targetGrid = dialogHost.Content as Grid;
            }
            // Approach 2: Direct Grid
            else if (mainWindow.Content is Grid directGrid)
            {
                targetGrid = directGrid;
            }
            // Approach 3: Find first Grid in visual tree
            else
            {
                targetGrid = FindVisualChild<Grid>(mainWindow);
            }

            if (targetGrid != null)
            {
                // Set the supplier form to fill the entire grid
                supplierForm.HorizontalAlignment = HorizontalAlignment.Stretch;
                supplierForm.VerticalAlignment = VerticalAlignment.Stretch;

                // Add to the grid with highest Z-index
                Grid.SetRowSpan(supplierForm, targetGrid.RowDefinitions.Count > 0 ? targetGrid.RowDefinitions.Count : 1);
                Grid.SetColumnSpan(supplierForm, targetGrid.ColumnDefinitions.Count > 0 ? targetGrid.ColumnDefinitions.Count : 1);
                Panel.SetZIndex(supplierForm, 1000);

                targetGrid.Children.Add(supplierForm);
            }
        }

        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        private void RemoveSupplierFormFromMainWindow(Controls.SupplierFormControl supplierForm)
        {
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow == null) return;

            Grid? targetGrid = null;

            if (mainWindow.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost)
            {
                targetGrid = dialogHost.Content as Grid;
            }
            else if (mainWindow.Content is Grid directGrid)
            {
                targetGrid = directGrid;
            }
            else
            {
                targetGrid = FindVisualChild<Grid>(mainWindow);
            }

            targetGrid?.Children.Remove(supplierForm);
        }

        private async void SupplierForm_FormClosed(object? sender, Controls.SupplierFormEventArgs e)
        {
            if (sender is Controls.SupplierFormControl supplierForm)
            {
                supplierForm.FormClosed -= SupplierForm_FormClosed;
                RemoveSupplierFormFromMainWindow(supplierForm);

                if (e.Success)
                {
                    // Refresh the suppliers list
                    await LoadSuppliersAsync();
                    UpdateStatistics();
                }
            }
        }
        
        private async void StatementButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                try
                {
                    await Task.Run(() =>
                    {
                        var statementWindow = new SupplierStatementWindow(supplier.Id);
                        Dispatcher.Invoke(() => statementWindow.Show());
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فتح كشف الحساب: {ex.Message}");
                    StatusTextBlock.Text = "خطأ في فتح كشف الحساب";

                    // Show error only for critical issues
                    if (ex is FileNotFoundException || ex is UnauthorizedAccessException)
                    {
                        MessageBox.Show($"خطأ في فتح كشف الحساب: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // We'll handle editing through the Edit button instead of selection
            // This prevents accidental form opening when just selecting rows
        }
        
        private void LoadSupplierToForm(Supplier supplier)
        {
            _currentSupplier = supplier;
            
            NameTextBox.Text = supplier.Name;
            ContactPersonTextBox.Text = supplier.ContactPerson ?? string.Empty;
            PhoneTextBox.Text = supplier.Phone ?? string.Empty;
            EmailTextBox.Text = supplier.Email ?? string.Empty;
            AddressTextBox.Text = supplier.Address ?? string.Empty;
            TaxNumberTextBox.Text = supplier.TaxNumber ?? string.Empty;
            NotesTextBox.Text = supplier.Notes ?? string.Empty;
        }
        
        private void ClearForm()
        {
            _currentSupplier = null;
            
            NameTextBox.Text = string.Empty;
            ContactPersonTextBox.Text = string.Empty;
            PhoneTextBox.Text = string.Empty;
            EmailTextBox.Text = string.Empty;
            AddressTextBox.Text = string.Empty;
            TaxNumberTextBox.Text = string.Empty;
            NotesTextBox.Text = string.Empty;
        }
        
        private void SetFormMode(bool isEdit)
        {
            _isEditMode = isEdit;
            
            if (isEdit)
            {
                FormHeaderTextBlock.Text = "تعديل بيانات المورد";
                DeleteButton.Visibility = Visibility.Visible;
            }
            else
            {
                FormHeaderTextBlock.Text = "إضافة مورد جديد";
                DeleteButton.Visibility = Visibility.Collapsed;
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;
            
            try
            {
                StatusTextBlock.Text = "جاري الحفظ...";
                SaveButton.IsEnabled = false;
                
                var supplier = _currentSupplier ?? new Supplier();
                
                supplier.Name = NameTextBox.Text.Trim();
                supplier.ContactPerson = ContactPersonTextBox.Text.Trim();
                supplier.Phone = PhoneTextBox.Text.Trim();
                supplier.Email = EmailTextBox.Text.Trim();
                supplier.Address = AddressTextBox.Text.Trim();
                supplier.TaxNumber = TaxNumberTextBox.Text.Trim();
                supplier.Notes = NotesTextBox.Text.Trim();
                supplier.IsActive = true;
                
                if (_isEditMode)
                {
                    await _supplierService.UpdateSupplierAsync(supplier);
                    StatusTextBlock.Text = "تم تحديث بيانات المورد بنجاح";

                    // Show enhanced success notification for update
                    _successNotificationService.ShowUpdateSuccess($"المورد '{supplier.Name}'");
                }
                else
                {
                    await _supplierService.CreateSupplierAsync(supplier);
                    StatusTextBlock.Text = "تم إضافة المورد بنجاح";

                    // Show enhanced success notification for create
                    var options = new SuccessNotificationOptions
                    {
                        ShowActions = true,
                        PrimaryActionText = "عرض المورد",
                        SecondaryActionText = "إضافة مورد آخر",
                        PrimaryAction = () => SelectSupplierInList(supplier.Name),
                        SecondaryAction = () => { ClearForm(); SetFormMode(true); }
                    };
                    _successNotificationService.ShowSuccess("تم إنشاء المورد بنجاح",
                        $"تم إنشاء المورد '{supplier.Name}' بنجاح وإضافته إلى النظام\nالهاتف: {supplier.Phone}\nالعنوان: {supplier.Address}", options);
                }

                await LoadSuppliersAsync();
                ClearForm();
                SetFormMode(false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ بيانات المورد: {ex.Message}");
                StatusTextBlock.Text = "خطأ في الحفظ - يرجى التحقق من البيانات";

                // Show MessageBox only for database or validation errors
                if (ex is System.Data.Common.DbException || ex is ArgumentException)
                {
                    MessageBox.Show($"خطأ في حفظ بيانات المورد: {ex.Message}", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            SetFormMode(false);
            StatusTextBlock.Text = "تم إلغاء العملية";
        }
        
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentSupplier == null)
                return;
            
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المورد '{_currentSupplier.Name}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    StatusTextBlock.Text = "جاري الحذف...";
                    DeleteButton.IsEnabled = false;
                    
                    await _supplierService.DeleteSupplierAsync(_currentSupplier.Id);
                    
                    await LoadSuppliersAsync();
                    ClearForm();
                    SetFormMode(false);

                    StatusTextBlock.Text = "تم حذف المورد بنجاح";

                    // Show enhanced success notification for delete
                    _successNotificationService.ShowDeleteSuccess($"المورد '{_currentSupplier.Name}'");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في حذف المورد: {ex.Message}");
                    StatusTextBlock.Text = "خطأ في الحذف - يرجى المحاولة مرة أخرى";

                    // Show error for database constraints or critical errors
                    if (ex is System.Data.Common.DbException)
                    {
                        MessageBox.Show($"لا يمكن حذف المورد: {ex.Message}", "خطأ في قاعدة البيانات",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                finally
                {
                    DeleteButton.IsEnabled = true;
                }
            }
        }
        
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterSuppliers();
        }
        
        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                // Show validation error in status instead of MessageBox
                StatusTextBlock.Text = "يرجى إدخال اسم المورد";
                StatusTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                NameTextBox.Focus();

                // Flash the textbox border to indicate error
                NameTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }

            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
            {
                StatusTextBlock.Text = "يرجى إدخال بريد إلكتروني صحيح";
                StatusTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                EmailTextBox.Focus();

                // Flash the textbox border to indicate error
                EmailTextBox.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
                return false;
            }
            
            return true;
        }
        
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void SelectSupplierInList(string supplierName)
        {
            var supplier = FilteredSuppliers.FirstOrDefault(s => s.Name == supplierName);
            if (supplier != null)
            {
                SuppliersDataGrid.SelectedItem = supplier;
                SuppliersDataGrid.ScrollIntoView(supplier);
                // Load supplier for editing
                LoadSupplierToForm(supplier);
                SetFormMode(true);
            }
        }
    }
}
