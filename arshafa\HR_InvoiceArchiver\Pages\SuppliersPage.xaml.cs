using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Windows;
using HR_InvoiceArchiver.Controls;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SuppliersPage : UserControl, INavigationAware
    {
        private readonly ISupplierService _supplierService;
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;

        private ObservableCollection<Supplier> _allSuppliers = new();
        private ObservableCollection<Supplier> _filteredSuppliers = new();
        private string _searchText = string.Empty;

        public SuppliersPage(
            ISupplierService supplierService,
            IInvoiceService invoiceService,
            IToastService toastService,
            INavigationService navigationService)
        {
            InitializeComponent();
            _supplierService = supplierService;
            _invoiceService = invoiceService;
            _toastService = toastService;
            _navigationService = navigationService;

            // Set up DataGrid binding
            SuppliersDataGrid.ItemsSource = _filteredSuppliers;

            Loaded += SuppliersPage_Loaded;
        }

        private async void SuppliersPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSupplierStatisticsAsync();
            await LoadSuppliersAsync();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                ShowLoading(true);

                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Calculate invoice counts and totals for each supplier
                foreach (var supplier in suppliers)
                {
                    var invoices = await _invoiceService.GetInvoicesBySupplierAsync(supplier.Id);
                    supplier.InvoiceCount = invoices.Count();
                    supplier.TotalAmount = invoices.Sum(i => i.Amount);
                }

                Dispatcher.Invoke(() =>
                {
                    _allSuppliers.Clear();
                    foreach (var supplier in suppliers)
                    {
                        _allSuppliers.Add(supplier);
                    }

                    ApplyFilter();
                    UpdateFooterStatistics();
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الموردين", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void ApplyFilter()
        {
            _filteredSuppliers.Clear();

            var filtered = _allSuppliers.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(_searchText))
            {
                filtered = filtered.Where(s =>
                    s.Name.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ||
                    (s.ContactPerson?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (s.Phone?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (s.Email?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            foreach (var supplier in filtered.OrderBy(s => s.Name))
            {
                _filteredSuppliers.Add(supplier);
            }
        }

        private void UpdateFooterStatistics()
        {
            // Footer statistics removed as they're now integrated in the header cards
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                _searchText = textBox.Text;
                ApplyFilter();
                UpdateFooterStatistics();
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowLoading(true);

                // Refresh both statistics and suppliers data
                await LoadSupplierStatisticsAsync();
                await LoadSuppliersAsync();

                // Show success message
                _toastService.ShowSuccess("تم تحديث البيانات", "تم تحديث بيانات الموردين بنجاح");

                // Add delay for better UX
                await Task.Delay(1000);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحديث البيانات", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        // Interactive Statistics Card Events
        private void StatCard_MouseEnter(object sender, MouseEventArgs e)
        {
            if (sender is MaterialDesignThemes.Wpf.Card card)
            {
                try
                {
                    // Create smooth scale animation
                    var scaleTransform = new ScaleTransform(1.0, 1.0);
                    card.RenderTransform = scaleTransform;
                    card.RenderTransformOrigin = new Point(0.5, 0.5);

                    var scaleXAnimation = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 1.05,
                        Duration = TimeSpan.FromMilliseconds(300),
                        EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                    };

                    var scaleYAnimation = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 1.05,
                        Duration = TimeSpan.FromMilliseconds(300),
                        EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                    };

                    scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
                    scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
                }
                catch
                {
                    // Fallback - just change elevation
                    MaterialDesignThemes.Wpf.ElevationAssist.SetElevation(card, MaterialDesignThemes.Wpf.Elevation.Dp8);
                }
            }
        }

        private void StatCard_MouseLeave(object sender, MouseEventArgs e)
        {
            if (sender is MaterialDesignThemes.Wpf.Card card)
            {
                try
                {
                    var scaleTransform = card.RenderTransform as ScaleTransform;
                    if (scaleTransform != null)
                    {
                        var scaleXAnimation = new DoubleAnimation
                        {
                            From = 1.05,
                            To = 1.0,
                            Duration = TimeSpan.FromMilliseconds(250),
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };

                        var scaleYAnimation = new DoubleAnimation
                        {
                            From = 1.05,
                            To = 1.0,
                            Duration = TimeSpan.FromMilliseconds(250),
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };

                        scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
                        scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
                    }
                }
                catch
                {
                    // Fallback - reset elevation
                    MaterialDesignThemes.Wpf.ElevationAssist.SetElevation(card, MaterialDesignThemes.Wpf.Elevation.Dp3);
                }
            }
        }

        // Enhanced Action Button Events
        private void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                EditButton_Click(sender, e);
            }
        }

        private async void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{supplier.Name}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        ShowLoading(true);
                        await _supplierService.DeleteSupplierAsync(supplier.Id);

                        // Refresh data
                        await LoadSupplierStatisticsAsync();
                        await LoadSuppliersAsync();

                        // Show enhanced success message
                        _toastService.ShowSuccess("تم حذف المورد بنجاح", $"تم حذف المورد '{supplier.Name}' وجميع البيانات المرتبطة به");

                        // Add delay for better UX
                        await Task.Delay(1500);
                    }
                    catch (Exception ex)
                    {
                        _toastService.ShowError("خطأ في حذف المورد", ex.Message);
                    }
                    finally
                    {
                        ShowLoading(false);
                    }
                }
            }
        }

        private void ViewSupplierDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                try
                {
                    // Navigate to supplier details or show details dialog
                    var detailsMessage = $"تفاصيل المورد:\n\n" +
                                       $"الاسم: {supplier.Name}\n" +
                                       $"رقم الهاتف: {supplier.Phone ?? "غير محدد"}\n" +
                                       $"البريد الإلكتروني: {supplier.Email ?? "غير محدد"}\n" +
                                       $"الشخص المسؤول: {supplier.ContactPerson ?? "غير محدد"}\n" +
                                       $"العنوان: {supplier.Address ?? "غير محدد"}\n" +
                                       $"عدد الفواتير: {supplier.InvoiceCount}\n" +
                                       $"إجمالي المبلغ: {supplier.TotalAmount:N0} د.ع";

                    MessageBox.Show(detailsMessage, $"تفاصيل المورد - {supplier.Name}",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في عرض تفاصيل المورد", ex.Message);
                }
            }
        }

        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Handle selection change if needed
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                try
                {
                    var supplierForm = new Controls.SupplierFormControl
                    {
                        CurrentSupplier = supplier
                    };

                    supplierForm.FormClosed += async (s, args) =>
                    {
                        try
                        {
                            // Remove the form from the main window first
                            RemoveSupplierFormFromMainWindow(supplierForm);

                            // Show loading while refreshing
                            ShowLoading(true);

                            // Refresh statistics and suppliers list with enhanced UX
                            await LoadSupplierStatisticsAsync();
                            await LoadSuppliersAsync();

                            // Show success message if supplier was updated
                            if (args.Success && args.Supplier != null)
                            {
                                _toastService.ShowSuccess("تم تحديث المورد بنجاح",
                                    $"تم تحديث بيانات المورد '{args.Supplier.Name}' بنجاح");

                                // Scroll to the updated supplier
                                await ScrollToSupplier(args.Supplier.Name);
                            }

                            // Add delay for better UX
                            await Task.Delay(1000);
                        }
                        catch (Exception ex)
                        {
                            _toastService.ShowError("خطأ في إغلاق نموذج المورد", ex.Message);
                        }
                        finally
                        {
                            ShowLoading(false);
                        }
                    };

                    AddSupplierFormToMainWindow(supplierForm);
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في فتح نموذج تعديل المورد", ex.Message);
                }
            }
        }

        private void StatementButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                try
                {
                    var statementWindow = new SupplierStatementWindow(supplier.Id);
                    statementWindow.ShowDialog();
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في فتح كشف حساب المورد", ex.Message);
                }
            }
        }

        public void OnNavigatedTo(object parameter)
        {
            // Refresh data when navigating to this page - use Dispatcher to avoid threading issues
            Dispatcher.BeginInvoke(new Action(async () =>
            {
                await LoadSupplierStatisticsAsync();
                await LoadSuppliersAsync();
            }));
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadSupplierStatisticsAsync()
        {
            try
            {
                ShowLoading(true);
                
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                
                Dispatcher.Invoke(() =>
                {
                    try
                    {
                        // Total suppliers
                        if (TotalSuppliersText != null)
                            TotalSuppliersText.Text = suppliers.Count().ToString("N0");

                        // Active suppliers (suppliers with invoices in the last 6 months)
                        var sixMonthsAgo = DateTime.Now.AddMonths(-6);
                        var activeSupplierIds = invoices
                            .Where(i => i.InvoiceDate >= sixMonthsAgo)
                            .Select(i => i.SupplierId)
                            .Distinct();
                        var activeSuppliersCount = suppliers.Count(s => activeSupplierIds.Contains(s.Id));
                        if (ActiveSuppliersText != null)
                            ActiveSuppliersText.Text = activeSuppliersCount.ToString("N0");

                        // Total outstanding amount (unpaid and partially paid invoices)
                        var outstandingAmount = invoices
                            .Where(i => i.Status == InvoiceStatus.Unpaid || i.Status == InvoiceStatus.PartiallyPaid)
                            .Sum(i => i.Amount - i.PaidAmount);
                        if (TotalOutstandingText != null)
                            TotalOutstandingText.Text = $"{outstandingAmount:N0} د.ع";

                        // Total paid amount (all paid amounts from invoices)
                        var totalPaidAmount = invoices.Sum(i => i.PaidAmount);
                        if (TotalPaidText != null)
                            TotalPaidText.Text = $"{totalPaidAmount:N0} د.ع";
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في تحميل إحصائيات الموردين", ex.Message);
                });
            }
            finally
            {
                Dispatcher.Invoke(() => ShowLoading(false));
            }
        }

        private void ShowLoading(bool isLoading)
        {
            try
            {
                // Use modern loading panel instead of simple progress bar
                if (LoadingPanel != null)
                {
                    LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
                }

                // Hide/show main content
                if (SuppliersDataGrid != null)
                {
                    SuppliersDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
                }

                // Handle empty state
                if (!isLoading && _filteredSuppliers.Count == 0)
                {
                    if (EmptyStatePanel != null)
                    {
                        EmptyStatePanel.Visibility = Visibility.Visible;
                    }
                    if (SuppliersDataGrid != null)
                    {
                        SuppliersDataGrid.Visibility = Visibility.Collapsed;
                    }
                }
                else if (!isLoading)
                {
                    if (EmptyStatePanel != null)
                    {
                        EmptyStatePanel.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ShowLoading: {ex.Message}");
                // Fallback - just hide/show main content
                if (SuppliersDataGrid != null)
                {
                    SuppliersDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
                }
            }
        }

        private void AddNewSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create and show the modern supplier form directly in the main window
                var supplierForm = new Controls.SupplierFormControl
                {
                    CurrentSupplier = null // New supplier
                };

                supplierForm.FormClosed += async (s, args) =>
                {
                    try
                    {
                        // Remove the form from the main window first
                        RemoveSupplierFormFromMainWindow(supplierForm);

                        // Show loading while refreshing
                        ShowLoading(true);

                        // Refresh statistics and suppliers list with enhanced UX
                        await LoadSupplierStatisticsAsync();
                        await LoadSuppliersAsync();

                        // Show success message if supplier was added
                        if (args.Success && args.Supplier != null)
                        {
                            _toastService.ShowSuccess("تم إضافة المورد بنجاح",
                                $"تم إضافة المورد '{args.Supplier.Name}' بنجاح إلى النظام");

                            // Scroll to the newly added supplier
                            await ScrollToSupplier(args.Supplier.Name);
                        }

                        // Add delay for better UX
                        await Task.Delay(1000);
                    }
                    catch (Exception ex)
                    {
                        _toastService.ShowError("خطأ في إغلاق نموذج المورد", ex.Message);
                    }
                    finally
                    {
                        ShowLoading(false);
                    }
                };

                AddSupplierFormToMainWindow(supplierForm);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في فتح نموذج إضافة المورد", ex.Message);
            }
        }

        private async Task ScrollToSupplier(string supplierName)
        {
            try
            {
                await Task.Delay(500); // Wait for data to load

                var supplier = _filteredSuppliers.FirstOrDefault(s =>
                    s.Name.Equals(supplierName, StringComparison.OrdinalIgnoreCase));

                if (supplier != null && SuppliersDataGrid != null)
                {
                    SuppliersDataGrid.SelectedItem = supplier;
                    SuppliersDataGrid.ScrollIntoView(supplier);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error scrolling to supplier: {ex.Message}");
            }
        }



        private void SupplierStatementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show supplier selection dialog
                if (_filteredSuppliers.Count == 0)
                {
                    _toastService.ShowWarning("لا توجد موردين", "يجب إضافة موردين أولاً لعرض كشف الحساب");
                    return;
                }

                // For now, show statement for the first supplier or selected supplier
                var selectedSupplier = _filteredSuppliers.FirstOrDefault();
                if (selectedSupplier != null)
                {
                    ShowSupplierStatementForm(selectedSupplier.Id);
                }
                else
                {
                    _toastService.ShowWarning("لم يتم تحديد مورد", "يرجى تحديد مورد من القائمة أولاً");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في فتح كشف حساب المورد", ex.Message);
            }
        }

        private void ShowSupplierStatementForm(int supplierId)
        {
            try
            {
                var statementForm = new Controls.SupplierStatementControl(supplierId);
                statementForm.FormClosed += SupplierStatementForm_FormClosed;

                // Find the main window to add the overlay
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow?.Content is Grid mainGrid)
                {
                    mainGrid.Children.Add(statementForm);
                }
                else
                {
                    // Fallback: try to find a suitable container using visual tree helper
                    var container = FindParentGrid(this);
                    if (container != null)
                    {
                        container.Children.Add(statementForm);
                    }
                    else
                    {
                        _toastService.ShowError("خطأ", "لا يمكن عرض نافذة كشف الحساب");
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في عرض كشف الحساب", ex.Message);
            }
        }

        private async void SupplierStatementForm_FormClosed(object? sender, Controls.SupplierStatementEventArgs e)
        {
            if (sender is Controls.SupplierStatementControl statementForm)
            {
                statementForm.FormClosed -= SupplierStatementForm_FormClosed;
                await Task.Run(() => RemoveSupplierStatementFormFromMainWindow(statementForm));
            }
        }

        private void RemoveSupplierStatementFormFromMainWindow(Controls.SupplierStatementControl statementForm)
        {
            try
            {
                // Try to find and remove from main window
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow?.Content is Grid mainGrid && mainGrid.Children.Contains(statementForm))
                {
                    mainGrid.Children.Remove(statementForm);
                    return;
                }

                // Fallback: try to find parent container
                var parent = statementForm.Parent;
                if (parent is Panel panel)
                {
                    panel.Children.Remove(statementForm);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing statement form: {ex.Message}");
            }
        }

        private void AddSupplierFormToMainWindow(Controls.SupplierFormControl supplierForm)
        {
            var mainWindow = Application.Current.MainWindow;
            if (mainWindow == null) return;

            Grid? targetGrid = null;

            // Find the main content grid in MaterialDesign DialogHost
            if (mainWindow.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost)
            {
                targetGrid = dialogHost.Content as Grid;
            }
            else if (mainWindow.Content is Grid directGrid)
            {
                targetGrid = directGrid;
            }
            else
            {
                // Find first Grid in visual tree
                targetGrid = FindVisualChild<Grid>(mainWindow);
            }

            if (targetGrid != null)
            {
                // Set the supplier form to fill the entire grid
                supplierForm.HorizontalAlignment = HorizontalAlignment.Stretch;
                supplierForm.VerticalAlignment = VerticalAlignment.Stretch;

                // Add to the grid with highest Z-index
                Grid.SetRowSpan(supplierForm, targetGrid.RowDefinitions.Count > 0 ? targetGrid.RowDefinitions.Count : 1);
                Grid.SetColumnSpan(supplierForm, targetGrid.ColumnDefinitions.Count > 0 ? targetGrid.ColumnDefinitions.Count : 1);
                Panel.SetZIndex(supplierForm, 1000);

                targetGrid.Children.Add(supplierForm);
            }
        }

        private void RemoveSupplierFormFromMainWindow(Controls.SupplierFormControl supplierForm)
        {
            try
            {
                var mainWindow = Application.Current.MainWindow;
                if (mainWindow == null) return;

                Grid? targetGrid = null;

                // Find the main content grid in MaterialDesign DialogHost
                if (mainWindow.Content is MaterialDesignThemes.Wpf.DialogHost dialogHost)
                {
                    targetGrid = dialogHost.Content as Grid;
                }
                else if (mainWindow.Content is Grid directGrid)
                {
                    targetGrid = directGrid;
                }
                else
                {
                    // Find first Grid in visual tree
                    targetGrid = FindVisualChild<Grid>(mainWindow);
                }

                if (targetGrid != null && targetGrid.Children.Contains(supplierForm))
                {
                    // Remove the supplier form from the grid
                    targetGrid.Children.Remove(supplierForm);
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user as this is cleanup
                System.Diagnostics.Debug.WriteLine($"Error removing supplier form: {ex.Message}");
            }
        }

        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        private Grid? FindParentGrid(DependencyObject child)
        {
            var parent = VisualTreeHelper.GetParent(child);
            while (parent != null)
            {
                if (parent is Grid grid)
                    return grid;
                parent = VisualTreeHelper.GetParent(parent);
            }
            return null;
        }
    }
}
