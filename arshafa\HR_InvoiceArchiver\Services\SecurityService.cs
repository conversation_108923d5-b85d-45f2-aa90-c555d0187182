using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// تطبيق خدمة الأمان
    /// </summary>
    public class SecurityService : ISecurityService
    {
        private readonly IEncryptionService _encryptionService;
        private readonly ILoggingService _loggingService;
        private readonly ISettingsService _settingsService;
        
        private readonly ConcurrentDictionary<string, SecurityUser> _users;
        private readonly ConcurrentDictionary<string, DateTime> _lockedAccounts;
        private readonly ConcurrentDictionary<string, int> _failedAttempts;
        private readonly List<SecurityEvent> _securityEvents;
        
        private SecurityUser? _currentUser;
        private string? _currentSessionToken;
        private DateTime? _sessionExpiry;
        
        private readonly object _lockObject = new object();

        public SecurityService(IEncryptionService encryptionService, ILoggingService loggingService, ISettingsService settingsService)
        {
            _encryptionService = encryptionService;
            _loggingService = loggingService;
            _settingsService = settingsService;
            
            _users = new ConcurrentDictionary<string, SecurityUser>();
            _lockedAccounts = new ConcurrentDictionary<string, DateTime>();
            _failedAttempts = new ConcurrentDictionary<string, int>();
            _securityEvents = new List<SecurityEvent>();

            // إنشاء مستخدم افتراضي للنظام
            InitializeDefaultUser();
        }

        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                var result = new LoginResult();

                // التحقق من قفل الحساب
                if (await IsAccountLockedAsync(username))
                {
                    result.ErrorMessage = "الحساب مقفل مؤقتاً بسبب محاولات دخول فاشلة متعددة";
                    await LogSecurityEventAsync(SecurityEventType.LoginFailure, $"محاولة دخول لحساب مقفل: {username}");
                    return result;
                }

                // البحث عن المستخدم
                if (!_users.TryGetValue(username.ToLower(), out var user) || !user.IsActive)
                {
                    await HandleFailedLoginAsync(username);
                    result.ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    return result;
                }

                // التحقق من كلمة المرور
                var isPasswordValid = await VerifyPasswordAsync(password, user.Id);
                if (!isPasswordValid)
                {
                    await HandleFailedLoginAsync(username);
                    result.ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    return result;
                }

                // نجح تسجيل الدخول
                await HandleSuccessfulLoginAsync(user);
                
                result.IsSuccessful = true;
                result.User = user;
                result.SessionToken = _currentSessionToken;
                result.SessionExpiry = _sessionExpiry;

                await LogSecurityEventAsync(SecurityEventType.LoginSuccess, $"تسجيل دخول ناجح للمستخدم: {username}");
                return result;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في تسجيل الدخول", ex);
                return new LoginResult { ErrorMessage = "حدث خطأ أثناء تسجيل الدخول" };
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                if (_currentUser != null)
                {
                    await LogSecurityEventAsync(SecurityEventType.Logout, $"تسجيل خروج للمستخدم: {_currentUser.Username}");
                }

                _currentUser = null;
                _currentSessionToken = null;
                _sessionExpiry = null;

                // مسح البيانات الحساسة من الذاكرة
                await SecurelyWipeMemoryAsync();
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في تسجيل الخروج", ex);
            }
        }

        public async Task<bool> IsSessionValidAsync()
        {
            try
            {
                if (_currentUser == null || _sessionExpiry == null || string.IsNullOrEmpty(_currentSessionToken))
                    return false;

                if (DateTime.Now > _sessionExpiry)
                {
                    await LogSecurityEventAsync(SecurityEventType.SessionExpired, "انتهت صلاحية الجلسة");
                    await LogoutAsync();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في التحقق من صحة الجلسة", ex);
                return false;
            }
        }

        public async Task<bool> RefreshSessionAsync()
        {
            try
            {
                if (!await IsSessionValidAsync())
                    return false;

                var settings = await _settingsService.LoadSettingsAsync();
                _sessionExpiry = DateTime.Now.AddMinutes(settings.SessionTimeoutMinutes);
                _currentSessionToken = await GenerateSessionTokenAsync();

                await LogSecurityEventAsync(SecurityEventType.DataAccess, "تم تجديد الجلسة");
                return true;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في تجديد الجلسة", ex);
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(string currentPassword, string newPassword)
        {
            try
            {
                if (_currentUser == null)
                    return false;

                // التحقق من كلمة المرور الحالية
                var isCurrentPasswordValid = await VerifyPasswordAsync(currentPassword, _currentUser.Id);
                if (!isCurrentPasswordValid)
                    return false;

                // التحقق من قوة كلمة المرور الجديدة
                var passwordStrength = await _encryptionService.CheckPasswordStrengthAsync(newPassword);
                if (passwordStrength < PasswordStrength.Medium)
                    return false;

                // حفظ كلمة المرور الجديدة
                await SavePasswordAsync(_currentUser.Id, newPassword);

                await LogSecurityEventAsync(SecurityEventType.PasswordChanged, $"تم تغيير كلمة المرور للمستخدم: {_currentUser.Username}");
                return true;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في تغيير كلمة المرور", ex);
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(string username, string securityAnswer)
        {
            try
            {
                if (!_users.TryGetValue(username.ToLower(), out var user))
                    return false;

                // في تطبيق حقيقي، يجب التحقق من إجابة السؤال الأمني
                // هنا نفترض أن الإجابة صحيحة للبساطة

                var newPassword = await _encryptionService.GenerateStrongPasswordAsync();
                await SavePasswordAsync(user.Id, newPassword);

                await LogSecurityEventAsync(SecurityEventType.PasswordReset, $"تم إعادة تعيين كلمة المرور للمستخدم: {username}");
                return true;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في إعادة تعيين كلمة المرور", ex);
                return false;
            }
        }

        public async Task LockAccountAsync(string username, TimeSpan lockDuration)
        {
            try
            {
                var lockUntil = DateTime.Now.Add(lockDuration);
                _lockedAccounts.TryAdd(username.ToLower(), lockUntil);

                if (_users.TryGetValue(username.ToLower(), out var user))
                {
                    user.IsLocked = true;
                    user.LockoutEnd = lockUntil;
                }

                await LogSecurityEventAsync(SecurityEventType.AccountLocked, 
                    $"تم قفل الحساب: {username} حتى {lockUntil:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في قفل الحساب", ex);
            }
        }

        public async Task UnlockAccountAsync(string username)
        {
            try
            {
                _lockedAccounts.TryRemove(username.ToLower(), out _);
                _failedAttempts.TryRemove(username.ToLower(), out _);

                if (_users.TryGetValue(username.ToLower(), out var user))
                {
                    user.IsLocked = false;
                    user.LockoutEnd = null;
                    user.FailedLoginAttempts = 0;
                }

                await LogSecurityEventAsync(SecurityEventType.AccountUnlocked, $"تم إلغاء قفل الحساب: {username}");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في إلغاء قفل الحساب", ex);
            }
        }

        public async Task<bool> IsAccountLockedAsync(string username)
        {
            try
            {
                if (!_lockedAccounts.TryGetValue(username.ToLower(), out var lockUntil))
                    return false;

                if (DateTime.Now > lockUntil)
                {
                    await UnlockAccountAsync(username);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في التحقق من قفل الحساب", ex);
                return false;
            }
        }

        public async Task LogSecurityEventAsync(SecurityEventType eventType, string description, 
            string? userId = null, Dictionary<string, object>? additionalData = null)
        {
            try
            {
                var securityEvent = new SecurityEvent
                {
                    EventType = eventType,
                    Description = description,
                    UserId = userId ?? _currentUser?.Id,
                    Username = _currentUser?.Username,
                    AdditionalData = additionalData ?? new Dictionary<string, object>(),
                    Level = GetSecurityLevel(eventType)
                };

                lock (_lockObject)
                {
                    _securityEvents.Add(securityEvent);
                    
                    // الاحتفاظ بآخر 1000 حدث فقط
                    if (_securityEvents.Count > 1000)
                    {
                        _securityEvents.RemoveAt(0);
                    }
                }

                await _loggingService.LogSecurityEventAsync(eventType.ToString(), description, userId, additionalData);
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في تسجيل الحدث الأمني", ex);
            }
        }

        public async Task<List<SecurityEvent>> GetRecentSecurityEventsAsync(int count = 50)
        {
            try
            {
                lock (_lockObject)
                {
                    return _securityEvents
                        .OrderByDescending(e => e.Timestamp)
                        .Take(count)
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في الحصول على الأحداث الأمنية", ex);
                return new List<SecurityEvent>();
            }
        }

        public async Task<bool> HasPermissionAsync(string permission)
        {
            try
            {
                if (_currentUser == null)
                    return false;

                return _currentUser.Permissions.Contains(permission) || 
                       _currentUser.Roles.Contains("Admin");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في التحقق من الصلاحيات", ex);
                return false;
            }
        }

        public async Task<SecurityUser?> GetCurrentUserAsync()
        {
            try
            {
                return _currentUser;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في الحصول على المستخدم الحالي", ex);
                return null;
            }
        }

        public async Task<string> EncryptSensitiveDataAsync(string data)
        {
            return await _encryptionService.EncryptSensitiveDataAsync(data);
        }

        public async Task<string> DecryptSensitiveDataAsync(string encryptedData)
        {
            return await _encryptionService.DecryptSensitiveDataAsync(encryptedData);
        }

        public async Task<bool> VerifyDataIntegrityAsync(string data, string signature)
        {
            try
            {
                var computedSignature = await CreateDataSignatureAsync(data);
                return computedSignature == signature;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في التحقق من سلامة البيانات", ex);
                return false;
            }
        }

        public async Task<string> CreateDataSignatureAsync(string data)
        {
            try
            {
                return await _encryptionService.CreateHashAsync(data);
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في إنشاء توقيع البيانات", ex);
                throw;
            }
        }

        public async Task SecurelyWipeMemoryAsync()
        {
            try
            {
                // تشغيل garbage collector لتنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                await _loggingService.LogInformationAsync("تم مسح البيانات الحساسة من الذاكرة");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في مسح الذاكرة", ex);
            }
        }

        #region Helper Methods

        private void InitializeDefaultUser()
        {
            var defaultUser = new SecurityUser
            {
                Id = "admin",
                Username = "admin",
                DisplayName = "مدير النظام",
                Email = "<EMAIL>",
                Roles = new List<string> { "Admin" },
                Permissions = new List<string> { "All" },
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            _users.TryAdd(defaultUser.Username.ToLower(), defaultUser);
        }

        private async Task<bool> VerifyPasswordAsync(string password, string userId)
        {
            try
            {
                // في تطبيق حقيقي، يجب استرجاع hash كلمة المرور من قاعدة البيانات
                // هنا نستخدم كلمة مرور افتراضية للمدير
                if (userId == "admin")
                {
                    var defaultSalt = "defaultSalt";
                    var defaultPasswordHash = await _encryptionService.CreateHashAsync("admin123", defaultSalt);
                    var inputPasswordHash = await _encryptionService.CreateHashAsync(password, defaultSalt);
                    return defaultPasswordHash == inputPasswordHash;
                }

                return false;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في التحقق من كلمة المرور", ex);
                return false;
            }
        }

        private async Task SavePasswordAsync(string userId, string password)
        {
            try
            {
                var passwordHash = await _encryptionService.CreateHashAsync(password);
                // في تطبيق حقيقي، يجب حفظ hash كلمة المرور في قاعدة البيانات
                await _loggingService.LogInformationAsync($"تم حفظ كلمة مرور جديدة للمستخدم: {userId}");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في حفظ كلمة المرور", ex);
                throw;
            }
        }

        private async Task HandleSuccessfulLoginAsync(SecurityUser user)
        {
            try
            {
                _currentUser = user;
                _currentSessionToken = await GenerateSessionTokenAsync();

                var settings = await _settingsService.LoadSettingsAsync();
                _sessionExpiry = DateTime.Now.AddMinutes(settings.SessionTimeoutMinutes);

                user.LastLogin = DateTime.Now;
                user.FailedLoginAttempts = 0;

                // إزالة محاولات الفشل
                _failedAttempts.TryRemove(user.Username.ToLower(), out _);
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في معالجة تسجيل الدخول الناجح", ex);
                throw;
            }
        }

        private async Task HandleFailedLoginAsync(string username)
        {
            try
            {
                var attempts = _failedAttempts.AddOrUpdate(username.ToLower(), 1, (key, value) => value + 1);

                if (_users.TryGetValue(username.ToLower(), out var user))
                {
                    user.FailedLoginAttempts = attempts;
                }

                var settings = await _settingsService.LoadSettingsAsync();
                if (attempts >= settings.MaxLoginAttempts)
                {
                    var lockDuration = TimeSpan.FromMinutes(settings.LockoutDurationMinutes);
                    await LockAccountAsync(username, lockDuration);
                }

                await LogSecurityEventAsync(SecurityEventType.LoginFailure,
                    $"محاولة دخول فاشلة للمستخدم: {username} (المحاولة {attempts})");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في معالجة محاولة الدخول الفاشلة", ex);
            }
        }

        private async Task<string> GenerateSessionTokenAsync()
        {
            try
            {
                var tokenData = $"{_currentUser?.Id}:{DateTime.Now:yyyy-MM-dd HH:mm:ss}:{Guid.NewGuid()}";
                return await _encryptionService.EncryptAsync(tokenData);
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("خطأ في إنشاء رمز الجلسة", ex);
                throw;
            }
        }

        private SecurityLevel GetSecurityLevel(SecurityEventType eventType)
        {
            return eventType switch
            {
                SecurityEventType.LoginSuccess => SecurityLevel.Low,
                SecurityEventType.Logout => SecurityLevel.Low,
                SecurityEventType.LoginFailure => SecurityLevel.Medium,
                SecurityEventType.AccountLocked => SecurityLevel.High,
                SecurityEventType.PasswordChanged => SecurityLevel.Medium,
                SecurityEventType.PasswordReset => SecurityLevel.High,
                SecurityEventType.PermissionDenied => SecurityLevel.Medium,
                SecurityEventType.SecurityViolation => SecurityLevel.Critical,
                SecurityEventType.UnauthorizedAccess => SecurityLevel.Critical,
                SecurityEventType.SessionExpired => SecurityLevel.Low,
                SecurityEventType.DataAccess => SecurityLevel.Low,
                SecurityEventType.DataModification => SecurityLevel.Medium,
                _ => SecurityLevel.Medium
            };
        }

        #endregion
    }
}
