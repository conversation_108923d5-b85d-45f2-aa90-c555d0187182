using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة النسخ الاحتياطي والاستعادة
    /// </summary>
    public interface IBackupRestoreService
    {
        /// <summary>
        /// إنشاء نسخة احتياطية كاملة
        /// </summary>
        Task<BackupResult> CreateFullBackupAsync(BackupOptions options);

        /// <summary>
        /// إنشاء نسخة احتياطية تزايدية
        /// </summary>
        Task<BackupResult> CreateIncrementalBackupAsync(BackupOptions options);

        /// <summary>
        /// إنشاء نسخة احتياطية تفاضلية
        /// </summary>
        Task<BackupResult> CreateDifferentialBackupAsync(BackupOptions options);

        /// <summary>
        /// استعادة من نسخة احتياطية
        /// </summary>
        Task<RestoreResult> RestoreFromBackupAsync(RestoreOptions options);

        /// <summary>
        /// التحقق من صحة النسخة الاحتياطية
        /// </summary>
        Task<ValidationResult> ValidateBackupAsync(string backupPath);

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        Task<List<BackupInfo>> GetAvailableBackupsAsync();

        /// <summary>
        /// حذف النسخ الاحتياطية القديمة
        /// </summary>
        Task<int> CleanupOldBackupsAsync(int daysToKeep = 30);

        /// <summary>
        /// جدولة النسخ الاحتياطي التلقائي
        /// </summary>
        Task ScheduleAutomaticBackupAsync(BackupSchedule schedule);

        /// <summary>
        /// إلغاء جدولة النسخ الاحتياطي التلقائي
        /// </summary>
        Task UnscheduleAutomaticBackupAsync();

        /// <summary>
        /// ضغط النسخة الاحتياطية
        /// </summary>
        Task<string> CompressBackupAsync(string backupPath, CompressionLevel level = CompressionLevel.Optimal);

        /// <summary>
        /// فك ضغط النسخة الاحتياطية
        /// </summary>
        Task<string> DecompressBackupAsync(string compressedBackupPath);

        /// <summary>
        /// تشفير النسخة الاحتياطية
        /// </summary>
        Task<string> EncryptBackupAsync(string backupPath, string password);

        /// <summary>
        /// فك تشفير النسخة الاحتياطية
        /// </summary>
        Task<string> DecryptBackupAsync(string encryptedBackupPath, string password);

        /// <summary>
        /// مراقبة تقدم العملية
        /// </summary>
        event EventHandler<BackupProgressEventArgs> ProgressChanged;

        /// <summary>
        /// الحصول على إحصائيات النسخ الاحتياطي
        /// </summary>
        Task<BackupStatistics> GetBackupStatisticsAsync();

        /// <summary>
        /// اختبار استعادة النسخة الاحتياطية (بدون تطبيق التغييرات)
        /// </summary>
        Task<TestRestoreResult> TestRestoreAsync(string backupPath);
    }

    /// <summary>
    /// خيارات النسخ الاحتياطي
    /// </summary>
    public class BackupOptions
    {
        public string BackupPath { get; set; } = string.Empty;
        public BackupType Type { get; set; } = BackupType.Full;
        public bool IncludeAttachments { get; set; } = true;
        public bool IncludeSettings { get; set; } = true;
        public bool IncludeLogs { get; set; } = false;
        public bool CompressBackup { get; set; } = true;
        public bool EncryptBackup { get; set; } = false;
        public string? Password { get; set; }
        public CompressionLevel CompressionLevel { get; set; } = CompressionLevel.Optimal;
        public List<string> ExcludedTables { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// خيارات الاستعادة
    /// </summary>
    public class RestoreOptions
    {
        public string BackupPath { get; set; } = string.Empty;
        public RestoreMode Mode { get; set; } = RestoreMode.Complete;
        public bool RestoreData { get; set; } = true;
        public bool RestoreSettings { get; set; } = true;
        public bool RestoreAttachments { get; set; } = true;
        public bool CreateBackupBeforeRestore { get; set; } = true;
        public string? Password { get; set; }
        public List<string> TablesToRestore { get; set; } = new();
        public bool VerifyIntegrity { get; set; } = true;
    }

    /// <summary>
    /// نتيجة النسخ الاحتياطي
    /// </summary>
    public class BackupResult
    {
        public bool Success { get; set; }
        public string BackupPath { get; set; } = string.Empty;
        public long BackupSizeBytes { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public BackupType Type { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Warnings { get; set; } = new();
        public BackupMetadata Metadata { get; set; } = new();
        public string ChecksumMD5 { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة الاستعادة
    /// </summary>
    public class RestoreResult
    {
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime RestoredAt { get; set; } = DateTime.Now;
        public string? ErrorMessage { get; set; }
        public List<string> Warnings { get; set; } = new();
        public int TablesRestored { get; set; }
        public int RecordsRestored { get; set; }
        public string? BackupCreatedBeforeRestore { get; set; }
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public BackupType Type { get; set; }
        public long SizeBytes { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Description { get; set; } = string.Empty;
        public bool IsCompressed { get; set; }
        public bool IsEncrypted { get; set; }
        public string ChecksumMD5 { get; set; } = string.Empty;
        public BackupMetadata Metadata { get; set; } = new();
        public bool IsValid { get; set; } = true;
    }

    /// <summary>
    /// بيانات وصفية للنسخة الاحتياطية
    /// </summary>
    public class BackupMetadata
    {
        public string ApplicationVersion { get; set; } = string.Empty;
        public string DatabaseVersion { get; set; } = string.Empty;
        public int TotalTables { get; set; }
        public int TotalRecords { get; set; }
        public Dictionary<string, int> TableRecordCounts { get; set; } = new();
        public bool IncludesAttachments { get; set; }
        public bool IncludesSettings { get; set; }
        public bool IncludesLogs { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string MachineName { get; set; } = Environment.MachineName;
    }

    /// <summary>
    /// جدولة النسخ الاحتياطي
    /// </summary>
    public class BackupSchedule
    {
        public bool IsEnabled { get; set; }
        public BackupFrequency Frequency { get; set; } = BackupFrequency.Daily;
        public TimeSpan ScheduledTime { get; set; } = new TimeSpan(2, 0, 0); // 2:00 AM
        public DayOfWeek? WeeklyDay { get; set; }
        public int? MonthlyDay { get; set; }
        public BackupType BackupType { get; set; } = BackupType.Full;
        public int RetentionDays { get; set; } = 30;
        public bool CompressBackups { get; set; } = true;
        public bool EncryptBackups { get; set; } = false;
        public string? Password { get; set; }
    }

    /// <summary>
    /// إحصائيات النسخ الاحتياطي
    /// </summary>
    public class BackupStatistics
    {
        public int TotalBackups { get; set; }
        public long TotalBackupSizeBytes { get; set; }
        public DateTime? LastBackupDate { get; set; }
        public DateTime? NextScheduledBackup { get; set; }
        public int SuccessfulBackups { get; set; }
        public int FailedBackups { get; set; }
        public double AverageBackupSizeBytes { get; set; }
        public TimeSpan AverageBackupDuration { get; set; }
        public List<BackupInfo> RecentBackups { get; set; } = new();
    }

    /// <summary>
    /// نتيجة اختبار الاستعادة
    /// </summary>
    public class TestRestoreResult
    {
        public bool CanRestore { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public BackupMetadata BackupMetadata { get; set; } = new();
        public TimeSpan EstimatedRestoreTime { get; set; }
    }

    /// <summary>
    /// معلومات تقدم العملية
    /// </summary>
    public class BackupProgressEventArgs : EventArgs
    {
        public int PercentageComplete { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public long ProcessedBytes { get; set; }
        public long TotalBytes { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    /// <summary>
    /// أنواع النسخ الاحتياطي
    /// </summary>
    public enum BackupType
    {
        Full = 0,        // نسخة كاملة
        Incremental = 1, // نسخة تزايدية
        Differential = 2 // نسخة تفاضلية
    }

    /// <summary>
    /// أنماط الاستعادة
    /// </summary>
    public enum RestoreMode
    {
        Complete = 0,    // استعادة كاملة
        DataOnly = 1,    // البيانات فقط
        SettingsOnly = 2 // الإعدادات فقط
    }

    /// <summary>
    /// مستويات الضغط
    /// </summary>
    public enum CompressionLevel
    {
        None = 0,
        Fastest = 1,
        Optimal = 2,
        Maximum = 3
    }

    /// <summary>
    /// تكرار النسخ الاحتياطي
    /// </summary>
    public enum BackupFrequency
    {
        Hourly = 0,
        Daily = 1,
        Weekly = 2,
        Monthly = 3
    }
}
