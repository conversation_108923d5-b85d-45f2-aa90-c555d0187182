using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Controls;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Windows
{
    public partial class InvoiceWindow : Window, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        private ObservableCollection<Invoice> _invoices = new();
        private ObservableCollection<Supplier> _suppliers = new();
        private ICollectionView? _invoicesView;
        private string _searchTerm = string.Empty;
        private InvoiceStatus? _selectedStatus;
        private DateTime? _fromDate;
        private DateTime? _toDate;

        public ObservableCollection<Invoice> Invoices
        {
            get => _invoices;
            set
            {
                _invoices = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set
            {
                _suppliers = value;
                OnPropertyChanged();
            }
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set
            {
                _searchTerm = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public InvoiceWindow()
        {
            // Initialize services from DI container
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();

            InitializeComponent();
            DataContext = this;

            try
            {
                InitializeAsync();
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تهيئة النافذة: {ex.Message}");
            }
        }

        private async void InitializeAsync()
        {
            try
            {
                await LoadDataAsync();
                SetupFilters();
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // Ensure UI updates happen on UI thread
                Dispatcher.Invoke(() => StatusTextBlock.Text = "جاري تحميل البيانات...");

                var invoices = await _invoiceService.GetAllInvoicesAsync();
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Update collections and UI on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    Invoices = new ObservableCollection<Invoice>(invoices);
                    Suppliers = new ObservableCollection<Supplier>(suppliers);
                    InvoicesDataGrid.ItemsSource = Invoices;
                    StatusTextBlock.Text = "تم تحميل البيانات بنجاح";
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    ShowErrorMessage($"خطأ في تحميل البيانات: {ex.Message}");
                    StatusTextBlock.Text = "خطأ في تحميل البيانات";
                });
            }
        }

        private void SetupFilters()
        {
            // Setup status filter
            StatusFilterComboBox.Items.Clear();
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الحالات", Tag = null });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "غير مسددة", Tag = InvoiceStatus.Unpaid });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "تسديد جزئي", Tag = InvoiceStatus.PartiallyPaid });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "مسددة", Tag = InvoiceStatus.Paid });
            StatusFilterComboBox.SelectedIndex = 0;

            // Setup collection view for filtering
            _invoicesView = CollectionViewSource.GetDefaultView(Invoices);
            _invoicesView.Filter = FilterInvoices;
        }

        private bool FilterInvoices(object item)
        {
            if (item is not Invoice invoice) return false;

            // Search filter
            if (!string.IsNullOrEmpty(_searchTerm))
            {
                var searchLower = _searchTerm.ToLower();
                if (!invoice.InvoiceNumber.ToLower().Contains(searchLower) &&
                    !(invoice.Supplier?.Name?.ToLower().Contains(searchLower) ?? false) &&
                    !(invoice.Description?.ToLower().Contains(searchLower) ?? false))
                {
                    return false;
                }
            }

            // Status filter
            if (_selectedStatus.HasValue && invoice.Status != _selectedStatus.Value)
            {
                return false;
            }

            // Date range filter
            if (_fromDate.HasValue && invoice.InvoiceDate < _fromDate.Value)
            {
                return false;
            }

            if (_toDate.HasValue && invoice.InvoiceDate > _toDate.Value)
            {
                return false;
            }

            return true;
        }

        private void ApplyFilters()
        {
            _invoicesView?.Refresh();
            UpdateStatusBar();
        }

        private void UpdateStatusBar()
        {
            var totalCount = Invoices.Count;
            var filteredCount = _invoicesView?.Cast<Invoice>().Count() ?? totalCount;
            
            RecordCountTextBlock.Text = filteredCount == totalCount 
                ? $"عدد السجلات: {totalCount}"
                : $"عدد السجلات: {filteredCount} من {totalCount}";

            var visibleInvoices = _invoicesView?.Cast<Invoice>() ?? Invoices;
            var totalAmount = visibleInvoices.Sum(i => i.Amount);
            var remainingAmount = visibleInvoices.Sum(i => i.RemainingAmount);

            TotalAmountTextBlock.Text = $"{totalAmount:N0} د.ع";
            RemainingAmountTextBlock.Text = $"{remainingAmount:N0} د.ع";

            // Update filter status
            var filterParts = new List<string>();
            if (!string.IsNullOrEmpty(_searchTerm))
                filterParts.Add($"البحث: {_searchTerm}");
            if (_selectedStatus.HasValue)
                filterParts.Add($"الحالة: {GetStatusText(_selectedStatus.Value)}");
            if (_fromDate.HasValue || _toDate.HasValue)
                filterParts.Add("مفلتر بالتاريخ");

            FilterStatusTextBlock.Text = filterParts.Any() 
                ? $"مفلتر: {string.Join(", ", filterParts)}"
                : string.Empty;
        }

        private string GetStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => "غير مسددة",
                InvoiceStatus.PartiallyPaid => "تسديد جزئي",
                InvoiceStatus.Paid => "مسددة",
                _ => "غير محدد"
            };
        }

        private void ShowErrorMessage(string message)
        {
            _toastService.ShowError("خطأ", message);
        }

        private void ShowSuccessMessage(string message)
        {
            _toastService.ShowSuccess("نجح", message);
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            SearchTerm = SearchTextBox.Text;
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (StatusFilterComboBox.SelectedItem is ComboBoxItem item)
            {
                _selectedStatus = item.Tag as InvoiceStatus?;
                ApplyFilters();
            }
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            _fromDate = FromDatePicker.SelectedDate;
            _toDate = ToDatePicker.SelectedDate;
            ApplyFilters();
        }

        private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = string.Empty;
            StatusFilterComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            
            _searchTerm = string.Empty;
            _selectedStatus = null;
            _fromDate = null;
            _toDate = null;
            
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            ShowErrorMessage("وظيفة التصدير قيد التطوير");
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            ShowErrorMessage("نافذة الإعدادات قيد التطوير");
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InvoiceFormOverlay.ShowAddInvoiceForm();
            }
            catch (Exception ex)
            {
                    // Refresh the invoice list
                ShowErrorMessage($"خطأ في فتح نموذج إضافة الفاتورة: {ex.Message}");
            }
        }

        private void InvoiceFormOverlay_FormClosed(object sender, Controls.InvoiceFormEventArgs e)
        {
            if (e.Success)
            {
                // Refresh the data
                _ = LoadDataAsync();
            }
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedInvoice = InvoicesDataGrid.SelectedItem as Invoice;
            if (selectedInvoice == null)
            {
                ShowErrorMessage("يرجى اختيار فاتورة للتعديل");
                return;
            }

            try
            {
                InvoiceFormOverlay.ShowEditInvoiceForm(selectedInvoice);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في فتح نموذج تعديل الفاتورة: {ex.Message}");
            }
        }

        private async void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الفاتورة رقم {selectedInvoice.InvoiceNumber}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _invoiceService.DeleteInvoiceAsync(selectedInvoice.Id);
                        await LoadDataAsync();
                        ShowSuccessMessage("تم حذف الفاتورة بنجاح");
                    }
                    catch (Exception ex)
                    {
                        ShowErrorMessage($"خطأ في حذف الفاتورة: {ex.Message}");
                    }
                }
            }
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = InvoicesDataGrid.SelectedItem != null;
            EditInvoiceButton.IsEnabled = hasSelection;
            DeleteInvoiceButton.IsEnabled = hasSelection;
        }

        private void InvoicesDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                ShowErrorMessage("نافذة تفاصيل الفاتورة قيد التطوير");
            }
        }

        private void ViewInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Invoice invoice)
            {
                ShowErrorMessage("نافذة تفاصيل الفاتورة قيد التطوير");
            }
        }

        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Invoice invoice)
            {
                ShowErrorMessage("نافذة إضافة الدفعة قيد التطوير");
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.CommandParameter is Invoice invoice)
            {
                ShowErrorMessage("وظيفة الطباعة قيد التطوير");
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
