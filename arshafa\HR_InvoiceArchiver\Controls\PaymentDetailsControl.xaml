<UserControl x:Class="HR_InvoiceArchiver.Controls.PaymentDetailsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Info Item Style -->
        <Style x:Key="InfoItemStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Icon Style -->
        <Style x:Key="IconStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="Margin" Value="0,0,15,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#6C757D"/>
            <Setter Property="Margin" Value="0,0,0,3"/>
        </Style>

        <!-- Value Style -->
        <Style x:Key="ValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <!-- Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="{Binding Background.Color, RelativeSource={RelativeSource TemplatedParent}}"
                                                Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Header -->
            <RowDefinition Height="*"/>    <!-- Content -->
            <RowDefinition Height="Auto"/> <!-- Footer -->
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" CornerRadius="12,12,0,0" Margin="0,0,0,0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#007BFF" Offset="0"/>
                    <GradientStop Color="#0056B3" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>
            
            <Grid Margin="30,25">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="#33FFFFFF" CornerRadius="50"
                            Width="60" Height="60" Margin="0,0,20,0">
                        <materialDesign:PackIcon Kind="CreditCard" Width="35" Height="35"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="HeaderTitle" Text="تفاصيل الدفعة" FontSize="28" FontWeight="Bold" Foreground="White"/>
                        <TextBlock x:Name="HeaderSubtitle" Text="معلومات شاملة عن الوصل" FontSize="14"
                                 Foreground="#CCFFFFFF" Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Close Button -->
                <Button Grid.Column="2" x:Name="CloseButton"
                        Style="{StaticResource MaterialDesignIconButton}"
                        Width="40" Height="40"
                        Background="#26FFFFFF"
                        Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24" Foreground="White"/>
                </Button>
            </Grid>
        </Border>

        <!-- Content Section -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30,20">
            <StackPanel x:Name="ContentPanel">
                <!-- Content will be added dynamically -->
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Section -->
        <Border Grid.Row="2" Background="#F8F9FA" 
                BorderBrush="#E9ECEF" BorderThickness="0,1,0,0"
                CornerRadius="0,0,12,12" Padding="30,20">
            <StackPanel x:Name="FooterPanel" Orientation="Horizontal" HorizontalAlignment="Right">
                <!-- Footer buttons will be added dynamically -->
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
