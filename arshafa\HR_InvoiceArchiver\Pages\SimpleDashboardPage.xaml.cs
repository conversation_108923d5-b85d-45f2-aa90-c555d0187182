using System.Windows.Controls;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SimpleDashboardPage : UserControl, INavigationAware
    {
        public SimpleDashboardPage()
        {
            Console.WriteLine("SimpleDashboardPage: Constructor called");
            System.Diagnostics.Debug.WriteLine("SimpleDashboardPage: Constructor called");
            InitializeComponent();
            Console.WriteLine("SimpleDashboardPage: InitializeComponent completed");
            System.Diagnostics.Debug.WriteLine("SimpleDashboardPage: InitializeComponent completed");
        }

        public void OnNavigatedTo(object parameter)
        {
            Console.WriteLine("SimpleDashboardPage: OnNavigatedTo called");
            System.Diagnostics.Debug.WriteLine("SimpleDashboardPage: OnNavigatedTo called");
        }

        public void OnNavigatedFrom()
        {
            Console.WriteLine("SimpleDashboardPage: OnNavigatedFrom called");
            System.Diagnostics.Debug.WriteLine("SimpleDashboardPage: OnNavigatedFrom called");
        }
    }
}
