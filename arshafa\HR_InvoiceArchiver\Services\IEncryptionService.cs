using System;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة التشفير
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// تشفير نص
        /// </summary>
        Task<string> EncryptAsync(string plainText, string? key = null);

        /// <summary>
        /// فك تشفير نص
        /// </summary>
        Task<string> DecryptAsync(string encryptedText, string? key = null);

        /// <summary>
        /// تشفير بيانات حساسة (كلمات المرور، أرقام البطاقات، إلخ)
        /// </summary>
        Task<string> EncryptSensitiveDataAsync(string sensitiveData);

        /// <summary>
        /// فك تشفير بيانات حساسة
        /// </summary>
        Task<string> DecryptSensitiveDataAsync(string encryptedData);

        /// <summary>
        /// إنشاء hash آمن للبيانات (للمقارنة فقط)
        /// </summary>
        Task<string> CreateHashAsync(string data, string? salt = null);

        /// <summary>
        /// التحقق من hash
        /// </summary>
        Task<bool> VerifyHashAsync(string data, string hash, string? salt = null);

        /// <summary>
        /// إنشاء مفتاح تشفير عشوائي
        /// </summary>
        Task<string> GenerateKeyAsync(int keySize = 256);

        /// <summary>
        /// إنشاء salt عشوائي
        /// </summary>
        Task<string> GenerateSaltAsync(int saltSize = 128);

        /// <summary>
        /// تشفير ملف
        /// </summary>
        Task<bool> EncryptFileAsync(string inputFilePath, string outputFilePath, string? key = null);

        /// <summary>
        /// فك تشفير ملف
        /// </summary>
        Task<bool> DecryptFileAsync(string inputFilePath, string outputFilePath, string? key = null);

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        Task<PasswordStrength> CheckPasswordStrengthAsync(string password);

        /// <summary>
        /// إنشاء كلمة مرور قوية
        /// </summary>
        Task<string> GenerateStrongPasswordAsync(int length = 12, bool includeSymbols = true);
    }

    /// <summary>
    /// مستويات قوة كلمة المرور
    /// </summary>
    public enum PasswordStrength
    {
        VeryWeak = 0,
        Weak = 1,
        Medium = 2,
        Strong = 3,
        VeryStrong = 4
    }

    /// <summary>
    /// معلومات قوة كلمة المرور
    /// </summary>
    public class PasswordStrengthInfo
    {
        public PasswordStrength Strength { get; set; }
        public int Score { get; set; }
        public string[] Suggestions { get; set; } = Array.Empty<string>();
        public bool HasUpperCase { get; set; }
        public bool HasLowerCase { get; set; }
        public bool HasNumbers { get; set; }
        public bool HasSymbols { get; set; }
        public bool HasMinimumLength { get; set; }
        public bool IsCommonPassword { get; set; }
    }
}
