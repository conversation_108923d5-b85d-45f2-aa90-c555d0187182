using System;
using System.IO;
using System.Text;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    public interface IGlobalExceptionHandler
    {
        void Initialize();
        void HandleException(Exception exception, string context = "");
        void LogError(string message, Exception? exception = null);
        void LogWarning(string message);
        void LogInfo(string message);
    }

    public class GlobalExceptionHandler : IGlobalExceptionHandler
    {
        private readonly ILogger<GlobalExceptionHandler> _logger;
        private readonly IToastService _toastService;
        private readonly string _logFilePath;
        private readonly object _logLock = new object();

        public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger, IToastService toastService)
        {
            _logger = logger;
            _toastService = toastService;
            
            // إنشاء مجلد السجلات
            var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            Directory.CreateDirectory(logsDirectory);
            
            _logFilePath = Path.Combine(logsDirectory, $"errors_{DateTime.Now:yyyy-MM-dd}.log");
        }

        public void Initialize()
        {
            // معالج الأخطاء غير المعالجة في التطبيق
            Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;
            
            // معالج الأخطاء في المهام غير المتزامنة
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
            
            // معالج الأخطاء العامة في AppDomain
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            LogInfo("Global Exception Handler initialized successfully");
        }

        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            HandleException(e.Exception, "UI Thread");
            e.Handled = true; // منع إغلاق التطبيق
        }

        private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            HandleException(e.Exception, "Background Task");
            e.SetObserved(); // منع إغلاق التطبيق
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception exception)
            {
                HandleException(exception, "AppDomain");
            }
        }

        public void HandleException(Exception exception, string context = "")
        {
            try
            {
                var errorMessage = FormatErrorMessage(exception, context);
                
                // تسجيل الخطأ
                _logger.LogError(exception, "Unhandled exception in {Context}", context);
                WriteToLogFile(errorMessage);

                // عرض رسالة للمستخدم
                ShowUserFriendlyError(exception, context);
            }
            catch (Exception logException)
            {
                // في حالة فشل تسجيل الخطأ، نعرض رسالة بسيطة
                try
                {
                    MessageBox.Show(
                        $"حدث خطأ غير متوقع:\n{exception.Message}\n\nخطأ إضافي في التسجيل:\n{logException.Message}",
                        "خطأ نظام",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
                catch
                {
                    // آخر محاولة - عرض رسالة أساسية
                    MessageBox.Show("حدث خطأ نظام خطير. يرجى إعادة تشغيل التطبيق.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ShowUserFriendlyError(Exception exception, string context)
        {
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                try
                {
                    var userMessage = GetUserFriendlyMessage(exception);
                    var title = GetErrorTitle(exception);

                    // استخدام Toast للأخطاء البسيطة
                    if (IsMinorError(exception))
                    {
                        _toastService.ShowError(title, userMessage);
                    }
                    else
                    {
                        // استخدام MessageBox للأخطاء الخطيرة
                        var result = MessageBox.Show(
                            $"{userMessage}\n\nهل تريد إرسال تقرير الخطأ؟",
                            title,
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Error);

                        if (result == MessageBoxResult.Yes)
                        {
                            // يمكن إضافة وظيفة إرسال التقرير هنا
                            _toastService.ShowInfo("تقرير الخطأ", "تم حفظ تقرير الخطأ محلياً");
                        }
                    }
                }
                catch
                {
                    // في حالة فشل عرض الرسالة المخصصة
                    MessageBox.Show("حدث خطأ غير متوقع", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            });
        }

        private string GetUserFriendlyMessage(Exception exception)
        {
            return exception switch
            {
                FileNotFoundException => "لم يتم العثور على الملف المطلوب",
                DirectoryNotFoundException => "لم يتم العثور على المجلد المطلوب",
                UnauthorizedAccessException => "ليس لديك صلاحية للوصول إلى هذا المورد",
                IOException => "حدث خطأ في عملية القراءة أو الكتابة",
                OutOfMemoryException => "الذاكرة غير كافية لإتمام العملية",
                ArgumentException => "تم تمرير قيمة غير صحيحة",
                InvalidOperationException => "العملية غير صالحة في الوضع الحالي",
                TimeoutException => "انتهت مهلة العملية",
                NotSupportedException => "العملية غير مدعومة",
                _ => "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى"
            };
        }

        private string GetErrorTitle(Exception exception)
        {
            return exception switch
            {
                FileNotFoundException or DirectoryNotFoundException => "ملف غير موجود",
                UnauthorizedAccessException => "خطأ في الصلاحيات",
                IOException => "خطأ في النظام",
                OutOfMemoryException => "خطأ في الذاكرة",
                ArgumentException => "خطأ في البيانات",
                InvalidOperationException => "خطأ في العملية",
                TimeoutException => "انتهاء المهلة",
                NotSupportedException => "عملية غير مدعومة",
                _ => "خطأ نظام"
            };
        }

        private bool IsMinorError(Exception exception)
        {
            return exception is ArgumentException or 
                   InvalidOperationException or 
                   TimeoutException or
                   NotSupportedException;
        }

        private string FormatErrorMessage(Exception exception, string context)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR in {context}");
            sb.AppendLine($"Exception Type: {exception.GetType().Name}");
            sb.AppendLine($"Message: {exception.Message}");
            sb.AppendLine($"Stack Trace: {exception.StackTrace}");
            
            if (exception.InnerException != null)
            {
                sb.AppendLine("Inner Exception:");
                sb.AppendLine($"  Type: {exception.InnerException.GetType().Name}");
                sb.AppendLine($"  Message: {exception.InnerException.Message}");
            }
            
            sb.AppendLine(new string('-', 80));
            return sb.ToString();
        }

        private void WriteToLogFile(string message)
        {
            lock (_logLock)
            {
                try
                {
                    File.AppendAllText(_logFilePath, message, Encoding.UTF8);
                }
                catch
                {
                    // تجاهل أخطاء الكتابة في ملف السجل
                }
            }
        }

        public void LogError(string message, Exception? exception = null)
        {
            _logger.LogError(exception, message);
            
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {message}";
            if (exception != null)
            {
                logMessage += $"\nException: {exception}";
            }
            logMessage += "\n" + new string('-', 40) + "\n";
            
            WriteToLogFile(logMessage);
        }

        public void LogWarning(string message)
        {
            _logger.LogWarning(message);
            
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] WARNING: {message}\n";
            WriteToLogFile(logMessage);
        }

        public void LogInfo(string message)
        {
            _logger.LogInformation(message);
            
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: {message}\n";
            WriteToLogFile(logMessage);
        }
    }
}
