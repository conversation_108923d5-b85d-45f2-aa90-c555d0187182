<UserControl x:Class="HR_InvoiceArchiver.Controls.OfferFormWindow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Enhanced Color Palette -->
            <SolidColorBrush x:Key="PrimaryBlue">#2196F3</SolidColorBrush>
            <SolidColorBrush x:Key="PrimaryDark">#1976D2</SolidColorBrush>
            <SolidColorBrush x:Key="AccentGreen">#4CAF50</SolidColorBrush>
            <SolidColorBrush x:Key="AccentOrange">#FF9800</SolidColorBrush>
            <SolidColorBrush x:Key="TextPrimary">#212121</SolidColorBrush>
            <SolidColorBrush x:Key="TextSecondary">#757575</SolidColorBrush>
            <SolidColorBrush x:Key="SurfaceColor">#FFFFFF</SolidColorBrush>
            <SolidColorBrush x:Key="BackgroundLight">#FAFBFC</SolidColorBrush>

            <!-- Modern Input Style -->
            <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                <Setter Property="Height" Value="48"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.8"/>
            </Style>

            <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                <Setter Property="Height" Value="48"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Padding" Value="12,8"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.8"/>
            </Style>

            <!-- Modern Button Styles -->
            <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource PrimaryBlue}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="Height" Value="44"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Padding" Value="24,0"/>
            </Style>

            <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="BorderBrush" Value="{StaticResource TextSecondary}"/>
                <Setter Property="Foreground" Value="{StaticResource TextSecondary}"/>
                <Setter Property="Height" Value="44"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Padding" Value="24,0"/>
            </Style>

            <!-- Modern GroupBox Style -->
            <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
                <Setter Property="Background" Value="{StaticResource BackgroundLight}"/>
                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="0,0,0,20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Enhanced Main Container -->
    <Border Background="White" CornerRadius="16" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Opacity="0.25"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header with Gradient -->
            <Border Grid.Row="0" CornerRadius="16,16,0,0">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#2196F3" Offset="0"/>
                        <GradientStop Color="#1976D2" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>

                <Grid Margin="24,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Icon -->
                    <Border Grid.Column="0" Background="rgba(255,255,255,0.2)"
                          CornerRadius="12" Padding="12" Margin="0,0,16,0">
                        <materialDesign:PackIcon Kind="Handshake" Width="28" Height="28" Foreground="White"/>
                    </Border>

                    <!-- Title and Subtitle -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock x:Name="HeaderTitle" Text="إضافة عرض جديد"
                                 FontSize="22" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="أدخل تفاصيل العرض المقدم من المندوب بدقة"
                                 FontSize="14" Foreground="#E3F2FD" Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Close Button -->
                    <Button Grid.Column="2" x:Name="CloseButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          Width="44" Height="44"
                          Background="rgba(255,255,255,0.1)"
                          Foreground="White"
                          Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="22" Height="22"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Enhanced Form Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                        Padding="32,24,32,0" Background="{StaticResource BackgroundLight}">
                <StackPanel>

                    <!-- معلومات المكتب والمندوب -->
                    <GroupBox Header="📋 معلومات المكتب والمندوب" Style="{StaticResource ModernGroupBoxStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="ScientificOfficeTextBox"
                                   Grid.Row="0" Grid.Column="0"
                                   Style="{StaticResource ModernTextBoxStyle}"
                                   Margin="0,0,12,20"
                                   materialDesign:HintAssist.Hint="المكتب العلمي *">
                                <materialDesign:HintAssist.HelperText>
                                    <TextBlock Text="اسم المكتب العلمي أو الشركة" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                </materialDesign:HintAssist.HelperText>
                            </TextBox>

                            <TextBox x:Name="RepresentativeNameTextBox"
                                   Grid.Row="0" Grid.Column="1"
                                   Style="{StaticResource ModernTextBoxStyle}"
                                   Margin="6,0,6,20"
                                   materialDesign:HintAssist.Hint="اسم المندوب *">
                                <materialDesign:HintAssist.HelperText>
                                    <TextBlock Text="الاسم الكامل للمندوب" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                </materialDesign:HintAssist.HelperText>
                            </TextBox>

                            <TextBox x:Name="RepresentativePhoneTextBox"
                                   Grid.Row="0" Grid.Column="2"
                                   Style="{StaticResource ModernTextBoxStyle}"
                                   Margin="12,0,0,20"
                                   materialDesign:HintAssist.Hint="رقم المندوب">
                                <materialDesign:HintAssist.HelperText>
                                    <TextBlock Text="رقم الهاتف للتواصل" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                </materialDesign:HintAssist.HelperText>
                            </TextBox>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات المادة -->
                    <GroupBox Header="🧪 معلومات المادة" Style="{StaticResource ModernGroupBoxStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox x:Name="ScientificNameComboBox"
                                    Grid.Row="0" Grid.Column="0"
                                    Style="{StaticResource ModernComboBoxStyle}"
                                    Margin="0,0,12,20"
                                    IsEditable="True"
                                    materialDesign:HintAssist.Hint="المادة العلمية *">
                                <materialDesign:HintAssist.HelperText>
                                    <TextBlock Text="اختر من القائمة أو أدخل اسم جديد" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                </materialDesign:HintAssist.HelperText>
                            </ComboBox>

                            <TextBox x:Name="TradeNameTextBox"
                                   Grid.Row="0" Grid.Column="1"
                                   Style="{StaticResource ModernTextBoxStyle}"
                                   Margin="12,0,0,20"
                                   materialDesign:HintAssist.Hint="المادة التجارية">
                                <materialDesign:HintAssist.HelperText>
                                    <TextBlock Text="الاسم التجاري للمادة" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                </materialDesign:HintAssist.HelperText>
                            </TextBox>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات السعر والعرض -->
                    <GroupBox Header="💰 معلومات السعر والعرض" Style="{StaticResource ModernGroupBoxStyle}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,12,20">
                                <TextBox x:Name="PriceTextBox"
                                       Style="{StaticResource ModernTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="السعر (د.ع) *">
                                    <materialDesign:HintAssist.HelperText>
                                        <TextBlock Text="السعر بالدينار العراقي" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                    </materialDesign:HintAssist.HelperText>
                                </TextBox>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="12,0,0,20">
                                <TextBox x:Name="BonusOrDiscountTextBox"
                                       Style="{StaticResource ModernTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="البونص أو الخصم">
                                    <materialDesign:HintAssist.HelperText>
                                        <TextBlock Text="مثال: 5% خصم أو هدية مجانية" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                    </materialDesign:HintAssist.HelperText>
                                </TextBox>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- ملاحظات ومرفقات -->
                    <GroupBox Header="📝 ملاحظات ومرفقات" Style="{StaticResource ModernGroupBoxStyle}">
                        <StackPanel>
                            <TextBox x:Name="NotesTextBox"
                                   Height="100"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   VerticalScrollBarVisibility="Auto"
                                   Style="{StaticResource ModernTextBoxStyle}"
                                   Margin="0,0,0,20"
                                   materialDesign:HintAssist.Hint="ملاحظات إضافية">
                                <materialDesign:HintAssist.HelperText>
                                    <TextBlock Text="أي معلومات إضافية حول العرض" FontSize="11" Foreground="{StaticResource TextSecondary}"/>
                                </materialDesign:HintAssist.HelperText>
                            </TextBox>

                            <!-- Enhanced Attachment Section -->
                            <Border Background="White" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1" Padding="16">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <materialDesign:PackIcon Grid.Column="0" Kind="Attachment"
                                                           Width="24" Height="24"
                                                           Foreground="{StaticResource PrimaryBlue}"
                                                           Margin="0,0,12,0" VerticalAlignment="Center"/>

                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock Text="المرفقات" FontWeight="Medium" FontSize="14"
                                                 Foreground="{StaticResource TextPrimary}"/>
                                        <TextBlock x:Name="AttachmentLabel"
                                                 Text="لم يتم اختيار ملف"
                                                 FontSize="12" Foreground="{StaticResource TextSecondary}"
                                                 Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <Button x:Name="AttachmentButton" Grid.Column="2"
                                          Style="{StaticResource SecondaryButton}"
                                          Height="36" Padding="16,0"
                                          Click="AttachmentButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Upload" Width="16" Height="16" Margin="0,0,6,0"/>
                                            <TextBlock Text="اختيار ملف"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </GroupBox>

                </StackPanel>
            </ScrollViewer>

            <!-- Enhanced Footer -->
            <Border Grid.Row="2" Background="White" CornerRadius="0,0,16,16"
                  BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
                <Grid Margin="32,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Validation Message -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="Information" Width="16" Height="16"
                                               Foreground="{StaticResource TextSecondary}" Margin="0,0,8,0"/>
                        <TextBlock Text="الحقول المطلوبة مميزة بعلامة *"
                                 FontSize="12" Foreground="{StaticResource TextSecondary}"/>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="ClearButton"
                              Style="{StaticResource SecondaryButton}"
                              Margin="0,0,12,0"
                              Click="ClearButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="تفريغ الحقول"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="CancelButton"
                              Style="{StaticResource SecondaryButton}"
                              Margin="0,0,12,0"
                              Click="CancelButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="إلغاء"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SaveButton"
                              Style="{StaticResource PrimaryButton}"
                              Click="SaveButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="حفظ العرض"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </Border>
</UserControl>
