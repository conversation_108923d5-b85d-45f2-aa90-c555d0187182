using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace HR_InvoiceArchiver.Controls
{
    public partial class SuccessNotification : UserControl
    {
        public event EventHandler? NotificationClosed;
        public event EventHandler? ViewInvoiceRequested;
        public event EventHandler? AddAnotherRequested;

        private bool _isAutoHideEnabled = true;
        private int _autoHideDelay = 5000; // 5 seconds

        public SuccessNotification()
        {
            InitializeComponent();
            Loaded += SuccessNotification_Loaded;
        }

        public SuccessNotification(string title, string message) : this()
        {
            TitleTextBlock.Text = title;
            MessageTextBlock.Text = message;
        }

        public SuccessNotification(string title, string message, bool autoHide, int autoHideDelay = 5000) : this()
        {
            TitleTextBlock.Text = title;
            MessageTextBlock.Text = message;
            _isAutoHideEnabled = autoHide;
            _autoHideDelay = autoHideDelay;
        }

        private async void SuccessNotification_Loaded(object sender, RoutedEventArgs e)
        {
            // Start main slide down animation
            var slideDownStoryboard = (Storyboard)Resources["SlideDownAnimation"];
            slideDownStoryboard.Begin();

            // Start icon animation after a short delay
            await Task.Delay(300);
            var iconStoryboard = (Storyboard)Resources["IconAnimation"];
            iconStoryboard.Begin();

            // Start progress animation
            await Task.Delay(200);
            var progressStoryboard = (Storyboard)Resources["ProgressAnimation"];
            progressStoryboard.Begin();

            // Auto-hide if enabled
            if (_isAutoHideEnabled)
            {
                await Task.Delay(_autoHideDelay);
                HideNotification();
            }
        }

        public void ShowNotification(string title, string message, bool autoHide = true, int autoHideDelay = 5000)
        {
            TitleTextBlock.Text = title;
            MessageTextBlock.Text = message;
            _isAutoHideEnabled = autoHide;
            _autoHideDelay = autoHideDelay;

            Visibility = Visibility.Visible;

            var slideDownStoryboard = (Storyboard)Resources["SlideDownAnimation"];
            slideDownStoryboard.Begin();
        }

        public void HideNotification()
        {
            var slideUpStoryboard = (Storyboard)Resources["SlideUpAnimation"];
            slideUpStoryboard.Begin();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            HideNotification();
        }

        private void ViewInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            ViewInvoiceRequested?.Invoke(this, EventArgs.Empty);
            HideNotification();
        }

        private void AddAnotherButton_Click(object sender, RoutedEventArgs e)
        {
            AddAnotherRequested?.Invoke(this, EventArgs.Empty);
            HideNotification();
        }

        private void SlideUpAnimation_Completed(object sender, EventArgs e)
        {
            Visibility = Visibility.Collapsed;
            NotificationClosed?.Invoke(this, EventArgs.Empty);
        }

        public void SetMessage(string title, string message)
        {
            TitleTextBlock.Text = title;
            MessageTextBlock.Text = message;
        }

        public void SetAutoHide(bool enabled, int delay = 5000)
        {
            _isAutoHideEnabled = enabled;
            _autoHideDelay = delay;
        }

        public void ShowSuccessMessage(string message, bool showActions = true)
        {
            TitleTextBlock.Text = "✨ نجحت العملية بامتياز!";
            MessageTextBlock.Text = message;

            // Show/hide action buttons based on parameter
            var actionPanel = (StackPanel)((Grid)((Border)NotificationCard).Child).Children[2];
            actionPanel.Visibility = showActions ? Visibility.Visible : Visibility.Collapsed;

            ShowNotification(TitleTextBlock.Text, message);
        }

        public void ShowErrorMessage(string message)
        {
            TitleTextBlock.Text = "❌ حدث خطأ!";
            MessageTextBlock.Text = message;

            // Change colors for error
            var border = (Border)NotificationCard;
            border.Background = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#F44336"));

            // Hide action buttons for errors
            var actionPanel = (StackPanel)((Grid)border.Child).Children[2];
            actionPanel.Visibility = Visibility.Collapsed;

            ShowNotification(TitleTextBlock.Text, message, true, 4000);
        }

        public void ShowWarningMessage(string message)
        {
            TitleTextBlock.Text = "⚠️ تحذير!";
            MessageTextBlock.Text = message;

            // Change colors for warning
            var border = (Border)NotificationCard;
            border.Background = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#FF9800"));

            // Hide action buttons for warnings
            var actionPanel = (StackPanel)((Grid)border.Child).Children[2];
            actionPanel.Visibility = Visibility.Collapsed;

            ShowNotification(TitleTextBlock.Text, message, true, 4000);
        }
    }
}
