# مرجع API - نظام أرشفة فواتير الموارد البشرية

## نظرة عامة

يوفر نظام أرشفة فواتير الموارد البشرية مجموعة شاملة من الخدمات والواجهات البرمجية لإدارة الفواتير والموردين والمدفوعات. هذا المرجع يوثق جميع الواجهات والطرق المتاحة للمطورين.

## الخدمات الأساسية

### IInvoiceService

خدمة إدارة الفواتير الرئيسية.

#### الطرق المتاحة

##### GetAllInvoicesAsync()
```csharp
Task<List<Invoice>> GetAllInvoicesAsync()
```
**الوصف**: استرجاع جميع الفواتير من قاعدة البيانات.

**القيمة المرجعة**: `Task<List<Invoice>>` - قائمة بجميع الفواتير

**مثال**:
```csharp
var invoices = await _invoiceService.GetAllInvoicesAsync();
```

##### GetInvoiceByIdAsync(int id)
```csharp
Task<Invoice?> GetInvoiceByIdAsync(int id)
```
**الوصف**: استرجاع فاتورة محددة بالمعرف.

**المعاملات**:
- `id` (int): معرف الفاتورة

**القيمة المرجعة**: `Task<Invoice?>` - الفاتورة أو null إذا لم توجد

**الاستثناءات**:
- `ArgumentException`: إذا كان المعرف أقل من أو يساوي صفر

**مثال**:
```csharp
var invoice = await _invoiceService.GetInvoiceByIdAsync(1);
if (invoice != null)
{
    Console.WriteLine($"رقم الفاتورة: {invoice.InvoiceNumber}");
}
```

##### CreateInvoiceAsync(Invoice invoice)
```csharp
Task<Invoice> CreateInvoiceAsync(Invoice invoice)
```
**الوصف**: إنشاء فاتورة جديدة.

**المعاملات**:
- `invoice` (Invoice): بيانات الفاتورة الجديدة

**القيمة المرجعة**: `Task<Invoice>` - الفاتورة المنشأة مع المعرف

**الاستثناءات**:
- `ArgumentNullException`: إذا كانت الفاتورة null
- `InvalidOperationException`: إذا كان رقم الفاتورة مكرر
- `ValidationException`: إذا كانت البيانات غير صحيحة

**مثال**:
```csharp
var newInvoice = new Invoice
{
    InvoiceNumber = "INV001",
    InvoiceDate = DateTime.Now,
    TotalAmount = 1000,
    SupplierId = 1,
    Status = InvoiceStatus.Pending
};

var createdInvoice = await _invoiceService.CreateInvoiceAsync(newInvoice);
```

##### UpdateInvoiceAsync(Invoice invoice)
```csharp
Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
```
**الوصف**: تحديث فاتورة موجودة.

**المعاملات**:
- `invoice` (Invoice): بيانات الفاتورة المحدثة

**القيمة المرجعة**: `Task<Invoice>` - الفاتورة المحدثة

**الاستثناءات**:
- `ArgumentNullException`: إذا كانت الفاتورة null
- `NotFoundException`: إذا لم توجد الفاتورة
- `ValidationException`: إذا كانت البيانات غير صحيحة

##### DeleteInvoiceAsync(int id)
```csharp
Task<bool> DeleteInvoiceAsync(int id)
```
**الوصف**: حذف فاتورة.

**المعاملات**:
- `id` (int): معرف الفاتورة

**القيمة المرجعة**: `Task<bool>` - true إذا تم الحذف بنجاح

**الاستثناءات**:
- `ArgumentException`: إذا كان المعرف غير صحيح
- `InvalidOperationException`: إذا كانت الفاتورة مرتبطة بمدفوعات

##### SearchInvoicesAsync(string searchTerm)
```csharp
Task<List<Invoice>> SearchInvoicesAsync(string searchTerm)
```
**الوصف**: البحث في الفواتير.

**المعاملات**:
- `searchTerm` (string): مصطلح البحث

**القيمة المرجعة**: `Task<List<Invoice>>` - قائمة الفواتير المطابقة

##### GetInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate)
```csharp
Task<List<Invoice>> GetInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate)
```
**الوصف**: استرجاع الفواتير في نطاق تاريخي.

**المعاملات**:
- `startDate` (DateTime): تاريخ البداية
- `endDate` (DateTime): تاريخ النهاية

**القيمة المرجعة**: `Task<List<Invoice>>` - قائمة الفواتير في النطاق المحدد

### ISupplierService

خدمة إدارة الموردين.

#### الطرق المتاحة

##### GetAllSuppliersAsync()
```csharp
Task<List<Supplier>> GetAllSuppliersAsync()
```
**الوصف**: استرجاع جميع الموردين.

##### GetActiveSuppliers()
```csharp
Task<List<Supplier>> GetActiveSuppliersAsync()
```
**الوصف**: استرجاع الموردين النشطين فقط.

##### CreateSupplierAsync(Supplier supplier)
```csharp
Task<Supplier> CreateSupplierAsync(Supplier supplier)
```
**الوصف**: إنشاء مورد جديد.

**الاستثناءات**:
- `ArgumentNullException`: إذا كان المورد null
- `InvalidOperationException`: إذا كان البريد الإلكتروني مكرر

##### UpdateSupplierAsync(Supplier supplier)
```csharp
Task<Supplier> UpdateSupplierAsync(Supplier supplier)
```
**الوصف**: تحديث مورد موجود.

##### DeactivateSupplierAsync(int id)
```csharp
Task<bool> DeactivateSupplierAsync(int id)
```
**الوصف**: إلغاء تفعيل مورد بدلاً من حذفه.

### IPaymentService

خدمة إدارة المدفوعات.

#### الطرق المتاحة

##### CreatePaymentAsync(Payment payment)
```csharp
Task<Payment> CreatePaymentAsync(Payment payment)
```
**الوصف**: تسجيل دفعة جديدة.

**الاستثناءات**:
- `ArgumentNullException`: إذا كانت الدفعة null
- `InvalidOperationException`: إذا كان المبلغ أكبر من المبلغ المستحق

##### GetPaymentsByInvoiceIdAsync(int invoiceId)
```csharp
Task<List<Payment>> GetPaymentsByInvoiceIdAsync(int invoiceId)
```
**الوصف**: استرجاع جميع المدفوعات لفاتورة محددة.

##### GetOutstandingInvoicesAsync()
```csharp
Task<List<Invoice>> GetOutstandingInvoicesAsync()
```
**الوصف**: استرجاع الفواتير غير المدفوعة بالكامل.

## خدمات التصدير والاستيراد

### IImportExportService

#### طرق التصدير

##### ExportInvoicesAsync(ExportOptions options)
```csharp
Task<ExportResult> ExportInvoicesAsync(ExportOptions options)
```
**الوصف**: تصدير الفواتير بصيغ مختلفة.

**المعاملات**:
- `options` (ExportOptions): خيارات التصدير

**مثال**:
```csharp
var options = new ExportOptions
{
    Format = ExportFormat.Excel,
    FilePath = @"C:\Exports\invoices.xlsx",
    StartDate = DateTime.Now.AddMonths(-1),
    EndDate = DateTime.Now
};

var result = await _importExportService.ExportInvoicesAsync(options);
if (result.Success)
{
    Console.WriteLine($"تم تصدير {result.RecordsExported} فاتورة");
}
```

##### ExportSuppliersAsync(ExportOptions options)
```csharp
Task<ExportResult> ExportSuppliersAsync(ExportOptions options)
```
**الوصف**: تصدير الموردين.

##### ExportPaymentsAsync(ExportOptions options)
```csharp
Task<ExportResult> ExportPaymentsAsync(ExportOptions options)
```
**الوصف**: تصدير المدفوعات.

#### طرق الاستيراد

##### ImportInvoicesAsync(ImportOptions options)
```csharp
Task<ImportResult> ImportInvoicesAsync(ImportOptions options)
```
**الوصف**: استيراد الفواتير من ملف.

**مثال**:
```csharp
var options = new ImportOptions
{
    FilePath = @"C:\Imports\invoices.xlsx",
    Format = ImportFormat.Excel,
    Mode = ImportMode.Upsert,
    ValidateData = true,
    SkipDuplicates = true,
    CreateBackup = true
};

var result = await _importExportService.ImportInvoicesAsync(options);
Console.WriteLine($"تم استيراد {result.RecordsImported} فاتورة");
```

##### ValidateImportFileAsync(string filePath, ImportFormat format)
```csharp
Task<ValidationResult> ValidateImportFileAsync(string filePath, ImportFormat format)
```
**الوصف**: التحقق من صحة ملف الاستيراد قبل المعالجة.

##### PreviewImportDataAsync(string filePath, ImportFormat format, int maxRows)
```csharp
Task<PreviewResult> PreviewImportDataAsync(string filePath, ImportFormat format, int maxRows = 100)
```
**الوصف**: معاينة البيانات قبل الاستيراد.

## خدمات النسخ الاحتياطي

### IBackupRestoreService

#### طرق النسخ الاحتياطي

##### CreateFullBackupAsync(BackupOptions options)
```csharp
Task<BackupResult> CreateFullBackupAsync(BackupOptions options)
```
**الوصف**: إنشاء نسخة احتياطية كاملة.

**مثال**:
```csharp
var options = new BackupOptions
{
    BackupPath = @"C:\Backups\full_backup.bak",
    IncludeAttachments = true,
    IncludeSettings = true,
    CompressBackup = true,
    EncryptBackup = true,
    Password = "SecurePassword123",
    Description = "نسخة احتياطية شهرية"
};

var result = await _backupService.CreateFullBackupAsync(options);
if (result.Success)
{
    Console.WriteLine($"تم إنشاء النسخة الاحتياطية: {result.BackupPath}");
    Console.WriteLine($"الحجم: {result.BackupSizeBytes} بايت");
}
```

##### CreateIncrementalBackupAsync(BackupOptions options)
```csharp
Task<BackupResult> CreateIncrementalBackupAsync(BackupOptions options)
```
**الوصف**: إنشاء نسخة احتياطية تزايدية.

##### CreateDifferentialBackupAsync(BackupOptions options)
```csharp
Task<BackupResult> CreateDifferentialBackupAsync(BackupOptions options)
```
**الوصف**: إنشاء نسخة احتياطية تفاضلية.

#### طرق الاستعادة

##### RestoreFromBackupAsync(RestoreOptions options)
```csharp
Task<RestoreResult> RestoreFromBackupAsync(RestoreOptions options)
```
**الوصف**: استعادة البيانات من نسخة احتياطية.

**مثال**:
```csharp
var options = new RestoreOptions
{
    BackupPath = @"C:\Backups\full_backup.bak",
    Mode = RestoreMode.Complete,
    CreateBackupBeforeRestore = true,
    VerifyIntegrity = true
};

var result = await _backupService.RestoreFromBackupAsync(options);
if (result.Success)
{
    Console.WriteLine($"تمت الاستعادة بنجاح");
    Console.WriteLine($"الجداول المستعادة: {result.TablesRestored}");
    Console.WriteLine($"السجلات المستعادة: {result.RecordsRestored}");
}
```

##### ValidateBackupAsync(string backupPath)
```csharp
Task<ValidationResult> ValidateBackupAsync(string backupPath)
```
**الوصف**: التحقق من صحة نسخة احتياطية.

##### TestRestoreAsync(string backupPath)
```csharp
Task<TestRestoreResult> TestRestoreAsync(string backupPath)
```
**الوصف**: اختبار إمكانية الاستعادة بدون تطبيق التغييرات.

## خدمات الأمان

### ISecurityService

#### طرق المصادقة

##### AuthenticateAsync(string username, string password)
```csharp
Task<AuthenticationResult> AuthenticateAsync(string username, string password)
```
**الوصف**: مصادقة المستخدم.

##### GetCurrentUserAsync()
```csharp
Task<User?> GetCurrentUserAsync()
```
**الوصف**: الحصول على المستخدم الحالي.

##### ChangePasswordAsync(string oldPassword, string newPassword)
```csharp
Task<bool> ChangePasswordAsync(string oldPassword, string newPassword)
```
**الوصف**: تغيير كلمة المرور.

### IEncryptionService

##### EncryptAsync(string data, string key)
```csharp
Task<string> EncryptAsync(string data, string key)
```
**الوصف**: تشفير البيانات.

##### DecryptAsync(string encryptedData, string key)
```csharp
Task<string> DecryptAsync(string encryptedData, string key)
```
**الوصف**: فك تشفير البيانات.

## خدمات التقارير

### IReportService

##### GenerateInvoiceReportAsync(ReportOptions options)
```csharp
Task<ReportResult> GenerateInvoiceReportAsync(ReportOptions options)
```
**الوصف**: إنشاء تقرير الفواتير.

**مثال**:
```csharp
var options = new ReportOptions
{
    ReportType = ReportType.Monthly,
    StartDate = new DateTime(2024, 1, 1),
    EndDate = new DateTime(2024, 1, 31),
    Format = ReportFormat.PDF,
    IncludeCharts = true
};

var result = await _reportService.GenerateInvoiceReportAsync(options);
```

##### GenerateSupplierReportAsync(ReportOptions options)
```csharp
Task<ReportResult> GenerateSupplierReportAsync(ReportOptions options)
```
**الوصف**: إنشاء تقرير الموردين.

##### GeneratePaymentReportAsync(ReportOptions options)
```csharp
Task<ReportResult> GeneratePaymentReportAsync(ReportOptions options)
```
**الوصف**: إنشاء تقرير المدفوعات.

## خدمات النظام

### ILoggingService

##### LogInformationAsync(string message)
```csharp
Task LogInformationAsync(string message)
```
**الوصف**: تسجيل رسالة معلوماتية.

##### LogWarningAsync(string message, Exception? exception = null)
```csharp
Task LogWarningAsync(string message, Exception? exception = null)
```
**الوصف**: تسجيل تحذير.

##### LogErrorAsync(string message, Exception exception)
```csharp
Task LogErrorAsync(string message, Exception exception)
```
**الوصف**: تسجيل خطأ.

### IToastService

##### ShowSuccess(string title, string message)
```csharp
void ShowSuccess(string title, string message)
```
**الوصف**: عرض إشعار نجاح.

##### ShowError(string title, string message)
```csharp
void ShowError(string title, string message)
```
**الوصف**: عرض إشعار خطأ.

##### ShowWarning(string title, string message)
```csharp
void ShowWarning(string title, string message)
```
**الوصف**: عرض إشعار تحذير.

##### ShowInfo(string title, string message)
```csharp
void ShowInfo(string title, string message)
```
**الوصف**: عرض إشعار معلوماتي.

## النماذج والتعدادات

### Invoice
```csharp
public class Invoice
{
    public int Id { get; set; }
    public string InvoiceNumber { get; set; }
    public DateTime InvoiceDate { get; set; }
    public decimal TotalAmount { get; set; }
    public int SupplierId { get; set; }
    public InvoiceStatus Status { get; set; }
    public string? Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Navigation Properties
    public Supplier Supplier { get; set; }
    public ICollection<Payment> Payments { get; set; }
}
```

### InvoiceStatus
```csharp
public enum InvoiceStatus
{
    Pending = 0,    // معلقة
    Paid = 1,       // مدفوعة
    Cancelled = 2,  // ملغاة
    Overdue = 3     // متأخرة
}
```

### ExportFormat
```csharp
public enum ExportFormat
{
    Excel = 0,
    CSV = 1,
    JSON = 2,
    XML = 3,
    PDF = 4
}
```

### BackupType
```csharp
public enum BackupType
{
    Full = 0,        // نسخة كاملة
    Incremental = 1, // نسخة تزايدية
    Differential = 2 // نسخة تفاضلية
}
```

## معالجة الأخطاء

### الاستثناءات المخصصة

#### ValidationException
```csharp
public class ValidationException : Exception
{
    public List<string> ValidationErrors { get; }
    
    public ValidationException(string message, List<string> errors) 
        : base(message)
    {
        ValidationErrors = errors;
    }
}
```

#### NotFoundException
```csharp
public class NotFoundException : Exception
{
    public NotFoundException(string entityName, object key) 
        : base($"{entityName} with key {key} was not found")
    {
    }
}
```

### مثال على معالجة الأخطاء

```csharp
try
{
    var invoice = await _invoiceService.CreateInvoiceAsync(newInvoice);
    _toastService.ShowSuccess("نجح", "تم إنشاء الفاتورة بنجاح");
}
catch (ValidationException ex)
{
    var errors = string.Join("\n", ex.ValidationErrors);
    _toastService.ShowError("خطأ في البيانات", errors);
}
catch (InvalidOperationException ex)
{
    _toastService.ShowError("خطأ في العملية", ex.Message);
}
catch (Exception ex)
{
    await _loggingService.LogErrorAsync("خطأ غير متوقع", ex);
    _toastService.ShowError("خطأ", "حدث خطأ غير متوقع");
}
```

## أمثلة شاملة

### مثال: إنشاء فاتورة مع مدفوعات

```csharp
public async Task CreateInvoiceWithPaymentAsync()
{
    try
    {
        // إنشاء الفاتورة
        var invoice = new Invoice
        {
            InvoiceNumber = "INV-2024-001",
            InvoiceDate = DateTime.Now,
            TotalAmount = 5000,
            SupplierId = 1,
            Status = InvoiceStatus.Pending,
            Description = "فاتورة خدمات استشارية"
        };

        var createdInvoice = await _invoiceService.CreateInvoiceAsync(invoice);

        // إضافة دفعة جزئية
        var payment = new Payment
        {
            InvoiceId = createdInvoice.Id,
            Amount = 2500,
            PaymentDate = DateTime.Now,
            PaymentMethod = "تحويل بنكي",
            Notes = "دفعة أولى - 50%"
        };

        await _paymentService.CreatePaymentAsync(payment);

        _toastService.ShowSuccess("نجح", "تم إنشاء الفاتورة والدفعة بنجاح");
    }
    catch (Exception ex)
    {
        await _loggingService.LogErrorAsync("فشل في إنشاء الفاتورة مع الدفعة", ex);
        _toastService.ShowError("خطأ", "فشل في إنشاء الفاتورة");
    }
}
```

### مثال: تصدير تقرير شامل

```csharp
public async Task ExportComprehensiveReportAsync()
{
    try
    {
        var startDate = DateTime.Now.AddMonths(-3);
        var endDate = DateTime.Now;

        // تصدير الفواتير
        var invoiceOptions = new ExportOptions
        {
            Format = ExportFormat.Excel,
            FilePath = @"C:\Reports\invoices_report.xlsx",
            StartDate = startDate,
            EndDate = endDate
        };

        var invoiceResult = await _importExportService.ExportInvoicesAsync(invoiceOptions);

        // تصدير المدفوعات
        var paymentOptions = new ExportOptions
        {
            Format = ExportFormat.Excel,
            FilePath = @"C:\Reports\payments_report.xlsx",
            StartDate = startDate,
            EndDate = endDate
        };

        var paymentResult = await _importExportService.ExportPaymentsAsync(paymentOptions);

        if (invoiceResult.Success && paymentResult.Success)
        {
            _toastService.ShowSuccess("نجح التصدير", 
                $"تم تصدير {invoiceResult.RecordsExported} فاتورة و {paymentResult.RecordsExported} دفعة");
        }
    }
    catch (Exception ex)
    {
        await _loggingService.LogErrorAsync("فشل في تصدير التقرير الشامل", ex);
        _toastService.ShowError("خطأ", "فشل في تصدير التقرير");
    }
}
```

---

**إصدار المرجع**: 1.0  
**تاريخ آخر تحديث**: 2024-01-15  
**التوافق**: .NET 8.0+
