using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HR_InvoiceArchiver.Tests.Models
{
    public class SupplierTests
    {
        [Fact]
        public void Supplier_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var supplier = new Supplier();

            // Assert
            supplier.Name.Should().Be(string.Empty);
            supplier.CreatedDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
            supplier.IsActive.Should().BeTrue();
            supplier.Invoices.Should().NotBeNull();
            supplier.Invoices.Should().BeEmpty();
        }

        [Fact]
        public void Supplier_TotalInvoicesAmount_WithNoInvoices_ShouldReturnZero()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>()
            };

            // Act
            var totalAmount = supplier.TotalInvoicesAmount;

            // Assert
            totalAmount.Should().Be(0);
        }

        [Fact]
        public void Supplier_TotalInvoicesAmount_WithInvoices_ShouldReturnSum()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice { Amount = 1000 },
                    new Invoice { Amount = 2000 },
                    new Invoice { Amount = 1500 }
                }
            };

            // Act
            var totalAmount = supplier.TotalInvoicesAmount;

            // Assert
            totalAmount.Should().Be(4500);
        }

        [Fact]
        public void Supplier_TotalPaidAmount_WithNoInvoices_ShouldReturnZero()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>()
            };

            // Act
            var totalPaidAmount = supplier.TotalPaidAmount;

            // Assert
            totalPaidAmount.Should().Be(0);
        }

        [Fact]
        public void Supplier_TotalPaidAmount_WithInvoices_ShouldReturnSum()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice { PaidAmount = 500 },
                    new Invoice { PaidAmount = 1000 },
                    new Invoice { PaidAmount = 300 }
                }
            };

            // Act
            var totalPaidAmount = supplier.TotalPaidAmount;

            // Assert
            totalPaidAmount.Should().Be(1800);
        }

        [Fact]
        public void Supplier_RemainingAmount_ShouldCalculateCorrectly()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice { Amount = 1000, PaidAmount = 300 },
                    new Invoice { Amount = 2000, PaidAmount = 1000 },
                    new Invoice { Amount = 1500, PaidAmount = 1500 }
                }
            };

            // Act
            var remainingAmount = supplier.RemainingAmount;

            // Assert
            remainingAmount.Should().Be(1700); // (1000+2000+1500) - (300+1000+1500)
        }

        [Fact]
        public void Supplier_TotalInvoicesCount_WithInvoices_ShouldReturnCorrectCount()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice(),
                    new Invoice(),
                    new Invoice()
                }
            };

            // Act
            var count = supplier.TotalInvoicesCount;

            // Assert
            count.Should().Be(3);
        }

        [Fact]
        public void Supplier_PaidInvoicesCount_ShouldReturnCorrectCount()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice { Status = InvoiceStatus.Paid },
                    new Invoice { Status = InvoiceStatus.Unpaid },
                    new Invoice { Status = InvoiceStatus.Paid },
                    new Invoice { Status = InvoiceStatus.PartiallyPaid }
                }
            };

            // Act
            var paidCount = supplier.PaidInvoicesCount;

            // Assert
            paidCount.Should().Be(2);
        }

        [Fact]
        public void Supplier_UnpaidInvoicesCount_ShouldReturnCorrectCount()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice { Status = InvoiceStatus.Paid },
                    new Invoice { Status = InvoiceStatus.Unpaid },
                    new Invoice { Status = InvoiceStatus.Unpaid },
                    new Invoice { Status = InvoiceStatus.PartiallyPaid }
                }
            };

            // Act
            var unpaidCount = supplier.UnpaidInvoicesCount;

            // Assert
            unpaidCount.Should().Be(2);
        }

        [Fact]
        public void Supplier_PartiallyPaidInvoicesCount_ShouldReturnCorrectCount()
        {
            // Arrange
            var supplier = new Supplier
            {
                Invoices = new List<Invoice>
                {
                    new Invoice { Status = InvoiceStatus.Paid },
                    new Invoice { Status = InvoiceStatus.Unpaid },
                    new Invoice { Status = InvoiceStatus.PartiallyPaid },
                    new Invoice { Status = InvoiceStatus.PartiallyPaid }
                }
            };

            // Act
            var partiallyPaidCount = supplier.PartiallyPaidInvoicesCount;

            // Assert
            partiallyPaidCount.Should().Be(2);
        }

        [Theory]
        [InlineData("")]
        [InlineData("a")]
        [InlineData("مورد")]
        public void Supplier_Name_WithInvalidLength_ShouldFailValidation(string name)
        {
            // Arrange
            var supplier = new Supplier { Name = name };

            // Act & Assert
            // هذا الاختبار يتطلب تطبيق validation attributes
            // في الوقت الحالي نتحقق فقط من أن القيمة تم تعيينها
            supplier.Name.Should().Be(name);
        }

        [Theory]
        [InlineData("مورد صحيح")]
        [InlineData("Supplier Name")]
        [InlineData("مورد123")]
        public void Supplier_Name_WithValidLength_ShouldPassValidation(string name)
        {
            // Arrange
            var supplier = new Supplier { Name = name };

            // Act & Assert
            supplier.Name.Should().Be(name);
        }

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("")]
        public void Supplier_Email_WithValidFormat_ShouldBeAccepted(string email)
        {
            // Arrange
            var supplier = new Supplier { Email = email };

            // Act & Assert
            supplier.Email.Should().Be(email);
        }

        [Fact]
        public void Supplier_Email_WithNull_ShouldBeAccepted()
        {
            // Arrange
            var supplier = new Supplier { Email = null! };

            // Act & Assert
            supplier.Email.Should().BeNull();
        }

        [Theory]
        [InlineData("0*********0")]
        [InlineData("+20*********")]
        [InlineData("(02) 1234-5678")]
        [InlineData("")]
        public void Supplier_Phone_WithValidFormat_ShouldBeAccepted(string phone)
        {
            // Arrange
            var supplier = new Supplier { Phone = phone };

            // Act & Assert
            supplier.Phone.Should().Be(phone);
        }

        [Fact]
        public void Supplier_Phone_WithNull_ShouldBeAccepted()
        {
            // Arrange
            var supplier = new Supplier { Phone = null! };

            // Act & Assert
            supplier.Phone.Should().BeNull();
        }

        [Fact]
        public void Supplier_UpdatedDate_ShouldBeNullByDefault()
        {
            // Arrange & Act
            var supplier = new Supplier();

            // Assert
            supplier.UpdatedDate.Should().BeNull();
        }

        [Fact]
        public void Supplier_UpdatedDate_CanBeSet()
        {
            // Arrange
            var supplier = new Supplier();
            var updateTime = DateTime.Now;

            // Act
            supplier.UpdatedDate = updateTime;

            // Assert
            supplier.UpdatedDate.Should().Be(updateTime);
        }

        [Fact]
        public void Supplier_AllProperties_CanBeSetAndRetrieved()
        {
            // Arrange
            var supplier = new Supplier();
            var testData = new
            {
                Name = "مورد تجريبي",
                ContactPerson = "أحمد محمد",
                Phone = "0*********0",
                Email = "<EMAIL>",
                Address = "القاهرة، مصر",
                TaxNumber = "*********",
                Description = "وصف المورد",
                Notes = "ملاحظات إضافية",
                IsActive = false
            };

            // Act
            supplier.Name = testData.Name;
            supplier.ContactPerson = testData.ContactPerson;
            supplier.Phone = testData.Phone;
            supplier.Email = testData.Email;
            supplier.Address = testData.Address;
            supplier.TaxNumber = testData.TaxNumber;
            supplier.Description = testData.Description;
            supplier.Notes = testData.Notes;
            supplier.IsActive = testData.IsActive;

            // Assert
            supplier.Name.Should().Be(testData.Name);
            supplier.ContactPerson.Should().Be(testData.ContactPerson);
            supplier.Phone.Should().Be(testData.Phone);
            supplier.Email.Should().Be(testData.Email);
            supplier.Address.Should().Be(testData.Address);
            supplier.TaxNumber.Should().Be(testData.TaxNumber);
            supplier.Description.Should().Be(testData.Description);
            supplier.Notes.Should().Be(testData.Notes);
            supplier.IsActive.Should().Be(testData.IsActive);
        }
    }
}
