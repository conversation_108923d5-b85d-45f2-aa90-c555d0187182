<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="HR_InvoiceArchiver.Windows.SearchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="البحث المتقدم والتقارير - أرشيف الفواتير"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <!-- Material Design Resource Dictionary -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{DynamicResource MaterialDesignPaper}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="Magnify" Width="24" Height="24"
                                       VerticalAlignment="Center" Margin="0,0,12,0"/>

                <TextBlock Grid.Column="1" Text="البحث المتقدم والتقارير"
                          VerticalAlignment="Center" FontSize="18" FontWeight="Medium"/>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           materialDesign:ButtonAssist.CornerRadius="4" Margin="0,0,8,0"
                           Click="ExportButton_Click" x:Name="ExportButton">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           materialDesign:ButtonAssist.CornerRadius="4"
                           Click="ClearButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="مسح"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Statistics Dashboard -->
        <Grid Grid.Row="1" Margin="16,16,16,8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Invoices Card -->
            <materialDesign:Card Grid.Column="0" materialDesign:ElevationAssist.Elevation="Dp2"
                               Margin="0,0,8,0" Padding="16">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="إجمالي الفواتير"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalInvoicesTextBlock" Text="{Binding TotalInvoices, StringFormat=N0, FallbackValue=0}"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}" FontWeight="Bold"/>
                        </StackPanel>
                        <materialDesign:PackIcon Grid.Column="1" Kind="FileDocument"
                                               Width="32" Height="32"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Amount Card -->
            <materialDesign:Card Grid.Column="1" materialDesign:ElevationAssist.Elevation="Dp2"
                               Margin="0,0,8,0" Padding="16">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="إجمالي المبلغ (د.ع)"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalAmountTextBlock" Text="{Binding TotalAmount, StringFormat=N0, FallbackValue=0}"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="{DynamicResource SecondaryHueMidBrush}" FontWeight="Bold"/>
                        </StackPanel>
                        <materialDesign:PackIcon Grid.Column="1" Kind="CurrencyUsd"
                                               Width="32" Height="32"
                                               Foreground="{DynamicResource SecondaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Paid Amount Card -->
            <materialDesign:Card Grid.Column="2" materialDesign:ElevationAssist.Elevation="Dp2"
                               Margin="0,0,8,0" Padding="16">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="المبلغ المسدد (د.ع)"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="PaidAmountTextBlock" Text="{Binding PaidAmount, StringFormat=N0, FallbackValue=0}"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="#4CAF50" FontWeight="Bold"/>
                        </StackPanel>
                        <materialDesign:PackIcon Grid.Column="1" Kind="CheckCircle"
                                               Width="32" Height="32"
                                               Foreground="#4CAF50"
                                               VerticalAlignment="Center"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Outstanding Amount Card -->
            <materialDesign:Card Grid.Column="3" materialDesign:ElevationAssist.Elevation="Dp2"
                               Padding="16">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="المبلغ المتبقي (د.ع)"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="OutstandingAmountTextBlock" Text="{Binding OutstandingAmount, StringFormat=N0, FallbackValue=0}"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="#F44336" FontWeight="Bold"/>
                        </StackPanel>
                        <materialDesign:PackIcon Grid.Column="1" Kind="AlertCircle"
                                               Width="32" Height="32"
                                               Foreground="#F44336"
                                               VerticalAlignment="Center"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Main Content -->
        <Grid Grid.Row="2" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="380"/>
                <ColumnDefinition Width="8"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="8"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- Search Filters Panel -->
            <materialDesign:Card Grid.Column="0" materialDesign:ElevationAssist.Elevation="Dp2" Padding="16">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="فلاتر البحث" Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  Margin="0,0,0,16" Foreground="{DynamicResource MaterialDesignBody}"/>

                        <!-- Invoice Number -->
                        <TextBox x:Name="InvoiceNumberTextBox"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                materialDesign:HintAssist.Hint="رقم الفاتورة"
                                Margin="0,0,0,16"/>

                        <!-- Supplier -->
                        <ComboBox x:Name="SupplierComboBox"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 materialDesign:HintAssist.Hint="اختر المورد"
                                 DisplayMemberPath="Name" SelectedValuePath="Id"
                                 Margin="0,0,0,16"/>

                        <!-- Date Range -->
                        <TextBlock Text="تاريخ الفاتورة" Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Margin="0,0,0,8"/>
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <DatePicker x:Name="FromDatePicker" Grid.Column="0"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       materialDesign:HintAssist.Hint="من تاريخ"/>
                            <TextBlock Grid.Column="1" Text="إلى" VerticalAlignment="Center" HorizontalAlignment="Center"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            <DatePicker x:Name="ToDatePicker" Grid.Column="2"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       materialDesign:HintAssist.Hint="إلى تاريخ"/>
                        </Grid>

                        <!-- Due Date Range -->
                        <TextBlock Text="تاريخ الاستحقاق" Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Margin="0,0,0,8"/>
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <DatePicker x:Name="DueFromDatePicker" Grid.Column="0"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       materialDesign:HintAssist.Hint="من تاريخ الاستحقاق"/>
                            <TextBlock Grid.Column="1" Text="إلى" VerticalAlignment="Center" HorizontalAlignment="Center"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            <DatePicker x:Name="DueToDatePicker" Grid.Column="2"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       materialDesign:HintAssist.Hint="إلى تاريخ الاستحقاق"/>
                        </Grid>

                        <!-- Amount Range -->
                        <TextBlock Text="نطاق المبلغ" Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Margin="0,0,0,8"/>
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBox x:Name="MinAmountTextBox" Grid.Column="0"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="الحد الأدنى"
                                    Text="{Binding MinAmount, UpdateSourceTrigger=PropertyChanged}"/>
                            <TextBlock Grid.Column="1" Text="إلى" VerticalAlignment="Center" HorizontalAlignment="Center"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                            <TextBox x:Name="MaxAmountTextBox" Grid.Column="2"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    materialDesign:HintAssist.Hint="الحد الأعلى"
                                    Text="{Binding MaxAmount, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <!-- Status -->
                        <ComboBox x:Name="StatusComboBox"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 materialDesign:HintAssist.Hint="حالة الفاتورة"
                                 Margin="0,0,0,16">
                            <ComboBoxItem Content="جميع الحالات" Tag="All"/>
                            <ComboBoxItem Content="غير مسددة" Tag="Unpaid"/>
                            <ComboBoxItem Content="تسديد جزئي" Tag="PartiallyPaid"/>
                            <ComboBoxItem Content="مسددة" Tag="Paid"/>
                        </ComboBox>

                        <!-- Overdue Filter -->
                        <CheckBox x:Name="OverdueOnlyCheckBox" Content="الفواتير المتأخرة فقط"
                                 Style="{StaticResource MaterialDesignCheckBox}"
                                 Margin="0,0,0,16"/>

                        <!-- Search Buttons -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                            <Button x:Name="SearchButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                   materialDesign:ButtonAssist.CornerRadius="4"
                                   Margin="0,0,8,0" Click="SearchButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16" Margin="0,0,4,0"/>
                                    <TextBlock Text="بحث"/>
                                </StackPanel>
                            </Button>
                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                   materialDesign:ButtonAssist.CornerRadius="4"
                                   Click="ClearButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,4,0"/>
                                    <TextBlock Text="مسح"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Results Panel -->
            <materialDesign:Card Grid.Column="2" materialDesign:ElevationAssist.Elevation="Dp2" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Results Header -->
                    <Grid Grid.Row="0" Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20"
                                                   VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="نتائج البحث" Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                      VerticalAlignment="Center" Margin="0,0,12,0"/>
                            <TextBlock x:Name="ResultCountTextBlock" Text="0 فاتورة"
                                      VerticalAlignment="Center"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Results DataGrid -->
                    <DataGrid x:Name="ResultsDataGrid" Grid.Row="1"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             RowHeight="48"
                             materialDesign:DataGridAssist.CellPadding="8,4"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8,4"
                             SelectionChanged="ResultsDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding Supplier.Name}" Width="150"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTextColumn Header="تاريخ الفاتورة" Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" Width="120"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat=yyyy/MM/dd}" Width="120"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTextColumn Header="المبلغ (د.ع)" Binding="{Binding Amount, StringFormat=N0}" Width="100"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTextColumn Header="المدفوع (د.ع)" Binding="{Binding PaidAmount, StringFormat=N0}" Width="100"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTextColumn Header="المتبقي (د.ع)" Binding="{Binding RemainingAmount, StringFormat=N0}" Width="100"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                            <DataGridTemplateColumn Header="الحالة" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <materialDesign:Chip Content="{Binding StatusText}"
                                                           Background="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                                           Foreground="White" FontSize="11" Margin="4"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"
                                               materialDesign:DataGridAssist.CellPadding="8,4"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Pagination -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                        <Button x:Name="FirstPageButton" Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="الصفحة الأولى" Margin="0,0,4,0" Click="FirstPageButton_Click">
                            <materialDesign:PackIcon Kind="PageFirst" Width="18" Height="18"/>
                        </Button>
                        <Button x:Name="PrevPageButton" Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="الصفحة السابقة" Margin="0,0,4,0" Click="PrevPageButton_Click">
                            <materialDesign:PackIcon Kind="ChevronRight" Width="18" Height="18"/>
                        </Button>
                        <TextBlock x:Name="PageInfoTextBlock" Text="صفحة 1 من 1"
                                  VerticalAlignment="Center" Margin="12,0"
                                  Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                        <Button x:Name="NextPageButton" Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="الصفحة التالية" Margin="4,0,0,0" Click="NextPageButton_Click">
                            <materialDesign:PackIcon Kind="ChevronLeft" Width="18" Height="18"/>
                        </Button>
                        <Button x:Name="LastPageButton" Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="الصفحة الأخيرة" Margin="4,0,0,0" Click="LastPageButton_Click">
                            <materialDesign:PackIcon Kind="PageLast" Width="18" Height="18"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Charts Panel -->
            <materialDesign:Card Grid.Column="4" materialDesign:ElevationAssist.Elevation="Dp2" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Chart Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <materialDesign:PackIcon Kind="ChartPie" Width="20" Height="20"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="توزيع حالات الفواتير"
                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Chart Content -->
                    <Grid Grid.Row="1">
                        <lvc:PieChart x:Name="StatusPieChart"
                                    LegendLocation="Bottom"
                                    InnerRadius="40"
                                    AnimationsSpeed="0:0:0.5"
                                    Hoverable="True"
                                    DataTooltip="{x:Null}">
                            <lvc:PieChart.ChartLegend>
                                <lvc:DefaultLegend BulletSize="15" Foreground="{DynamicResource MaterialDesignBody}"/>
                            </lvc:PieChart.ChartLegend>
                        </lvc:PieChart>

                        <!-- No Data Message -->
                        <StackPanel x:Name="NoDataPanel"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  Visibility="Collapsed">
                            <materialDesign:PackIcon Kind="ChartPieOutline"
                                                   Width="48" Height="48"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="لا توجد بيانات للعرض"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     HorizontalAlignment="Center"
                                     Margin="0,8,0,0"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <Grid Grid.Row="3" Background="{DynamicResource MaterialDesignDivider}" Height="24">
            <TextBlock x:Name="StatusTextBlock" Text="جاهز"
                      VerticalAlignment="Center" Margin="16,0"
                      Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
        </Grid>
    </Grid>
</Window>
