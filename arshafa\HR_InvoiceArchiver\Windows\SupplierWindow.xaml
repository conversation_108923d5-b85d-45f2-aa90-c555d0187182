<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="HR_InvoiceArchiver.Windows.SupplierWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين - أرشيف الفواتير"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

    <Window.Resources>
        <!-- Styles -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>

        <Style x:Key="InputStyle" TargetType="Control">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderBrush" Value="#BDC3C7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2980B9"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#27AE60"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#229954"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#F39C12"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E67E22"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#E74C3C"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C0392B"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#95A5A6"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#7F8C8D"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Background="#ECF0F1">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="إدارة الموردين" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock Text="إضافة وتعديل وإدارة بيانات الموردين"
                              Foreground="#7F8C8D" FontSize="12"/>
                </StackPanel>

                <!-- Quick Add Button -->
                <Button Grid.Column="1"
                       x:Name="QuickAddButton"
                       Style="{StaticResource SuccessButtonStyle}"
                       Click="AddNewButton_Click"
                       MinWidth="180"
                       Height="50"
                       FontSize="16"
                       FontWeight="Bold"
                       VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="➕" FontSize="20" Margin="0,0,10,0" VerticalAlignment="Center"/>
                        <TextBlock Text="إضافة مورد جديد" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Supplier Form Panel -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock x:Name="FormHeaderTextBlock" Text="إضافة مورد جديد" Style="{StaticResource HeaderStyle}"/>

                    <!-- Name -->
                    <TextBlock Text="اسم المورد *" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="NameTextBox" Style="{StaticResource InputStyle}" Margin="0,0,0,15"/>

                    <!-- Contact Person -->
                    <TextBlock Text="الشخص المسؤول" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="ContactPersonTextBox" Style="{StaticResource InputStyle}" Margin="0,0,0,15"/>

                    <!-- Phone -->
                    <TextBlock Text="رقم الهاتف" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="PhoneTextBox" Style="{StaticResource InputStyle}" Margin="0,0,0,15"/>

                    <!-- Email -->
                    <TextBlock Text="البريد الإلكتروني" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="EmailTextBox" Style="{StaticResource InputStyle}" Margin="0,0,0,15"/>

                    <!-- Address -->
                    <TextBlock Text="العنوان" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="AddressTextBox" Style="{StaticResource InputStyle}" 
                            Height="80" TextWrapping="Wrap" AcceptsReturn="True" 
                            VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>

                    <!-- Tax Number -->
                    <TextBlock Text="الرقم الضريبي" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="TaxNumberTextBox" Style="{StaticResource InputStyle}" Margin="0,0,0,15"/>

                    <!-- Notes -->
                    <TextBlock Text="ملاحظات" Style="{StaticResource LabelStyle}"/>
                    <TextBox x:Name="NotesTextBox" Style="{StaticResource InputStyle}" 
                            Height="60" TextWrapping="Wrap" AcceptsReturn="True" 
                            VerticalScrollBarVisibility="Auto" Margin="0,0,0,20"/>

                    <!-- Form Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="SaveButton" Content="حفظ" Style="{StaticResource SuccessButtonStyle}" 
                               Margin="0,0,10,0" Click="SaveButton_Click"/>
                        <Button x:Name="CancelButton" Content="إلغاء" Style="{StaticResource SecondaryButtonStyle}" 
                               Margin="0,0,10,0" Click="CancelButton_Click"/>
                        <Button x:Name="DeleteButton" Content="حذف" Style="{StaticResource DangerButtonStyle}" 
                               Visibility="Collapsed" Click="DeleteButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Suppliers List Panel -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- List Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="قائمة الموردين" Style="{StaticResource HeaderStyle}" Margin="0,0,20,0"/>
                        <TextBlock x:Name="SupplierCountTextBlock" Text="0 مورد" 
                                  VerticalAlignment="Center" Foreground="#7F8C8D"/>
                    </StackPanel>

                    <!-- Search Bar and Add Button -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox x:Name="SearchTextBox" Grid.Column="0" Style="{StaticResource InputStyle}"
                                Margin="0,0,15,0" TextChanged="SearchTextBox_TextChanged"
                                Tag="البحث في الموردين..." Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
                        <Button x:Name="AddNewButton" Grid.Column="1"
                               Style="{StaticResource SuccessButtonStyle}"
                               Click="AddNewButton_Click"
                               MinWidth="150"
                               Height="45"
                               FontSize="14"
                               FontWeight="Bold">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="إضافة مورد جديد" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>

                    <!-- Suppliers DataGrid -->
                    <DataGrid x:Name="SuppliersDataGrid" Grid.Row="2" 
                             AutoGenerateColumns="False" 
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Single"
                             AlternatingRowBackground="#F8F9FA"
                             RowHeight="40"
                             FontSize="12"
                             SelectionChanged="SuppliersDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم المورد" Binding="{Binding Name}" Width="200"/>
                            <DataGridTextColumn Header="الشخص المسؤول" Binding="{Binding ContactPerson}" Width="150"/>
                            <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                            <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="180"/>
                            <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="100"/>
                            <DataGridTextColumn Header="إجمالي المبلغ" Binding="{Binding TotalAmount, StringFormat=N2}" Width="120"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="تعديل" Style="{StaticResource WarningButtonStyle}" 
                                                   Height="30" Padding="10,0" FontSize="11" Margin="0,0,5,0"
                                                   Click="EditButton_Click" Tag="{Binding}"/>
                                            <Button Content="كشف حساب" Style="{StaticResource PrimaryButtonStyle}" 
                                                   Height="30" Padding="10,0" FontSize="11"
                                                   Click="StatementButton_Click" Tag="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Statistics Panel -->
                    <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="5" Padding="15" Margin="0,15,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock x:Name="TotalSuppliersTextBlock" Text="إجمالي الموردين: 0" 
                                      FontWeight="SemiBold" Margin="0,0,30,0"/>
                            <TextBlock x:Name="ActiveSuppliersTextBlock" Text="الموردين النشطين: 0" 
                                      FontWeight="SemiBold" Margin="0,0,30,0"/>
                            <TextBlock x:Name="TotalInvoicesTextBlock" Text="إجمالي الفواتير: 0" 
                                      FontWeight="SemiBold" Margin="0,0,30,0"/>
                            <TextBlock x:Name="TotalAmountTextBlock" Text="إجمالي المبلغ: 0.00 د.ع"
                                      FontWeight="SemiBold"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#34495E" Height="30">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                <TextBlock x:Name="StatusTextBlock" Text="جاهز" Foreground="White" FontSize="12"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
