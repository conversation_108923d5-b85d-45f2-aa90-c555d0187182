using System;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Controls
{
    /// <summary>
    /// نافذة تأكيد حديثة وأنيقة للعمليات الحساسة
    /// </summary>
    public partial class ConfirmationDialog : UserControl
    {
        public event EventHandler<bool>? DialogResult;

        public ConfirmationDialog()
        {
            InitializeComponent();
        }

        /// <summary>
        /// إعداد نافذة التأكيد لحذف مدفوعة
        /// </summary>
        public void SetupForPaymentDeletion(Payment payment)
        {
            DialogTitle.Text = "تأكيد حذف المدفوعة";
            DialogMessage.Text = "هل أنت متأكد من رغبتك في حذف هذه المدفوعة؟ سيتم حذف جميع البيانات والمرفقات المرتبطة بها نهائياً.";
            
            // عرض تفاصيل المدفوعة
            PaymentDetailsPanel.Visibility = Visibility.Visible;
            ReceiptNumberText.Text = payment.ReceiptNumber;
            SupplierNameText.Text = payment.SupplierName ?? "غير محدد";
            AmountText.Text = $"{payment.Amount:N0} د.ع";

            // تخصيص الألوان للحذف
            DialogIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.DeleteAlert;
            ConfirmButton.Content = CreateButtonContent("Delete", "نعم، احذف");
        }

        /// <summary>
        /// إعداد نافذة تأكيد عامة
        /// </summary>
        public void SetupGeneral(string title, string message, string confirmText = "تأكيد", string cancelText = "إلغاء")
        {
            DialogTitle.Text = title;
            DialogMessage.Text = message;
            PaymentDetailsPanel.Visibility = Visibility.Collapsed;

            ConfirmButton.Content = CreateButtonContent("Check", confirmText);
            CancelButton.Content = CreateButtonContent("Cancel", cancelText);
        }

        /// <summary>
        /// إعداد نافذة تأكيد للعمليات الخطيرة
        /// </summary>
        public void SetupForDangerousAction(string title, string message, string itemDetails = "")
        {
            DialogTitle.Text = title;
            DialogMessage.Text = message;
            
            if (!string.IsNullOrEmpty(itemDetails))
            {
                PaymentDetailsPanel.Visibility = Visibility.Visible;
                // يمكن تخصيص عرض التفاصيل حسب الحاجة
            }

            DialogIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.AlertCircle;
            DialogIcon.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(220, 53, 69));
            
            ConfirmButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(220, 53, 69));
            ConfirmButton.Content = CreateButtonContent("AlertCircle", "تأكيد");
        }

        /// <summary>
        /// إنشاء محتوى الزر مع أيقونة
        /// </summary>
        private StackPanel CreateButtonContent(string iconKind, string text)
        {
            var stackPanel = new StackPanel { Orientation = Orientation.Horizontal };
            
            var icon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Width = 18,
                Height = 18,
                Margin = new Thickness(0, 0, 8, 0)
            };

            // تحديد نوع الأيقونة
            switch (iconKind.ToLower())
            {
                case "delete":
                    icon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Delete;
                    break;
                case "check":
                    icon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Check;
                    break;
                case "cancel":
                    icon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Cancel;
                    break;
                case "alertcircle":
                    icon.Kind = MaterialDesignThemes.Wpf.PackIconKind.AlertCircle;
                    break;
                default:
                    icon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Check;
                    break;
            }

            var textBlock = new TextBlock { Text = text };

            stackPanel.Children.Add(icon);
            stackPanel.Children.Add(textBlock);

            return stackPanel;
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult?.Invoke(this, true);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult?.Invoke(this, false);
        }

        /// <summary>
        /// إظهار نافذة التأكيد في النافذة الرئيسية
        /// </summary>
        public static bool ShowConfirmation(Window parentWindow, string title, string message, string confirmText = "تأكيد")
        {
            var dialog = new ConfirmationDialog();
            dialog.SetupGeneral(title, message, confirmText);

            return ShowDialog(parentWindow, dialog);
        }

        /// <summary>
        /// إظهار نافذة تأكيد حذف المدفوعة
        /// </summary>
        public static bool ShowPaymentDeletionConfirmation(Window parentWindow, Payment payment)
        {
            var dialog = new ConfirmationDialog();
            dialog.SetupForPaymentDeletion(payment);

            return ShowDialog(parentWindow, dialog);
        }

        /// <summary>
        /// إظهار نافذة التأكيد كـ Overlay
        /// </summary>
        private static bool ShowDialog(Window parentWindow, ConfirmationDialog dialog)
        {
            bool result = false;

            // إضافة الحوار كـ overlay
            var parentGrid = parentWindow.Content as Grid;
            if (parentGrid == null)
            {
                // إذا لم تكن النافذة الرئيسية Grid، نستخدم MessageBox كبديل
                return MessageBox.Show("هل أنت متأكد؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
            }

            // إضافة الحوار إلى الشبكة
            Grid.SetRowSpan(dialog, parentGrid.RowDefinitions.Count > 0 ? parentGrid.RowDefinitions.Count : 1);
            Grid.SetColumnSpan(dialog, parentGrid.ColumnDefinitions.Count > 0 ? parentGrid.ColumnDefinitions.Count : 1);
            parentGrid.Children.Add(dialog);

            // معالج إغلاق الحوار
            dialog.DialogResult += (s, confirmed) =>
            {
                result = confirmed;
                parentGrid.Children.Remove(dialog);
            };

            // انتظار إغلاق الحوار (محاكاة ShowDialog)
            var frame = new System.Windows.Threading.DispatcherFrame();
            dialog.DialogResult += (s, e) => frame.Continue = false;

            System.Windows.Threading.Dispatcher.PushFrame(frame);

            return result;
        }
    }
}
