<UserControl x:Class="HR_InvoiceArchiver.Controls.InvoiceFormOverlay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             Background="Transparent"
             Visibility="Collapsed">

    <UserControl.Resources>
        <!-- Fade In Animation -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>
        
        <!-- Fade Out Animation -->
        <Storyboard x:Key="FadeOutAnimation" Completed="FadeOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
    </UserControl.Resources>

    <!-- Overlay Container with Semi-transparent Background -->
    <Grid Background="#80000000">
        <!-- Centered Container for Form -->
        <Grid x:Name="OverlayContainer"
              HorizontalAlignment="Center"
              VerticalAlignment="Center"
              Background="Transparent">
            <!-- Invoice forms will be added dynamically -->
        </Grid>
    </Grid>
</UserControl>
