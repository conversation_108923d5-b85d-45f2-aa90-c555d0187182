using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    public interface IInvoiceService
    {
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();
        Task<IEnumerable<Invoice>> GetAllInvoicesBasicAsync(); // طريقة محسنة للأداء
        Task<Invoice?> GetInvoiceByIdAsync(int id);
        Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<Invoice>> GetInvoicesBySupplierAsync(int supplierId);
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int id);
        Task<bool> InvoiceExistsAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null);
        Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm);
        Task<IEnumerable<Invoice>> GetFilteredInvoicesAsync(InvoiceFilter filter);
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();
        Task<bool> ValidateInvoiceAsync(Invoice invoice);
        Task<InvoiceStatistics> GetInvoiceStatisticsAsync();
        Task<InvoiceStatistics> GetInvoiceStatisticsOptimizedAsync(); // إحصائيات محسنة
        Task<IEnumerable<MonthlyStatistics>> GetMonthlyStatisticsAsync(int months = 12);
        Task<List<Invoice>> GetRecentInvoicesAsync(int count = 10);
    }

    public class InvoiceStatistics
    {
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int UnpaidInvoices { get; set; }
        public int PartiallyPaidInvoices { get; set; }
        public int OverdueInvoices { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public decimal PendingAmount => RemainingAmount; // Alias for RemainingAmount
        public decimal AverageInvoiceAmount { get; set; }
        public IEnumerable<SupplierInvoiceCount> TopSuppliersByInvoiceCount { get; set; } = new List<SupplierInvoiceCount>();
        public IEnumerable<SupplierInvoiceAmount> TopSuppliersByAmount { get; set; } = new List<SupplierInvoiceAmount>();
    }

    public class SupplierInvoiceCount
    {
        public string SupplierName { get; set; } = string.Empty;
        public int InvoiceCount { get; set; }
    }

    public class SupplierInvoiceAmount
    {
        public string SupplierName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
    }

    public class MonthlyStatistics
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
    }

    public class InvoiceService : IInvoiceService
    {
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly ISupplierRepository _supplierRepository;
        private readonly IPaymentRepository _paymentRepository;

        public InvoiceService(
            IInvoiceRepository invoiceRepository,
            ISupplierRepository supplierRepository,
            IPaymentRepository paymentRepository)
        {
            _invoiceRepository = invoiceRepository;
            _supplierRepository = supplierRepository;
            _paymentRepository = paymentRepository;
        }

        public async Task<IEnumerable<Invoice>> GetAllInvoicesAsync()
        {
            return await _invoiceRepository.GetAllAsync();
        }

        /// <summary>
        /// الحصول على جميع الفواتير بطريقة محسنة للأداء (بدون تحميل جميع العلاقات)
        /// </summary>
        public async Task<IEnumerable<Invoice>> GetAllInvoicesBasicAsync()
        {
            return await _invoiceRepository.GetAllBasicAsync();
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            return await _invoiceRepository.GetByIdAsync(id);
        }

        public async Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber)
        {
            return await _invoiceRepository.GetByInvoiceNumberAsync(invoiceNumber);
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesBySupplierAsync(int supplierId)
        {
            return await _invoiceRepository.GetBySupplierIdAsync(supplierId);
        }

        public async Task<Invoice> CreateInvoiceAsync(Invoice invoice)
        {
            if (!await ValidateInvoiceAsync(invoice))
                throw new ArgumentException("بيانات الفاتورة غير صحيحة");

            if (await _invoiceRepository.ExistsByInvoiceNumberAsync(invoice.InvoiceNumber))
                throw new ArgumentException("يوجد فاتورة بنفس الرقم مسبقاً");

            if (!await _supplierRepository.ExistsAsync(invoice.SupplierId))
                throw new ArgumentException("المورد غير موجود");

            return await _invoiceRepository.AddAsync(invoice);
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {
            if (!await ValidateInvoiceAsync(invoice))
                throw new ArgumentException("بيانات الفاتورة غير صحيحة");

            if (!await _invoiceRepository.ExistsAsync(invoice.Id))
                throw new ArgumentException("الفاتورة غير موجودة");

            if (await _invoiceRepository.ExistsByInvoiceNumberAsync(invoice.InvoiceNumber, invoice.Id))
                throw new ArgumentException("يوجد فاتورة أخرى بنفس الرقم");

            if (!await _supplierRepository.ExistsAsync(invoice.SupplierId))
                throw new ArgumentException("المورد غير موجود");

            return await _invoiceRepository.UpdateAsync(invoice);
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            var invoice = await _invoiceRepository.GetByIdAsync(id);
            if (invoice == null)
                return false;

            // Check if invoice has payments
            if (invoice.Payments.Any())
                throw new InvalidOperationException("لا يمكن حذف الفاتورة لأنها تحتوي على مدفوعات");

            return await _invoiceRepository.DeleteAsync(id);
        }

        public async Task<bool> InvoiceExistsAsync(int id)
        {
            return await _invoiceRepository.ExistsAsync(id);
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null)
        {
            return await _invoiceRepository.ExistsByInvoiceNumberAsync(invoiceNumber, excludeId);
        }

        public async Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm)
        {
            return await _invoiceRepository.SearchAsync(searchTerm);
        }

        public async Task<IEnumerable<Invoice>> GetFilteredInvoicesAsync(InvoiceFilter filter)
        {
            return await _invoiceRepository.GetFilteredAsync(filter);
        }

        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            return await _invoiceRepository.GetOverdueInvoicesAsync();
        }

        public async Task<bool> ValidateInvoiceAsync(Invoice invoice)
        {
            if (string.IsNullOrWhiteSpace(invoice.InvoiceNumber))
                return false;

            if (invoice.Amount <= 0)
                return false;

            if (invoice.PaidAmount < 0 || invoice.PaidAmount > invoice.Amount)
                return false;

            if (invoice.SupplierId <= 0)
                return false;

            if (invoice.InvoiceDate > DateTime.Now.AddDays(1)) // Allow future dates within 1 day
                return false;

            return await Task.FromResult(true);
        }

        public async Task<InvoiceStatistics> GetInvoiceStatisticsAsync()
        {
            var allInvoices = await _invoiceRepository.GetAllAsync();
            var invoicesList = allInvoices.ToList();

            var totalInvoices = invoicesList.Count;
            var paidInvoices = invoicesList.Count(i => i.Status == InvoiceStatus.Paid);
            var unpaidInvoices = invoicesList.Count(i => i.Status == InvoiceStatus.Unpaid);
            var partiallyPaidInvoices = invoicesList.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
            var overdueInvoices = invoicesList.Count(i => i.IsOverdue);

            var totalAmount = invoicesList.Sum(i => i.Amount);
            var paidAmount = invoicesList.Sum(i => i.PaidAmount);
            var remainingAmount = totalAmount - paidAmount;
            var averageAmount = totalInvoices > 0 ? totalAmount / totalInvoices : 0;

            var topSuppliersByCount = invoicesList
                .GroupBy(i => i.Supplier.Name)
                .Select(g => new SupplierInvoiceCount
                {
                    SupplierName = g.Key,
                    InvoiceCount = g.Count()
                })
                .OrderByDescending(s => s.InvoiceCount)
                .Take(5);

            var topSuppliersByAmount = invoicesList
                .GroupBy(i => i.Supplier.Name)
                .Select(g => new SupplierInvoiceAmount
                {
                    SupplierName = g.Key,
                    TotalAmount = g.Sum(i => i.Amount)
                })
                .OrderByDescending(s => s.TotalAmount)
                .Take(5);

            return new InvoiceStatistics
            {
                TotalInvoices = totalInvoices,
                PaidInvoices = paidInvoices,
                UnpaidInvoices = unpaidInvoices,
                PartiallyPaidInvoices = partiallyPaidInvoices,
                OverdueInvoices = overdueInvoices,
                TotalAmount = totalAmount,
                PaidAmount = paidAmount,
                RemainingAmount = remainingAmount,
                AverageInvoiceAmount = averageAmount,
                TopSuppliersByInvoiceCount = topSuppliersByCount,
                TopSuppliersByAmount = topSuppliersByAmount
            };
        }

        /// <summary>
        /// الحصول على الإحصائيات بطريقة محسنة (استعلام واحد)
        /// </summary>
        public async Task<InvoiceStatistics> GetInvoiceStatisticsOptimizedAsync()
        {
            // استخدام الطريقة المحسنة من Repository
            var (totalCount, totalAmount, paidAmount) = await _invoiceRepository.GetStatisticsAsync();

            // الحصول على البيانات الأساسية فقط للحسابات المتقدمة
            var basicInvoices = await _invoiceRepository.GetAllBasicAsync();
            var invoicesList = basicInvoices.ToList();

            var paidInvoices = invoicesList.Count(i => i.Status == InvoiceStatus.Paid);
            var unpaidInvoices = invoicesList.Count(i => i.Status == InvoiceStatus.Unpaid);
            var partiallyPaidInvoices = invoicesList.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
            var overdueInvoices = invoicesList.Count(i => i.IsOverdue);
            var averageAmount = totalCount > 0 ? totalAmount / totalCount : 0;

            // حساب أفضل الموردين بطريقة محسنة
            var topSuppliersByCount = invoicesList
                .Where(i => i.Supplier != null)
                .GroupBy(i => i.Supplier.Name)
                .Select(g => new SupplierInvoiceCount
                {
                    SupplierName = g.Key,
                    InvoiceCount = g.Count()
                })
                .OrderByDescending(s => s.InvoiceCount)
                .Take(5);

            var topSuppliersByAmount = invoicesList
                .Where(i => i.Supplier != null)
                .GroupBy(i => i.Supplier.Name)
                .Select(g => new SupplierInvoiceAmount
                {
                    SupplierName = g.Key,
                    TotalAmount = g.Sum(i => i.Amount)
                })
                .OrderByDescending(s => s.TotalAmount)
                .Take(5);

            return new InvoiceStatistics
            {
                TotalInvoices = totalCount,
                PaidInvoices = paidInvoices,
                UnpaidInvoices = unpaidInvoices,
                PartiallyPaidInvoices = partiallyPaidInvoices,
                OverdueInvoices = overdueInvoices,
                TotalAmount = totalAmount,
                PaidAmount = paidAmount,
                RemainingAmount = totalAmount - paidAmount,
                AverageInvoiceAmount = averageAmount,
                TopSuppliersByInvoiceCount = topSuppliersByCount,
                TopSuppliersByAmount = topSuppliersByAmount
            };
        }

        public async Task<IEnumerable<MonthlyStatistics>> GetMonthlyStatisticsAsync(int months = 12)
        {
            var allInvoices = await _invoiceRepository.GetAllAsync();
            var startDate = DateTime.Now.AddMonths(-months);

            var monthlyStats = allInvoices
                .Where(i => i.InvoiceDate >= startDate)
                .GroupBy(i => new { i.InvoiceDate.Year, i.InvoiceDate.Month })
                .Select(g => new MonthlyStatistics
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = GetArabicMonthName(g.Key.Month),
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(i => i.Amount),
                    PaidAmount = g.Sum(i => i.PaidAmount),
                    RemainingAmount = g.Sum(i => i.RemainingAmount)
                })
                .OrderBy(s => s.Year)
                .ThenBy(s => s.Month);

            return monthlyStats;
        }

        public async Task<List<Invoice>> GetRecentInvoicesAsync(int count = 10)
        {
            var allInvoices = await _invoiceRepository.GetAllAsync();
            return allInvoices
                .OrderByDescending(i => i.CreatedDate)
                .Take(count)
                .ToList();
        }

        private string GetArabicMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
    }
}
