using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Controls
{
    public partial class OfferFormOverlay : UserControl
    {
        public event EventHandler<OfferFormEventArgs>? FormClosed;

        public OfferFormOverlay()
        {
            InitializeComponent();
        }

        public void ShowAddOfferForm()
        {
            // Remove existing form if any
            OverlayContainer.Children.Clear();

            // Create new form
            var newForm = new OfferFormWindow();
            newForm.FormClosed += OfferForm_FormClosed;
            OverlayContainer.Children.Add(newForm);

            ShowOverlay();
        }

        public void ShowEditOfferForm(Offer offer)
        {
            // Remove existing form if any
            OverlayContainer.Children.Clear();

            // Create new form with offer data
            var newForm = new OfferFormWindow(offer);
            newForm.FormClosed += OfferForm_FormClosed;
            OverlayContainer.Children.Add(newForm);

            ShowOverlay();
        }

        private void ShowOverlay()
        {
            Visibility = Visibility.Visible;
            var storyboard = (Storyboard)Resources["FadeInAnimation"];
            storyboard.Begin(this);
        }

        private void HideOverlay()
        {
            var storyboard = (Storyboard)Resources["FadeOutAnimation"];
            storyboard.Begin(this);
        }

        private void OfferForm_FormClosed(object? sender, OfferFormEventArgs e)
        {
            FormClosed?.Invoke(this, e);
            HideOverlay();
        }

        private void FadeOutAnimation_Completed(object? sender, EventArgs e)
        {
            Visibility = Visibility.Collapsed;
            OverlayContainer.Children.Clear();
        }
    }

    public class OfferFormEventArgs : EventArgs
    {
        public bool Success { get; set; }
        public Offer? Offer { get; set; }
        public string? Action { get; set; } // "Add", "Edit", "Cancel"

        public OfferFormEventArgs(bool success, Offer? offer = null, string? action = null)
        {
            Success = success;
            Offer = offer;
            Action = action;
        }
    }
}
