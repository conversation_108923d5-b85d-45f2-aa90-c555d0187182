﻿#pragma checksum "..\..\..\..\Windows\AddEditPaymentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DE74A6BEACA33E7AF26D5CCADD40FA35D2822446"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Windows {
    
    
    /// <summary>
    /// AddEditPaymentWindow
    /// </summary>
    public partial class AddEditPaymentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 53 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InvoiceComboBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InvoiceInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReceiptNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PaymentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiscountLabel;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RefundValueLabel;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border RefundValueDisplay;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RefundValueText;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachmentPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewAttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/windows/addeditpaymentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.InvoiceComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 58 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.InvoiceComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InvoiceComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.InvoiceInfoPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.InvoiceInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ReceiptNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.PaymentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 92 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 105 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.AmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DiscountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.DiscountAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 114 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.DiscountAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.DiscountAmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RefundValueLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.RefundValueDisplay = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            this.RefundValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.AttachmentPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            
            #line 175 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PreviewAttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.PreviewAttachmentButton.Click += new System.Windows.RoutedEventHandler(this.PreviewAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 195 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 209 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\Windows\AddEditPaymentWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

