﻿#pragma checksum "..\..\..\..\Pages\OffersPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FE339D047926761F93152433385F60929772DC89"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Controls;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// OffersPage
    /// </summary>
    public partial class OffersPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 102 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalOffersText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BestOffersText;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddOfferButton;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CompareOffersButton;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportOffersButton;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ViewToggleButton;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearSearchButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterScientificNameComboBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterOfficeComboBox;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterRepComboBox;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SortComboBox;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer CardsScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl OffersCardsControl;
        
        #line default
        #line hidden
        
        
        #line 421 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card TableViewCard;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TableResultsText;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid OffersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 648 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 672 "..\..\..\..\Pages\OffersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HR_InvoiceArchiver.Controls.OfferFormOverlay OfferFormOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/offerspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\OffersPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalOffersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.BestOffersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.AddOfferButton = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\Pages\OffersPage.xaml"
            this.AddOfferButton.Click += new System.Windows.RoutedEventHandler(this.AddOfferButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CompareOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\Pages\OffersPage.xaml"
            this.CompareOffersButton.Click += new System.Windows.RoutedEventHandler(this.CompareOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ExportOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\Pages\OffersPage.xaml"
            this.ExportOffersButton.Click += new System.Windows.RoutedEventHandler(this.ExportOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ViewToggleButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 165 "..\..\..\..\Pages\OffersPage.xaml"
            this.ViewToggleButton.Checked += new System.Windows.RoutedEventHandler(this.ViewToggleButton_Checked);
            
            #line default
            #line hidden
            
            #line 166 "..\..\..\..\Pages\OffersPage.xaml"
            this.ViewToggleButton.Unchecked += new System.Windows.RoutedEventHandler(this.ViewToggleButton_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 201 "..\..\..\..\Pages\OffersPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ClearSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\Pages\OffersPage.xaml"
            this.ClearSearchButton.Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.FilterScientificNameComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 218 "..\..\..\..\Pages\OffersPage.xaml"
            this.FilterScientificNameComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.FilterOfficeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 224 "..\..\..\..\Pages\OffersPage.xaml"
            this.FilterOfficeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.FilterRepComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 230 "..\..\..\..\Pages\OffersPage.xaml"
            this.FilterRepComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SortComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 236 "..\..\..\..\Pages\OffersPage.xaml"
            this.SortComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SortComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ResetFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 249 "..\..\..\..\Pages\OffersPage.xaml"
            this.ResetFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ResetFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.CardsScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 15:
            this.OffersCardsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 19:
            this.TableViewCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 20:
            this.TableResultsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            
            #line 447 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 453 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintTableButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.OffersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 473 "..\..\..\..\Pages\OffersPage.xaml"
            this.OffersDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.OffersDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 27:
            this.EmptyStatePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            
            #line 662 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddOfferButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.OfferFormOverlay = ((HR_InvoiceArchiver.Controls.OfferFormOverlay)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 271 "..\..\..\..\Pages\OffersPage.xaml"
            ((MaterialDesignThemes.Wpf.Card)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.OfferCard_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 398 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditOfferButton_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 406 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteOfferButton_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 616 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditOfferButton_Click);
            
            #line default
            #line hidden
            break;
            case 25:
            
            #line 624 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteOfferButton_Click);
            
            #line default
            #line hidden
            break;
            case 26:
            
            #line 633 "..\..\..\..\Pages\OffersPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewAttachmentButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

