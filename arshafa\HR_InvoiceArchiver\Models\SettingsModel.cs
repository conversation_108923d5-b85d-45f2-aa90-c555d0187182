using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;

namespace HR_InvoiceArchiver.Models
{
    /// <summary>
    /// نموذج إعدادات التطبيق
    /// </summary>
    public class SettingsModel : INotifyPropertyChanged
    {
        #region إعدادات عامة
        private string _applicationName = "نظام أرشفة فواتير الموارد البشرية";
        private string _companyName = "";
        private string _companyAddress = "";
        private string _companyPhone = "";
        private string _companyEmail = "";
        private string _applicationLanguage = "ar-SA";
        private string _applicationTheme = "Light";
        private bool _enableNotifications = true;
        private bool _enableSounds = true;

        [Display(Name = "اسم التطبيق")]
        public string ApplicationName
        {
            get => _applicationName;
            set => SetProperty(ref _applicationName, value);
        }

        [Display(Name = "اسم الشركة")]
        public string CompanyName
        {
            get => _companyName;
            set => SetProperty(ref _companyName, value);
        }

        [Display(Name = "عنوان الشركة")]
        public string CompanyAddress
        {
            get => _companyAddress;
            set => SetProperty(ref _companyAddress, value);
        }

        [Display(Name = "هاتف الشركة")]
        public string CompanyPhone
        {
            get => _companyPhone;
            set => SetProperty(ref _companyPhone, value);
        }

        [Display(Name = "بريد الشركة الإلكتروني")]
        public string CompanyEmail
        {
            get => _companyEmail;
            set => SetProperty(ref _companyEmail, value);
        }

        [Display(Name = "لغة التطبيق")]
        public string ApplicationLanguage
        {
            get => _applicationLanguage;
            set => SetProperty(ref _applicationLanguage, value);
        }

        [Display(Name = "سمة التطبيق")]
        public string ApplicationTheme
        {
            get => _applicationTheme;
            set => SetProperty(ref _applicationTheme, value);
        }

        [Display(Name = "تفعيل الإشعارات")]
        public bool EnableNotifications
        {
            get => _enableNotifications;
            set => SetProperty(ref _enableNotifications, value);
        }

        [Display(Name = "تفعيل الأصوات")]
        public bool EnableSounds
        {
            get => _enableSounds;
            set => SetProperty(ref _enableSounds, value);
        }
        #endregion

        #region إعدادات قاعدة البيانات
        private string _databasePath = "";
        private bool _enableDatabaseBackup = true;
        private int _backupIntervalHours = 24;
        private int _maxBackupFiles = 10;
        private bool _enableDatabaseEncryption = false;
        private bool _enableDatabaseCompression = true;

        [Display(Name = "مسار قاعدة البيانات")]
        public string DatabasePath
        {
            get => _databasePath;
            set => SetProperty(ref _databasePath, value);
        }

        [Display(Name = "تفعيل النسخ الاحتياطي لقاعدة البيانات")]
        public bool EnableDatabaseBackup
        {
            get => _enableDatabaseBackup;
            set => SetProperty(ref _enableDatabaseBackup, value);
        }

        [Display(Name = "فترة النسخ الاحتياطي (ساعات)")]
        [Range(1, 168, ErrorMessage = "يجب أن تكون الفترة بين 1 و 168 ساعة")]
        public int BackupIntervalHours
        {
            get => _backupIntervalHours;
            set => SetProperty(ref _backupIntervalHours, value);
        }

        [Display(Name = "الحد الأقصى لملفات النسخ الاحتياطي")]
        [Range(1, 100, ErrorMessage = "يجب أن يكون العدد بين 1 و 100")]
        public int MaxBackupFiles
        {
            get => _maxBackupFiles;
            set => SetProperty(ref _maxBackupFiles, value);
        }

        [Display(Name = "تفعيل تشفير قاعدة البيانات")]
        public bool EnableDatabaseEncryption
        {
            get => _enableDatabaseEncryption;
            set => SetProperty(ref _enableDatabaseEncryption, value);
        }

        [Display(Name = "تفعيل ضغط قاعدة البيانات")]
        public bool EnableDatabaseCompression
        {
            get => _enableDatabaseCompression;
            set => SetProperty(ref _enableDatabaseCompression, value);
        }
        #endregion

        #region إعدادات التخزين السحابي
        private bool _enableCloudSync = false;
        private string _cloudProvider = "GoogleDrive";
        private string _cloudCredentialsPath = "";
        private bool _autoSyncEnabled = true;
        private int _syncIntervalMinutes = 30;
        private bool _syncOnStartup = true;
        private bool _syncOnShutdown = true;
        private long _maxFileSizeForSync = 50 * 1024 * 1024; // 50 MB

        [Display(Name = "تفعيل المزامنة السحابية")]
        public bool EnableCloudSync
        {
            get => _enableCloudSync;
            set => SetProperty(ref _enableCloudSync, value);
        }

        [Display(Name = "مزود الخدمة السحابية")]
        public string CloudProvider
        {
            get => _cloudProvider;
            set => SetProperty(ref _cloudProvider, value);
        }

        [Display(Name = "مسار ملف بيانات الاعتماد")]
        public string CloudCredentialsPath
        {
            get => _cloudCredentialsPath;
            set => SetProperty(ref _cloudCredentialsPath, value);
        }

        [Display(Name = "تفعيل المزامنة التلقائية")]
        public bool AutoSyncEnabled
        {
            get => _autoSyncEnabled;
            set => SetProperty(ref _autoSyncEnabled, value);
        }

        [Display(Name = "فترة المزامنة (دقائق)")]
        [Range(5, 1440, ErrorMessage = "يجب أن تكون الفترة بين 5 دقائق و 24 ساعة")]
        public int SyncIntervalMinutes
        {
            get => _syncIntervalMinutes;
            set => SetProperty(ref _syncIntervalMinutes, value);
        }

        [Display(Name = "مزامنة عند بدء التشغيل")]
        public bool SyncOnStartup
        {
            get => _syncOnStartup;
            set => SetProperty(ref _syncOnStartup, value);
        }

        [Display(Name = "مزامنة عند الإغلاق")]
        public bool SyncOnShutdown
        {
            get => _syncOnShutdown;
            set => SetProperty(ref _syncOnShutdown, value);
        }

        [Display(Name = "الحد الأقصى لحجم الملف للمزامنة (بايت)")]
        [Range(1024, long.MaxValue, ErrorMessage = "يجب أن يكون الحجم أكبر من 1 كيلوبايت")]
        public long MaxFileSizeForSync
        {
            get => _maxFileSizeForSync;
            set => SetProperty(ref _maxFileSizeForSync, value);
        }
        #endregion

        #region إعدادات الأمان
        private bool _enableAuditLog = true;
        private bool _enableDataEncryption = false;
        private int _sessionTimeoutMinutes = 60;
        private bool _requirePasswordOnStartup = false;
        private int _maxLoginAttempts = 3;
        private int _lockoutDurationMinutes = 15;

        [Display(Name = "تفعيل سجل المراجعة")]
        public bool EnableAuditLog
        {
            get => _enableAuditLog;
            set => SetProperty(ref _enableAuditLog, value);
        }

        [Display(Name = "تفعيل تشفير البيانات")]
        public bool EnableDataEncryption
        {
            get => _enableDataEncryption;
            set => SetProperty(ref _enableDataEncryption, value);
        }

        [Display(Name = "مهلة انتهاء الجلسة (دقائق)")]
        [Range(5, 480, ErrorMessage = "يجب أن تكون المهلة بين 5 دقائق و 8 ساعات")]
        public int SessionTimeoutMinutes
        {
            get => _sessionTimeoutMinutes;
            set => SetProperty(ref _sessionTimeoutMinutes, value);
        }

        [Display(Name = "طلب كلمة مرور عند بدء التشغيل")]
        public bool RequirePasswordOnStartup
        {
            get => _requirePasswordOnStartup;
            set => SetProperty(ref _requirePasswordOnStartup, value);
        }

        [Display(Name = "الحد الأقصى لمحاولات تسجيل الدخول")]
        [Range(1, 10, ErrorMessage = "يجب أن يكون العدد بين 1 و 10")]
        public int MaxLoginAttempts
        {
            get => _maxLoginAttempts;
            set => SetProperty(ref _maxLoginAttempts, value);
        }

        [Display(Name = "مدة القفل (دقائق)")]
        [Range(1, 1440, ErrorMessage = "يجب أن تكون المدة بين دقيقة واحدة و 24 ساعة")]
        public int LockoutDurationMinutes
        {
            get => _lockoutDurationMinutes;
            set => SetProperty(ref _lockoutDurationMinutes, value);
        }
        #endregion

        #region إعدادات الأداء
        private bool _enablePerformanceMonitoring = true;
        private int _maxLogEntries = 10000;
        private int _logRetentionDays = 30;
        private bool _enableCaching = true;
        private int _cacheExpirationMinutes = 60;
        private bool _enableLazyLoading = true;

        [Display(Name = "تفعيل مراقبة الأداء")]
        public bool EnablePerformanceMonitoring
        {
            get => _enablePerformanceMonitoring;
            set => SetProperty(ref _enablePerformanceMonitoring, value);
        }

        [Display(Name = "الحد الأقصى لإدخالات السجل")]
        [Range(1000, 100000, ErrorMessage = "يجب أن يكون العدد بين 1000 و 100000")]
        public int MaxLogEntries
        {
            get => _maxLogEntries;
            set => SetProperty(ref _maxLogEntries, value);
        }

        [Display(Name = "مدة الاحتفاظ بالسجلات (أيام)")]
        [Range(1, 365, ErrorMessage = "يجب أن تكون المدة بين يوم واحد و 365 يوم")]
        public int LogRetentionDays
        {
            get => _logRetentionDays;
            set => SetProperty(ref _logRetentionDays, value);
        }

        [Display(Name = "تفعيل التخزين المؤقت")]
        public bool EnableCaching
        {
            get => _enableCaching;
            set => SetProperty(ref _enableCaching, value);
        }

        [Display(Name = "مدة انتهاء صلاحية التخزين المؤقت (دقائق)")]
        [Range(1, 1440, ErrorMessage = "يجب أن تكون المدة بين دقيقة واحدة و 24 ساعة")]
        public int CacheExpirationMinutes
        {
            get => _cacheExpirationMinutes;
            set => SetProperty(ref _cacheExpirationMinutes, value);
        }

        [Display(Name = "تفعيل التحميل الكسول")]
        public bool EnableLazyLoading
        {
            get => _enableLazyLoading;
            set => SetProperty(ref _enableLazyLoading, value);
        }
        #endregion

        #region معلومات النظام
        public string ApplicationVersion { get; set; } = "1.0.0";
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public string DatabaseVersion { get; set; } = "1.0";
        public long DatabaseSize { get; set; } = 0;
        public int TotalInvoices { get; set; } = 0;
        public int TotalPayments { get; set; } = 0;
        public int TotalSuppliers { get; set; } = 0;
        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        public bool IsValid()
        {
            // التحقق من البريد الإلكتروني إذا لم يكن فارغاً
            bool emailValid = string.IsNullOrEmpty(CompanyEmail) || IsValidEmail(CompanyEmail);

            return !string.IsNullOrWhiteSpace(ApplicationName) &&
                   BackupIntervalHours > 0 &&
                   MaxBackupFiles > 0 &&
                   SyncIntervalMinutes >= 5 &&
                   SessionTimeoutMinutes >= 5 &&
                   MaxLoginAttempts > 0 &&
                   LockoutDurationMinutes > 0 &&
                   MaxLogEntries >= 1000 &&
                   LogRetentionDays > 0 &&
                   CacheExpirationMinutes > 0 &&
                   emailValid;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        public void ResetToDefaults()
        {
            ApplicationName = "نظام أرشفة فواتير الموارد البشرية";
            CompanyName = "";
            CompanyAddress = "";
            CompanyPhone = "";
            CompanyEmail = "";
            ApplicationLanguage = "ar-SA";
            ApplicationTheme = "Light";
            EnableNotifications = true;
            EnableSounds = true;

            EnableDatabaseBackup = true;
            BackupIntervalHours = 24;
            MaxBackupFiles = 10;
            EnableDatabaseEncryption = false;
            EnableDatabaseCompression = true;

            EnableCloudSync = false;
            CloudProvider = "GoogleDrive";
            AutoSyncEnabled = true;
            SyncIntervalMinutes = 30;
            SyncOnStartup = true;
            SyncOnShutdown = true;
            MaxFileSizeForSync = 50 * 1024 * 1024;

            EnableAuditLog = true;
            EnableDataEncryption = false;
            SessionTimeoutMinutes = 60;
            RequirePasswordOnStartup = false;
            MaxLoginAttempts = 3;
            LockoutDurationMinutes = 15;

            EnablePerformanceMonitoring = true;
            MaxLogEntries = 10000;
            LogRetentionDays = 30;
            EnableCaching = true;
            CacheExpirationMinutes = 60;
            EnableLazyLoading = true;

            LastUpdated = DateTime.Now;
        }
        #endregion
    }
}
