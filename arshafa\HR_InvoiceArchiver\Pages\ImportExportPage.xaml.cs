using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class ImportExportPage : UserControl, INavigationAware
    {
        private readonly IImportExportService _importExportService;
        private readonly IToastService _toastService;
        private readonly ILoggingService _loggingService;
        private bool _isProcessing = false;

        public ImportExportPage()
        {
            InitializeComponent();
            
            // الحصول على الخدمات من DI Container
            var serviceProvider = App.ServiceProvider;
            _importExportService = serviceProvider.GetRequiredService<IImportExportService>();
            _toastService = serviceProvider.GetRequiredService<IToastService>();
            _loggingService = serviceProvider.GetRequiredService<ILoggingService>();

            InitializeDefaults();
        }

        public async void OnNavigatedTo(object parameter)
        {
            await _loggingService.LogInformationAsync("تم فتح صفحة التصدير والاستيراد");
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private void InitializeDefaults()
        {
            // تعيين القيم الافتراضية
            ExportDataTypeComboBox.SelectedIndex = 0; // الفواتير
            ExportFormatComboBox.SelectedIndex = 0;   // Excel
            ImportFormatComboBox.SelectedIndex = 0;   // Excel
            ImportModeComboBox.SelectedIndex = 2;     // Upsert
        }

        private void SetProcessingState(bool isProcessing, string message = "جاري المعالجة...")
        {
            _isProcessing = isProcessing;
            LoadingGrid.Visibility = isProcessing ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
            
            // تعطيل/تفعيل الأزرار
            var buttons = new[] 
            { 
                ExportButton, ImportButton, CreateTemplateButton, PreviewButton,
                ValidateFileButton, HistoryButton, CleanupButton
            };

            foreach (var button in buttons)
            {
                button.IsEnabled = !isProcessing;
            }
        }

        #region Export Events

        private void BrowseExportPathButton_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "اختيار مسار التصدير",
                Filter = GetExportFileFilter(),
                DefaultExt = GetDefaultExtension()
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                ExportFilePathTextBox.Text = saveFileDialog.FileName;
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing) return;

            try
            {
                SetProcessingState(true, "جاري التصدير...");

                var options = CreateExportOptions();
                ExportResult? result = null;

                var dataType = GetSelectedDataType();
                switch (dataType)
                {
                    case DataType.Invoices:
                        result = await _importExportService.ExportInvoicesAsync(options);
                        break;
                    case DataType.Suppliers:
                        result = await _importExportService.ExportSuppliersAsync(options);
                        break;
                    case DataType.Payments:
                        result = await _importExportService.ExportPaymentsAsync(options);
                        break;
                    case DataType.All:
                        result = await _importExportService.ExportAllDataAsync(options);
                        break;
                }

                if (result?.Success == true)
                {
                    _toastService.ShowSuccess("تم التصدير", 
                        $"تم تصدير {result.RecordsExported} سجل بنجاح\nالملف: {result.FilePath}");
                    
                    // عرض خيار فتح المجلد
                    var openFolder = MessageBox.Show(
                        "تم التصدير بنجاح. هل تريد فتح مجلد الملف؟",
                        "تم التصدير",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (openFolder == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{result.FilePath}\"");
                    }
                }
                else
                {
                    _toastService.ShowError("فشل التصدير", result?.ErrorMessage ?? "حدث خطأ غير معروف");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء التصدير");
                await _loggingService.LogErrorAsync("فشل في التصدير", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        private async void CreateTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing) return;

            try
            {
                SetProcessingState(true, "جاري إنشاء القالب...");

                var dataType = GetSelectedDataType();
                var format = GetSelectedExportFormat();

                var templatePath = await _importExportService.CreateImportTemplateAsync(dataType, format);

                _toastService.ShowSuccess("تم إنشاء القالب", $"تم إنشاء القالب في:\n{templatePath}");

                // عرض خيار فتح الملف
                var openFile = MessageBox.Show(
                    "تم إنشاء القالب بنجاح. هل تريد فتح الملف؟",
                    "تم إنشاء القالب",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (openFile == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = templatePath,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء إنشاء القالب");
                await _loggingService.LogErrorAsync("فشل في إنشاء القالب", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        #endregion

        #region Import Events

        private void BrowseImportFileButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف الاستيراد",
                Filter = "جميع الملفات المدعومة|*.xlsx;*.csv;*.json;*.xml|" +
                        "ملفات Excel (*.xlsx)|*.xlsx|" +
                        "ملفات CSV (*.csv)|*.csv|" +
                        "ملفات JSON (*.json)|*.json|" +
                        "ملفات XML (*.xml)|*.xml|" +
                        "جميع الملفات (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                ImportFilePathTextBox.Text = openFileDialog.FileName;
                
                // تحديد صيغة الاستيراد تلقائياً بناءً على امتداد الملف
                var extension = Path.GetExtension(openFileDialog.FileName).ToLower();
                switch (extension)
                {
                    case ".xlsx":
                        ImportFormatComboBox.SelectedIndex = 0;
                        break;
                    case ".csv":
                        ImportFormatComboBox.SelectedIndex = 1;
                        break;
                    case ".json":
                        ImportFormatComboBox.SelectedIndex = 2;
                        break;
                    case ".xml":
                        ImportFormatComboBox.SelectedIndex = 3;
                        break;
                }
            }
        }

        private async void ValidateFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing || string.IsNullOrEmpty(ImportFilePathTextBox.Text)) return;

            try
            {
                SetProcessingState(true, "جاري التحقق من الملف...");

                var format = GetSelectedImportFormat();
                var validation = await _importExportService.ValidateImportFileAsync(ImportFilePathTextBox.Text, format);

                if (validation.IsValid)
                {
                    _toastService.ShowSuccess("الملف صحيح", 
                        $"الملف صحيح ويحتوي على {validation.TotalRows} صف\n" +
                        $"الأعمدة المكتشفة: {validation.DetectedColumns.Count}");
                }
                else
                {
                    var errors = string.Join("\n", validation.Errors);
                    _toastService.ShowError("الملف غير صحيح", $"الأخطاء المكتشفة:\n{errors}");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء التحقق من الملف");
                await _loggingService.LogErrorAsync("فشل في التحقق من الملف", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        private async void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing || string.IsNullOrEmpty(ImportFilePathTextBox.Text)) return;

            try
            {
                SetProcessingState(true, "جاري إنشاء المعاينة...");

                var format = GetSelectedImportFormat();
                var preview = await _importExportService.PreviewImportDataAsync(ImportFilePathTextBox.Text, format, 10);

                if (preview.SampleData.Any())
                {
                    PreviewDataGrid.ItemsSource = preview.SampleData;
                    PreviewCard.Visibility = Visibility.Visible;
                    
                    _toastService.ShowInfo("المعاينة جاهزة", 
                        $"تم عرض أول {preview.SampleData.Count} صف من أصل {preview.TotalRows} صف");
                }
                else
                {
                    _toastService.ShowWarning("لا توجد بيانات", "الملف لا يحتوي على بيانات للمعاينة");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء إنشاء المعاينة");
                await _loggingService.LogErrorAsync("فشل في إنشاء المعاينة", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isProcessing || string.IsNullOrEmpty(ImportFilePathTextBox.Text)) return;

            var result = MessageBox.Show(
                "هل أنت متأكد من استيراد البيانات؟\nقد يؤثر هذا على البيانات الموجودة.",
                "تأكيد الاستيراد",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes) return;

            try
            {
                SetProcessingState(true, "جاري الاستيراد...");

                var options = CreateImportOptions();
                var importResult = await _importExportService.ImportAllDataAsync(options);

                if (importResult.Success)
                {
                    var message = $"تم الاستيراد بنجاح!\n" +
                                 $"السجلات المستوردة: {importResult.RecordsImported}\n" +
                                 $"السجلات المتخطاة: {importResult.RecordsSkipped}\n" +
                                 $"السجلات مع أخطاء: {importResult.RecordsWithErrors}";

                    if (!string.IsNullOrEmpty(importResult.BackupFilePath))
                    {
                        message += $"\nالنسخة الاحتياطية: {importResult.BackupFilePath}";
                    }

                    _toastService.ShowSuccess("تم الاستيراد", message);

                    // إخفاء المعاينة
                    PreviewCard.Visibility = Visibility.Collapsed;
                }
                else
                {
                    var errorMessage = importResult.ErrorMessage ?? "حدث خطأ غير معروف";
                    if (importResult.Errors.Any())
                    {
                        errorMessage += $"\nالأخطاء: {importResult.Errors.Count}";
                    }
                    
                    _toastService.ShowError("فشل الاستيراد", errorMessage);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء الاستيراد");
                await _loggingService.LogErrorAsync("فشل في الاستيراد", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        #endregion

        #region Other Events

        private async void HistoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var history = await _importExportService.GetImportExportHistoryAsync(30);

                var historyMessage = "سجل العمليات الأخيرة:\n\n";
                foreach (var item in history.Take(10))
                {
                    historyMessage += $"{item.Date:yyyy-MM-dd HH:mm} - {item.Operation} - {item.DataType} - {(item.Success ? "نجح" : "فشل")}\n";
                }

                MessageBox.Show(historyMessage, "سجل العمليات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء عرض السجل");
                await _loggingService.LogErrorAsync("فشل في عرض سجل العمليات", ex);
            }
        }

        private async void CleanupButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد حذف ملفات التصدير القديمة (أكثر من 30 يوم)؟",
                "تنظيف الملفات",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    SetProcessingState(true, "جاري تنظيف الملفات...");

                    var deletedCount = await _importExportService.CleanupOldExportFilesAsync(30);

                    _toastService.ShowSuccess("تم التنظيف", $"تم حذف {deletedCount} ملف قديم");
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", "حدث خطأ أثناء تنظيف الملفات");
                    await _loggingService.LogErrorAsync("فشل في تنظيف الملفات", ex);
                }
                finally
                {
                    SetProcessingState(false);
                }
            }
        }

        #endregion

        #region Helper Methods

        private ExportOptions CreateExportOptions()
        {
            return new ExportOptions
            {
                Format = GetSelectedExportFormat(),
                FilePath = ExportFilePathTextBox.Text,
                StartDate = ExportStartDatePicker.SelectedDate,
                EndDate = ExportEndDatePicker.SelectedDate,
                IncludeAttachments = IncludeAttachmentsCheckBox.IsChecked ?? false,
                CompressOutput = CompressOutputCheckBox.IsChecked ?? false,
                ExportAll = true
            };
        }

        private ImportOptions CreateImportOptions()
        {
            return new ImportOptions
            {
                FilePath = ImportFilePathTextBox.Text,
                Format = GetSelectedImportFormat(),
                Mode = GetSelectedImportMode(),
                ValidateData = ValidateDataCheckBox.IsChecked ?? true,
                SkipDuplicates = SkipDuplicatesCheckBox.IsChecked ?? true,
                CreateBackup = CreateBackupCheckBox.IsChecked ?? true,
                StartRow = int.TryParse(StartRowTextBox.Text, out var startRow) ? startRow : 2
            };
        }

        private DataType GetSelectedDataType()
        {
            var selectedItem = ExportDataTypeComboBox.SelectedItem as ComboBoxItem;
            var tag = selectedItem?.Tag?.ToString();

            return tag switch
            {
                "Invoices" => DataType.Invoices,
                "Suppliers" => DataType.Suppliers,
                "Payments" => DataType.Payments,
                "All" => DataType.All,
                _ => DataType.Invoices
            };
        }

        private ExportFormat GetSelectedExportFormat()
        {
            var selectedItem = ExportFormatComboBox.SelectedItem as ComboBoxItem;
            var tag = selectedItem?.Tag?.ToString();

            return tag switch
            {
                "Excel" => ExportFormat.Excel,
                "CSV" => ExportFormat.CSV,
                "JSON" => ExportFormat.JSON,
                "XML" => ExportFormat.XML,
                _ => ExportFormat.Excel
            };
        }

        private ImportFormat GetSelectedImportFormat()
        {
            var selectedItem = ImportFormatComboBox.SelectedItem as ComboBoxItem;
            var tag = selectedItem?.Tag?.ToString();

            return tag switch
            {
                "Excel" => ImportFormat.Excel,
                "CSV" => ImportFormat.CSV,
                "JSON" => ImportFormat.JSON,
                "XML" => ImportFormat.XML,
                _ => ImportFormat.Excel
            };
        }

        private ImportMode GetSelectedImportMode()
        {
            var selectedItem = ImportModeComboBox.SelectedItem as ComboBoxItem;
            var tag = selectedItem?.Tag?.ToString();

            return tag switch
            {
                "Insert" => ImportMode.Insert,
                "Update" => ImportMode.Update,
                "Upsert" => ImportMode.Upsert,
                _ => ImportMode.Upsert
            };
        }

        private string GetExportFileFilter()
        {
            var format = GetSelectedExportFormat();
            return format switch
            {
                ExportFormat.Excel => "ملفات Excel (*.xlsx)|*.xlsx",
                ExportFormat.CSV => "ملفات CSV (*.csv)|*.csv",
                ExportFormat.JSON => "ملفات JSON (*.json)|*.json",
                ExportFormat.XML => "ملفات XML (*.xml)|*.xml",
                _ => "جميع الملفات (*.*)|*.*"
            };
        }

        private string GetDefaultExtension()
        {
            var format = GetSelectedExportFormat();
            return format switch
            {
                ExportFormat.Excel => "xlsx",
                ExportFormat.CSV => "csv",
                ExportFormat.JSON => "json",
                ExportFormat.XML => "xml",
                _ => "txt"
            };
        }

        #endregion
    }
}
