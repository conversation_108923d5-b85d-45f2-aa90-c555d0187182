using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Linq;
using System.Collections.Concurrent;
using System.Threading;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// معلومات مقياس الأداء المحسن
    /// </summary>
    public class EnhancedPerformanceMetric
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string OperationName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool IsSuccess { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
        public long MemoryUsedBytes { get; set; }
        public int ThreadId { get; set; }
        public string? UserId { get; set; }
    }

    /// <summary>
    /// إحصائيات الأداء المحسنة
    /// </summary>
    public class EnhancedPerformanceStatistics
    {
        public string OperationName { get; set; } = string.Empty;
        public int TotalExecutions { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions * 100 : 0;
        public long AverageMemoryUsage { get; set; }
        public DateTime FirstExecution { get; set; }
        public DateTime LastExecution { get; set; }
    }

    /// <summary>
    /// نطاق مراقبة الأداء المحسن
    /// </summary>
    public interface IEnhancedPerformanceScope : IDisposable
    {
        void AddProperty(string key, object value);
        void SetCategory(string category);
        void SetUserId(string userId);
        void MarkAsFailure(string errorMessage);
    }

    /// <summary>
    /// واجهة خدمة مراقبة الأداء المحسنة
    /// </summary>
    public interface IEnhancedPerformanceMonitoringService
    {
        /// <summary>
        /// بدء مراقبة عملية
        /// </summary>
        IEnhancedPerformanceScope BeginOperation(string operationName, string category = "General");

        /// <summary>
        /// تسجيل مقياس أداء مخصص
        /// </summary>
        Task RecordMetricAsync(string operationName, TimeSpan duration, bool isSuccess = true, 
            string? errorMessage = null, Dictionary<string, object>? properties = null);

        /// <summary>
        /// الحصول على المقاييس الحديثة
        /// </summary>
        Task<List<EnhancedPerformanceMetric>> GetRecentMetricsAsync(int count = 100, string? operationName = null);

        /// <summary>
        /// الحصول على إحصائيات الأداء
        /// </summary>
        Task<List<EnhancedPerformanceStatistics>> GetPerformanceStatisticsAsync(DateTime? fromDate = null, 
            DateTime? toDate = null, string? category = null);

        /// <summary>
        /// الحصول على العمليات البطيئة
        /// </summary>
        Task<List<EnhancedPerformanceMetric>> GetSlowOperationsAsync(TimeSpan threshold, int count = 50);

        /// <summary>
        /// تنظيف المقاييس القديمة
        /// </summary>
        Task CleanupOldMetricsAsync(TimeSpan maxAge);

        /// <summary>
        /// تصدير تقرير الأداء
        /// </summary>
        Task<string> ExportPerformanceReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// الحصول على استخدام الذاكرة الحالي
        /// </summary>
        long GetCurrentMemoryUsage();

        /// <summary>
        /// تفعيل أو إلغاء تفعيل المراقبة
        /// </summary>
        void SetMonitoringEnabled(bool enabled);
    }

    /// <summary>
    /// تطبيق خدمة مراقبة الأداء المحسنة
    /// </summary>
    public class EnhancedPerformanceMonitoringService : IEnhancedPerformanceMonitoringService, IDisposable
    {
        private readonly ILoggingService? _loggingService;
        private readonly ConcurrentBag<EnhancedPerformanceMetric> _metrics;
        private readonly Timer _cleanupTimer;
        private readonly TimeSpan _slowOperationThreshold;
        private readonly int _maxMetricsCount;
        private bool _isMonitoringEnabled;
        private bool _disposed = false;

        public EnhancedPerformanceMonitoringService(ILoggingService? loggingService = null)
        {
            _loggingService = loggingService;
            _metrics = new ConcurrentBag<EnhancedPerformanceMetric>();
            _slowOperationThreshold = TimeSpan.FromSeconds(1);
            _maxMetricsCount = 10000;
            _isMonitoringEnabled = true;

            // تشغيل مؤقت التنظيف كل ساعة
            _cleanupTimer = new Timer(CleanupCallback, null, TimeSpan.FromHours(1), TimeSpan.FromHours(1));
        }

        public IEnhancedPerformanceScope BeginOperation(string operationName, string category = "General")
        {
            if (!_isMonitoringEnabled)
                return new DisabledEnhancedPerformanceScope();

            return new EnhancedPerformanceScope(this, operationName, category);
        }

        public async Task RecordMetricAsync(string operationName, TimeSpan duration, bool isSuccess = true, 
            string? errorMessage = null, Dictionary<string, object>? properties = null)
        {
            if (!_isMonitoringEnabled) return;

            var metric = new EnhancedPerformanceMetric
            {
                OperationName = operationName,
                Duration = duration,
                StartTime = DateTime.Now - duration,
                EndTime = DateTime.Now,
                IsSuccess = isSuccess,
                ErrorMessage = errorMessage,
                Properties = properties ?? new Dictionary<string, object>(),
                MemoryUsedBytes = GetCurrentMemoryUsage(),
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _metrics.Add(metric);

            // تسجيل العمليات البطيئة
            if (duration > _slowOperationThreshold)
            {
                await _loggingService?.LogPerformanceAsync(operationName, duration, new
                {
                    IsSuccess = isSuccess,
                    ErrorMessage = errorMessage,
                    Properties = properties
                })!;
            }

            // تنظيف المقاييس إذا تجاوزت الحد الأقصى
            if (_metrics.Count > _maxMetricsCount)
            {
                await CleanupOldMetricsAsync(TimeSpan.FromDays(7));
            }
        }

        public async Task<List<EnhancedPerformanceMetric>> GetRecentMetricsAsync(int count = 100, string? operationName = null)
        {
            var metrics = _metrics.ToList();
            
            if (!string.IsNullOrEmpty(operationName))
            {
                metrics = metrics.Where(m => m.OperationName.Equals(operationName, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            return await Task.FromResult(metrics
                .OrderByDescending(m => m.EndTime)
                .Take(count)
                .ToList());
        }

        public async Task<List<EnhancedPerformanceStatistics>> GetPerformanceStatisticsAsync(DateTime? fromDate = null, 
            DateTime? toDate = null, string? category = null)
        {
            var metrics = _metrics.ToList();

            // تطبيق الفلاتر
            if (fromDate.HasValue)
                metrics = metrics.Where(m => m.StartTime >= fromDate.Value).ToList();

            if (toDate.HasValue)
                metrics = metrics.Where(m => m.EndTime <= toDate.Value).ToList();

            if (!string.IsNullOrEmpty(category))
                metrics = metrics.Where(m => m.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();

            // تجميع الإحصائيات
            var statistics = metrics
                .GroupBy(m => m.OperationName)
                .Select(g => new EnhancedPerformanceStatistics
                {
                    OperationName = g.Key,
                    TotalExecutions = g.Count(),
                    SuccessfulExecutions = g.Count(m => m.IsSuccess),
                    FailedExecutions = g.Count(m => !m.IsSuccess),
                    AverageDuration = TimeSpan.FromTicks((long)g.Average(m => m.Duration.Ticks)),
                    MinDuration = g.Min(m => m.Duration),
                    MaxDuration = g.Max(m => m.Duration),
                    TotalDuration = TimeSpan.FromTicks(g.Sum(m => m.Duration.Ticks)),
                    AverageMemoryUsage = (long)g.Average(m => m.MemoryUsedBytes),
                    FirstExecution = g.Min(m => m.StartTime),
                    LastExecution = g.Max(m => m.EndTime)
                })
                .OrderByDescending(s => s.TotalExecutions)
                .ToList();

            return await Task.FromResult(statistics);
        }

        public async Task<List<EnhancedPerformanceMetric>> GetSlowOperationsAsync(TimeSpan threshold, int count = 50)
        {
            var slowOperations = _metrics
                .Where(m => m.Duration > threshold)
                .OrderByDescending(m => m.Duration)
                .Take(count)
                .ToList();

            return await Task.FromResult(slowOperations);
        }

        public async Task CleanupOldMetricsAsync(TimeSpan maxAge)
        {
            var cutoffDate = DateTime.Now - maxAge;
            var metricsToKeep = _metrics.Where(m => m.EndTime > cutoffDate).ToList();
            
            // إعادة إنشاء المجموعة بالمقاييس الجديدة فقط
            _metrics.Clear();
            foreach (var metric in metricsToKeep)
            {
                _metrics.Add(metric);
            }

            await _loggingService?.LogInformationAsync($"تم تنظيف {_metrics.Count - metricsToKeep.Count} مقياس أداء قديم")!;
        }

        public async Task<string> ExportPerformanceReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var statistics = await GetPerformanceStatisticsAsync(fromDate, toDate);
            var reportPath = System.IO.Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory, 
                "Reports", 
                $"enhanced-performance-report-{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.csv");

            System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(reportPath)!);

            using var writer = new System.IO.StreamWriter(reportPath);
            await writer.WriteLineAsync("OperationName,TotalExecutions,SuccessfulExecutions,FailedExecutions,SuccessRate,AverageDurationMs,MinDurationMs,MaxDurationMs,TotalDurationMs,AverageMemoryUsageMB");
            
            foreach (var stat in statistics)
            {
                var line = $"{stat.OperationName},{stat.TotalExecutions},{stat.SuccessfulExecutions}," +
                          $"{stat.FailedExecutions},{stat.SuccessRate:F2},{stat.AverageDuration.TotalMilliseconds:F2}," +
                          $"{stat.MinDuration.TotalMilliseconds:F2},{stat.MaxDuration.TotalMilliseconds:F2}," +
                          $"{stat.TotalDuration.TotalMilliseconds:F2},{stat.AverageMemoryUsage / 1024.0 / 1024.0:F2}";
                await writer.WriteLineAsync(line);
            }

            return reportPath;
        }

        public long GetCurrentMemoryUsage()
        {
            return GC.GetTotalMemory(false);
        }

        public void SetMonitoringEnabled(bool enabled)
        {
            _isMonitoringEnabled = enabled;
        }

        private void CleanupCallback(object? state)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await CleanupOldMetricsAsync(TimeSpan.FromDays(30));
                }
                catch (Exception ex)
                {
                    await _loggingService?.LogErrorAsync("فشل في تنظيف مقاييس الأداء المحسنة", ex)!;
                }
            });
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cleanupTimer?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// نطاق مراقبة الأداء المحسن
    /// </summary>
    internal class EnhancedPerformanceScope : IEnhancedPerformanceScope
    {
        private readonly EnhancedPerformanceMonitoringService _service;
        private readonly string _operationName;
        private readonly Stopwatch _stopwatch;
        private readonly DateTime _startTime;
        private readonly long _startMemory;
        private string _category = "General";
        private string? _userId;
        private bool _isFailure = false;
        private string? _errorMessage;
        private readonly Dictionary<string, object> _properties = new();
        private bool _disposed = false;

        public EnhancedPerformanceScope(EnhancedPerformanceMonitoringService service, string operationName, string category)
        {
            _service = service;
            _operationName = operationName;
            _category = category;
            _stopwatch = Stopwatch.StartNew();
            _startTime = DateTime.Now;
            _startMemory = GC.GetTotalMemory(false);
        }

        public void AddProperty(string key, object value)
        {
            _properties[key] = value;
        }

        public void SetCategory(string category)
        {
            _category = category;
        }

        public void SetUserId(string userId)
        {
            _userId = userId;
        }

        public void MarkAsFailure(string errorMessage)
        {
            _isFailure = true;
            _errorMessage = errorMessage;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();
                
                _ = Task.Run(() => _service.RecordMetricAsync(_operationName, _stopwatch.Elapsed, 
                    !_isFailure, _errorMessage, _properties));

                _disposed = true;
            }
        }
    }

    /// <summary>
    /// نطاق أداء معطل (عندما تكون المراقبة معطلة)
    /// </summary>
    internal class DisabledEnhancedPerformanceScope : IEnhancedPerformanceScope
    {
        public void AddProperty(string key, object value) { }
        public void SetCategory(string category) { }
        public void SetUserId(string userId) { }
        public void MarkAsFailure(string errorMessage) { }
        public void Dispose() { }
    }
}
