using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Data;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// تطبيق خدمة إدارة الإعدادات
    /// </summary>
    public class SettingsService : ISettingsService
    {
        private readonly string _settingsFilePath;
        private readonly ILoggingService? _loggingService;
        private readonly DatabaseContext? _databaseContext;
        private SettingsModel? _cachedSettings;
        private readonly object _lockObject = new object();
        private readonly DateTime _applicationStartTime;

        public event EventHandler<SettingsChangedEventArgs>? SettingsChanged;

        public SettingsService(ILoggingService? loggingService = null, DatabaseContext? databaseContext = null)
        {
            _loggingService = loggingService;
            _databaseContext = databaseContext;
            _applicationStartTime = DateTime.Now;
            
            var appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "HR_InvoiceArchiver");
            
            Directory.CreateDirectory(appDataPath);
            _settingsFilePath = Path.Combine(appDataPath, "settings.json");
        }

        public async Task<SettingsModel> LoadSettingsAsync()
        {
            try
            {
                if (_cachedSettings != null)
                    return _cachedSettings;

                lock (_lockObject)
                {
                    if (_cachedSettings != null)
                        return _cachedSettings;

                    if (File.Exists(_settingsFilePath))
                    {
                        var json = File.ReadAllText(_settingsFilePath);
                        _cachedSettings = JsonSerializer.Deserialize<SettingsModel>(json) ?? new SettingsModel();
                    }
                    else
                    {
                        _cachedSettings = new SettingsModel();
                        // حفظ الإعدادات الافتراضية
                        _ = Task.Run(() => SaveSettingsAsync(_cachedSettings));
                    }
                }

                await _loggingService?.LogInformationAsync("تم تحميل الإعدادات بنجاح")!;
                return _cachedSettings;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في تحميل الإعدادات", ex)!;
                return new SettingsModel();
            }
        }

        public async Task SaveSettingsAsync(SettingsModel settings)
        {
            try
            {
                var validation = await ValidateSettingsAsync(settings);
                if (!validation.IsValid)
                {
                    throw new ArgumentException($"الإعدادات غير صحيحة: {string.Join(", ", validation.Errors)}");
                }

                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                lock (_lockObject)
                {
                    File.WriteAllText(_settingsFilePath, json);
                    _cachedSettings = settings;
                }

                settings.LastUpdated = DateTime.Now;
                
                // تطبيق الإعدادات على النظام
                await ApplySettingsAsync(settings);

                await _loggingService?.LogInformationAsync("تم حفظ الإعدادات بنجاح")!;
                
                // إثارة حدث تغيير الإعدادات
                SettingsChanged?.Invoke(this, new SettingsChangedEventArgs
                {
                    SettingKey = "AllSettings",
                    NewValue = settings,
                    ChangedAt = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في حفظ الإعدادات", ex)!;
                throw;
            }
        }

        public async Task ResetToDefaultsAsync()
        {
            try
            {
                var defaultSettings = new SettingsModel();
                defaultSettings.ResetToDefaults();
                
                await SaveSettingsAsync(defaultSettings);
                await _loggingService?.LogInformationAsync("تم إعادة تعيين الإعدادات إلى القيم الافتراضية")!;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في إعادة تعيين الإعدادات", ex)!;
                throw;
            }
        }

        public async Task<string> ExportSettingsAsync(string? filePath = null)
        {
            try
            {
                var settings = await LoadSettingsAsync();
                
                if (string.IsNullOrEmpty(filePath))
                {
                    var exportDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Exports");
                    Directory.CreateDirectory(exportDir);
                    filePath = Path.Combine(exportDir, $"settings-export-{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.json");
                }

                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.WriteAllTextAsync(filePath, json);
                await _loggingService?.LogInformationAsync($"تم تصدير الإعدادات إلى: {filePath}")!;
                
                return filePath;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في تصدير الإعدادات", ex)!;
                throw;
            }
        }

        public async Task<bool> ImportSettingsAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    await _loggingService?.LogErrorAsync($"ملف الإعدادات غير موجود: {filePath}")!;
                    return false;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var importedSettings = JsonSerializer.Deserialize<SettingsModel>(json);
                
                if (importedSettings == null)
                {
                    await _loggingService?.LogErrorAsync("فشل في تحليل ملف الإعدادات")!;
                    return false;
                }

                var validation = await ValidateSettingsAsync(importedSettings);
                if (!validation.IsValid)
                {
                    await _loggingService?.LogErrorAsync($"الإعدادات المستوردة غير صحيحة: {string.Join(", ", validation.Errors)}")!;
                    return false;
                }

                await SaveSettingsAsync(importedSettings);
                await _loggingService?.LogInformationAsync($"تم استيراد الإعدادات من: {filePath}")!;
                
                return true;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في استيراد الإعدادات", ex)!;
                return false;
            }
        }

        public async Task<(bool IsValid, string[] Errors)> ValidateSettingsAsync(SettingsModel settings)
        {
            var errors = new List<string>();

            try
            {
                // التحقق من صحة الخصائص باستخدام Data Annotations
                var validationContext = new ValidationContext(settings);
                var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

                if (!Validator.TryValidateObject(settings, validationContext, validationResults, true))
                {
                    // تصفية أخطاء البريد الإلكتروني إذا كان الحقل فارغاً
                    var filteredResults = validationResults.Where(vr =>
                        !(vr.MemberNames.Contains("CompanyEmail") && string.IsNullOrEmpty(settings.CompanyEmail)));

                    errors.AddRange(filteredResults.Select(vr => vr.ErrorMessage ?? "خطأ في التحقق"));
                }

                // تحققات إضافية مخصصة
                if (!settings.IsValid())
                {
                    errors.Add("الإعدادات لا تجتاز التحقق الأساسي");
                }

                // التحقق من مسار قاعدة البيانات
                if (!string.IsNullOrEmpty(settings.DatabasePath))
                {
                    var dbDir = Path.GetDirectoryName(settings.DatabasePath);
                    if (!string.IsNullOrEmpty(dbDir) && !Directory.Exists(dbDir))
                    {
                        errors.Add("مجلد قاعدة البيانات غير موجود");
                    }
                }

                // التحقق من ملف بيانات الاعتماد السحابية
                if (settings.EnableCloudSync && !string.IsNullOrEmpty(settings.CloudCredentialsPath))
                {
                    if (!File.Exists(settings.CloudCredentialsPath))
                    {
                        errors.Add("ملف بيانات الاعتماد السحابية غير موجود");
                    }
                }

                return (errors.Count == 0, errors.ToArray());
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("خطأ في التحقق من صحة الإعدادات", ex)!;
                errors.Add($"خطأ في التحقق: {ex.Message}");
                return (false, errors.ToArray());
            }
        }

        public async Task<T?> GetSettingAsync<T>(string key, T? defaultValue = default)
        {
            try
            {
                var settings = await LoadSettingsAsync();
                var property = typeof(SettingsModel).GetProperty(key);
                
                if (property != null && property.PropertyType == typeof(T))
                {
                    return (T?)property.GetValue(settings) ?? defaultValue;
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync($"فشل في الحصول على الإعداد: {key}", ex)!;
                return defaultValue;
            }
        }

        public async Task SetSettingAsync<T>(string key, T value)
        {
            try
            {
                var settings = await LoadSettingsAsync();
                var property = typeof(SettingsModel).GetProperty(key);
                
                if (property != null && property.PropertyType == typeof(T))
                {
                    var oldValue = property.GetValue(settings);
                    property.SetValue(settings, value);
                    
                    await SaveSettingsAsync(settings);
                    
                    // إثارة حدث تغيير الإعداد
                    SettingsChanged?.Invoke(this, new SettingsChangedEventArgs
                    {
                        SettingKey = key,
                        OldValue = oldValue,
                        NewValue = value,
                        ChangedAt = DateTime.Now
                    });
                }
                else
                {
                    throw new ArgumentException($"الإعداد غير موجود أو نوع البيانات غير متطابق: {key}");
                }
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync($"فشل في تعيين الإعداد: {key}", ex)!;
                throw;
            }
        }

        public async Task RemoveSettingAsync(string key)
        {
            try
            {
                var settings = await LoadSettingsAsync();
                var property = typeof(SettingsModel).GetProperty(key);
                
                if (property != null)
                {
                    // إعادة تعيين القيمة إلى الافتراضية
                    var defaultSettings = new SettingsModel();
                    var defaultValue = property.GetValue(defaultSettings);
                    property.SetValue(settings, defaultValue);
                    
                    await SaveSettingsAsync(settings);
                    await _loggingService?.LogInformationAsync($"تم حذف الإعداد: {key}")!;
                }
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync($"فشل في حذف الإعداد: {key}", ex)!;
                throw;
            }
        }

        public async Task<bool> SettingExistsAsync(string key)
        {
            try
            {
                var property = typeof(SettingsModel).GetProperty(key);
                return await Task.FromResult(property != null);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync($"فشل في التحقق من وجود الإعداد: {key}", ex)!;
                return false;
            }
        }

        public async Task<string> CreateBackupAsync()
        {
            try
            {
                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups", "Settings");
                Directory.CreateDirectory(backupDir);
                
                var backupPath = Path.Combine(backupDir, $"settings-backup-{DateTime.Now:yyyy-MM-dd-HH-mm-ss}.json");
                return await ExportSettingsAsync(backupPath);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في إنشاء نسخة احتياطية من الإعدادات", ex)!;
                throw;
            }
        }

        public async Task<bool> RestoreFromBackupAsync(string backupPath)
        {
            try
            {
                return await ImportSettingsAsync(backupPath);
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في استعادة الإعدادات من النسخة الاحتياطية", ex)!;
                return false;
            }
        }

        public async Task<SystemInfo> GetSystemInfoAsync()
        {
            try
            {
                var systemInfo = new SystemInfo
                {
                    ApplicationVersion = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0",
                    DatabaseVersion = "1.0",
                    OperatingSystem = Environment.OSVersion.ToString(),
                    DotNetVersion = Environment.Version.ToString(),
                    MemoryUsage = GC.GetTotalMemory(false),
                    Uptime = DateTime.Now - _applicationStartTime
                };

                // معلومات قاعدة البيانات
                if (_databaseContext != null)
                {
                    try
                    {
                        systemInfo.TotalInvoices = await _databaseContext.Invoices.CountAsync();
                        systemInfo.TotalPayments = await _databaseContext.Payments.CountAsync();
                        systemInfo.TotalSuppliers = await _databaseContext.Suppliers.CountAsync();
                        
                        // حجم قاعدة البيانات
                        var dbPath = _databaseContext.Database.GetConnectionString();
                        if (!string.IsNullOrEmpty(dbPath) && dbPath.Contains("Data Source="))
                        {
                            var actualPath = dbPath.Split("Data Source=")[1].Split(';')[0];
                            if (File.Exists(actualPath))
                            {
                                systemInfo.DatabaseSize = new FileInfo(actualPath).Length;
                                systemInfo.DatabasePath = actualPath;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        await _loggingService?.LogWarningAsync($"فشل في الحصول على معلومات قاعدة البيانات: {ex.Message}")!;
                    }
                }

                return systemInfo;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في الحصول على معلومات النظام", ex)!;
                return new SystemInfo();
            }
        }

        public async Task ApplySettingsAsync(SettingsModel settings)
        {
            try
            {
                // تطبيق إعدادات السمة
                if (!string.IsNullOrEmpty(settings.ApplicationTheme))
                {
                    // يمكن تطبيق السمة هنا
                }

                // تطبيق إعدادات اللغة
                if (!string.IsNullOrEmpty(settings.ApplicationLanguage))
                {
                    // يمكن تطبيق اللغة هنا
                }

                await _loggingService?.LogInformationAsync("تم تطبيق الإعدادات على النظام")!;
            }
            catch (Exception ex)
            {
                await _loggingService?.LogErrorAsync("فشل في تطبيق الإعدادات", ex)!;
                throw;
            }
        }
    }
}
