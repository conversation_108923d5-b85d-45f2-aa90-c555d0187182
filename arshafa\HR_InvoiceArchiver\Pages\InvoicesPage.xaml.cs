using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Windows;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Windows.Data;

namespace HR_InvoiceArchiver.Pages
{
    public partial class InvoicesPage : UserControl, INavigationAware
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;

        public ObservableCollection<Invoice> Invoices { get; set; } = new();
        public ObservableCollection<Invoice> AllInvoices { get; set; } = new();
        private ICollectionView _invoicesView;

        // Pagination support - دعم تقسيم الصفحات
        private PaginationCriteria _currentCriteria = new();
        private PaginationResult<Invoice>? _currentPage;
        private bool _isPaginationEnabled = true;

        public InvoicesPage(
            IInvoiceService invoiceService,
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            InitializeComponent();
            _invoiceService = invoiceService;
            _paymentService = paymentService;
            _toastService = toastService;
            _navigationService = navigationService;

            // Setup collection view for filtering
            _invoicesView = CollectionViewSource.GetDefaultView(Invoices);
            _invoicesView.Filter = FilterInvoices;
            InvoicesDataGrid.ItemsSource = _invoicesView;

            Loaded += InvoicesPage_Loaded;
        }

        private async void InvoicesPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadInvoicesAsync();
        }

        public void OnNavigatedTo(object parameter)
        {
            // Handle navigation parameters
            if (parameter is string action && action == "add")
            {
                // Show add invoice dialog
                AddInvoiceButton_Click(this, new RoutedEventArgs());
            }
            else
            {
                // Refresh data when navigating to this page
                Dispatcher.BeginInvoke(new Action(async () => await LoadInvoicesAsync()));
            }
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadInvoicesAsync()
        {
            try
            {
                ShowLoading(true);

                if (_isPaginationEnabled)
                {
                    // استخدام Pagination للأداء الأفضل
                    await LoadInvoicesWithPaginationAsync();
                }
                else
                {
                    // تحميل جميع البيانات (للتوافق مع الكود القديم)
                    await LoadAllInvoicesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading invoices: {ex.Message}");
                _toastService.ShowError("خطأ", "حدث خطأ في تحميل الفواتير");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        /// <summary>
        /// تحميل الفواتير مع تقسيم الصفحات (محسن للأداء)
        /// </summary>
        private async Task LoadInvoicesWithPaginationAsync()
        {
            var repository = App.ServiceProvider.GetRequiredService<Data.Repositories.IInvoiceRepository>();
            _currentPage = await repository.GetPagedAsync(_currentCriteria);

            Dispatcher.Invoke(() =>
            {
                AllInvoices.Clear();
                Invoices.Clear();

                foreach (var invoice in _currentPage.Items)
                {
                    AllInvoices.Add(invoice);
                    Invoices.Add(invoice);
                }

                UpdateStatistics();
                UpdateEmptyState();
                UpdatePaginationInfo();
                _invoicesView.Refresh();
            });
        }

        /// <summary>
        /// تحميل جميع الفواتير (للتوافق مع الكود القديم)
        /// </summary>
        private async Task LoadAllInvoicesAsync()
        {
            var invoices = await _invoiceService.GetAllInvoicesBasicAsync();

            Dispatcher.Invoke(() =>
            {
                AllInvoices.Clear();
                Invoices.Clear();

                foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
                {
                    AllInvoices.Add(invoice);
                    Invoices.Add(invoice);
                }

                UpdateStatistics();
                UpdateEmptyState();
                _invoicesView.Refresh();
            });
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            InvoicesDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateEmptyState()
        {
            EmptyStatePanel.Visibility = Invoices.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            InvoicesDataGrid.Visibility = Invoices.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        /// <summary>
        /// تحديث معلومات تقسيم الصفحات
        /// </summary>
        private void UpdatePaginationInfo()
        {
            if (_currentPage == null) return;

            // يمكن إضافة UI elements لعرض معلومات Pagination هنا
            // مثل: "صفحة 1 من 5" أو "عرض 1-50 من 200"

            System.Diagnostics.Debug.WriteLine($"Pagination: Page {_currentPage.PageNumber} of {_currentPage.TotalPages}, Total: {_currentPage.TotalCount}");
        }

        /// <summary>
        /// الانتقال للصفحة التالية
        /// </summary>
        public async Task NextPageAsync()
        {
            if (_currentPage?.HasNextPage == true)
            {
                _currentCriteria.PageNumber++;
                await LoadInvoicesWithPaginationAsync();
            }
        }

        /// <summary>
        /// الانتقال للصفحة السابقة
        /// </summary>
        public async Task PreviousPageAsync()
        {
            if (_currentPage?.HasPreviousPage == true)
            {
                _currentCriteria.PageNumber--;
                await LoadInvoicesWithPaginationAsync();
            }
        }

        /// <summary>
        /// تغيير حجم الصفحة
        /// </summary>
        public async Task ChangePageSizeAsync(int pageSize)
        {
            _currentCriteria.PageSize = pageSize;
            _currentCriteria.PageNumber = 1; // العودة للصفحة الأولى
            await LoadInvoicesWithPaginationAsync();
        }

        private void UpdateStatistics()
        {
            var totalInvoices = AllInvoices.Count;
            var unpaidInvoices = AllInvoices.Count(i => i.Status == InvoiceStatus.Unpaid);
            var partiallyPaidInvoices = AllInvoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
            var paidInvoices = AllInvoices.Count(i => i.Status == InvoiceStatus.Paid || i.Status == InvoiceStatus.PaidWithDiscount);
            var totalAmount = AllInvoices.Sum(i => i.Amount);

            TotalInvoicesText.Text = totalInvoices.ToString("N0");
            UnpaidInvoicesText.Text = unpaidInvoices.ToString("N0");
            PartiallyPaidInvoicesText.Text = partiallyPaidInvoices.ToString("N0");
            PaidInvoicesText.Text = paidInvoices.ToString("N0");
            TotalAmountText.Text = totalAmount.ToString("N0");
        }

        private bool FilterInvoices(object item)
        {
            if (item is not Invoice invoice) return false;

            // Search filter
            var searchText = SearchTextBox?.Text?.Trim().ToLower() ?? "";
            if (!string.IsNullOrEmpty(searchText))
            {
                var matchesSearch = invoice.InvoiceNumber.ToLower().Contains(searchText) ||
                                  invoice.SupplierName.ToLower().Contains(searchText) ||
                                  (invoice.Description?.ToLower().Contains(searchText) ?? false);

                if (!matchesSearch) return false;
            }

            // Status filter
            if (StatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null)
            {
                var selectedStatus = Enum.Parse<InvoiceStatus>(statusItem.Tag.ToString()!);
                if (invoice.Status != selectedStatus) return false;
            }

            // Date range filter
            if (FromDatePicker?.SelectedDate.HasValue == true)
            {
                if (invoice.InvoiceDate < FromDatePicker.SelectedDate.Value) return false;
            }

            if (ToDatePicker?.SelectedDate.HasValue == true)
            {
                if (invoice.InvoiceDate > ToDatePicker.SelectedDate.Value) return false;
            }

            return true;
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _invoicesView?.Refresh();
        }

        private void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            FilterPanel.Visibility = FilterPanel.Visibility == Visibility.Visible
                ? Visibility.Collapsed
                : Visibility.Visible;
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _invoicesView?.Refresh();
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            _invoicesView?.Refresh();
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            StatusFilterComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            _invoicesView?.Refresh();
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InvoiceFormOverlay.ShowAddInvoiceForm();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في فتح نموذج إضافة الفاتورة: {ex.Message}");
            }
        }

        private void InvoiceFormOverlay_FormClosed(object sender, Controls.InvoiceFormEventArgs e)
        {
            if (e.Success)
            {
                // Show success message
                string message = e.Invoice != null && e.Invoice.Id > 0 ?
                    "تم تحديث الفاتورة وتحديث الجدول بنجاح!" :
                    "تم إضافة الفاتورة وتحديث الجدول بنجاح!";

                _toastService.ShowSuccess("تم بنجاح", message);

                // Refresh the invoices list immediately
                Dispatcher.BeginInvoke(new Action(async () =>
                {
                    await LoadInvoicesAsync();

                    // Scroll to the newly added/updated invoice if it's a new one
                    if (e.Invoice != null && Invoices.Count > 0)
                    {
                        var targetInvoice = Invoices.FirstOrDefault(i => i.InvoiceNumber == e.Invoice.InvoiceNumber);
                        if (targetInvoice != null)
                        {
                            InvoicesDataGrid.SelectedItem = targetInvoice;
                            InvoicesDataGrid.ScrollIntoView(targetInvoice);
                        }
                    }
                }));
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadInvoicesAsync();
        }

        private void InvoicesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                try
                {
                    var editInvoiceWindow = new AddEditInvoiceWindow(selectedInvoice);
                    if (editInvoiceWindow.ShowDialog() == true)
                    {
                        _toastService.ShowSuccess("تم تحديث الفاتورة بنجاح", "تمت العملية بنجاح");
                        Dispatcher.BeginInvoke(new Action(async () => await LoadInvoicesAsync()));
                    }
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في تحديث الفاتورة", ex.Message);
                }
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|Excel files (*.xlsx)|*.xlsx",
                    DefaultExt = "csv",
                    FileName = $"الفواتير_{DateTime.Now:yyyy-MM-dd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Simulate async export operation
                    await Task.Delay(100);
                    // TODO: Implement export functionality
                    _toastService.ShowSuccess("تم التصدير", "تم تصدير البيانات بنجاح");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تصدير البيانات: {ex.Message}");
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement print functionality
                _toastService.ShowInfo("قريباً", "ميزة الطباعة ستكون متاحة قريباً");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في الطباعة: {ex.Message}");
            }
        }

        private void QuickFilterUnpaid_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Set status filter to unpaid
                StatusFilterComboBox.SelectedIndex = 1; // Unpaid option

                _toastService.ShowInfo("تم التطبيق", "تم عرض الفواتير غير المسددة فقط");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterPaid_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Set status filter to paid
                StatusFilterComboBox.SelectedIndex = 3; // Paid option

                _toastService.ShowInfo("تم التطبيق", "تم عرض الفواتير المسددة فقط");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void ViewInvoiceAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Invoice invoice)
                {
                    if (string.IsNullOrEmpty(invoice.AttachmentPath))
                    {
                        _toastService.ShowWarning("تنبيه", "لا يوجد مرفق للفاتورة");
                        return;
                    }

                    var fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments", invoice.AttachmentPath);

                    if (File.Exists(fullPath))
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = fullPath,
                                UseShellExecute = true
                            });
                            _toastService.ShowSuccess("تم فتح المرفق", "تم فتح مرفق الفاتورة بنجاح");
                        }
                        catch (Exception ex)
                        {
                            _toastService.ShowError("خطأ في فتح الملف", $"فشل في فتح المرفق: {ex.Message}");
                        }
                    }
                    else
                    {
                        _toastService.ShowError("ملف غير موجود", "مرفق الفاتورة غير موجود في المسار المحدد");
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض مرفق الفاتورة: {ex.Message}");
            }
        }

        private async void ViewReceiptAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Invoice invoice)
                {
                    // Get payments for this invoice
                    var payments = await _paymentService.GetPaymentsByInvoiceAsync(invoice.Id);
                    var paymentsWithAttachments = payments.Where(p => !string.IsNullOrEmpty(p.AttachmentPath)).ToList();

                    if (!paymentsWithAttachments.Any())
                    {
                        _toastService.ShowWarning("تنبيه", "لا يوجد مرفقات للوصولات");
                        return;
                    }

                    if (paymentsWithAttachments.Count == 1)
                    {
                        // Open single attachment directly
                        var payment = paymentsWithAttachments.First();
                        OpenPaymentAttachment(payment);
                    }
                    else
                    {
                        // Show selection dialog for multiple attachments
                        ShowPaymentAttachmentsDialog(paymentsWithAttachments);
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض مرفقات الوصولات: {ex.Message}");
            }
        }

        private void OpenPaymentAttachment(Payment payment)
        {
            try
            {
                if (string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    _toastService.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                    return;
                }

                Utils.FileHelper.OpenAttachment(payment.AttachmentPath, "Payments");
                _toastService.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {payment.ReceiptNumber}");
            }
            catch (FileNotFoundException)
            {
                _toastService.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في فتح الملف", $"فشل في فتح مرفق الوصل: {ex.Message}");
            }
        }

        private void ShowPaymentAttachmentsDialog(List<Payment> payments)
        {
            try
            {
                var dialog = new Window
                {
                    Title = "اختيار مرفق الوصل",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.NoResize
                };

                var stackPanel = new StackPanel { Margin = new Thickness(20) };

                stackPanel.Children.Add(new TextBlock
                {
                    Text = "اختر الوصل المطلوب عرض مرفقه:",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 0, 15)
                });

                foreach (var payment in payments)
                {
                    var button = new Button
                    {
                        Content = $"وصل رقم {payment.ReceiptNumber} - {payment.Amount:N0} د.ع - {payment.PaymentDate:dd/MM/yyyy}",
                        Margin = new Thickness(0, 0, 0, 10),
                        Padding = new Thickness(15, 8, 15, 8),
                        HorizontalAlignment = HorizontalAlignment.Stretch,
                        Tag = payment
                    };

                    button.Click += (s, e) =>
                    {
                        if (s is Button btn && btn.Tag is Payment selectedPayment)
                        {
                            OpenPaymentAttachment(selectedPayment);
                            dialog.Close();
                        }
                    };

                    stackPanel.Children.Add(button);
                }

                dialog.Content = stackPanel;
                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض قائمة المرفقات: {ex.Message}");
            }
        }
    }
}
