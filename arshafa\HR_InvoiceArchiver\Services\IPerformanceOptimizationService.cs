using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة تحسين الأداء
    /// </summary>
    public interface IPerformanceOptimizationService
    {
        /// <summary>
        /// تحليل أداء قاعدة البيانات
        /// </summary>
        Task<DatabasePerformanceReport> AnalyzeDatabasePerformanceAsync();

        /// <summary>
        /// إنشاء الفهارس المطلوبة
        /// </summary>
        Task<bool> CreateOptimalIndexesAsync();

        /// <summary>
        /// تحديث إحصائيات قاعدة البيانات
        /// </summary>
        Task<bool> UpdateDatabaseStatisticsAsync();

        /// <summary>
        /// تنظيف قاعدة البيانات من البيانات القديمة
        /// </summary>
        Task<DatabaseCleanupResult> CleanupDatabaseAsync();

        /// <summary>
        /// ضغط قاعدة البيانات
        /// </summary>
        Task<bool> CompressDatabaseAsync();

        /// <summary>
        /// تحليل الاستعلامات البطيئة
        /// </summary>
        Task<List<SlowQueryInfo>> AnalyzeSlowQueriesAsync();

        /// <summary>
        /// تحسين استعلام محدد
        /// </summary>
        Task<QueryOptimizationResult> OptimizeQueryAsync(string query);

        /// <summary>
        /// مراقبة استخدام الذاكرة
        /// </summary>
        Task<MemoryUsageInfo> GetMemoryUsageInfoAsync();

        /// <summary>
        /// تنظيف ذاكرة التخزين المؤقت
        /// </summary>
        Task ClearCacheAsync();

        /// <summary>
        /// تحسين حجم ذاكرة التخزين المؤقت
        /// </summary>
        Task OptimizeCacheSizeAsync();

        /// <summary>
        /// تحليل أداء التطبيق
        /// </summary>
        Task<ApplicationPerformanceReport> AnalyzeApplicationPerformanceAsync();

        /// <summary>
        /// تحسين إعدادات الاتصال بقاعدة البيانات
        /// </summary>
        Task<bool> OptimizeConnectionSettingsAsync();

        /// <summary>
        /// جدولة مهام التحسين التلقائي
        /// </summary>
        Task ScheduleAutomaticOptimizationAsync();

        /// <summary>
        /// إلغاء جدولة مهام التحسين التلقائي
        /// </summary>
        Task UnscheduleAutomaticOptimizationAsync();
    }

    /// <summary>
    /// تقرير أداء قاعدة البيانات
    /// </summary>
    public class DatabasePerformanceReport
    {
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public long DatabaseSizeBytes { get; set; }
        public int TotalTables { get; set; }
        public int TotalIndexes { get; set; }
        public int MissingIndexes { get; set; }
        public int UnusedIndexes { get; set; }
        public double AverageQueryTimeMs { get; set; }
        public int SlowQueriesCount { get; set; }
        public double FragmentationPercentage { get; set; }
        public List<TablePerformanceInfo> TablePerformance { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public PerformanceScore OverallScore { get; set; }
    }

    /// <summary>
    /// معلومات أداء الجدول
    /// </summary>
    public class TablePerformanceInfo
    {
        public string TableName { get; set; } = string.Empty;
        public long RowCount { get; set; }
        public long SizeBytes { get; set; }
        public int IndexCount { get; set; }
        public double AverageQueryTimeMs { get; set; }
        public double FragmentationPercentage { get; set; }
        public List<string> SuggestedIndexes { get; set; } = new();
    }

    /// <summary>
    /// نتيجة تنظيف قاعدة البيانات
    /// </summary>
    public class DatabaseCleanupResult
    {
        public int DeletedRecords { get; set; }
        public long FreedSpaceBytes { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> CleanedTables { get; set; } = new();
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// معلومات الاستعلام البطيء
    /// </summary>
    public class SlowQueryInfo
    {
        public string Query { get; set; } = string.Empty;
        public double ExecutionTimeMs { get; set; }
        public int ExecutionCount { get; set; }
        public DateTime LastExecuted { get; set; }
        public string? SuggestedOptimization { get; set; }
        public List<string> MissingIndexes { get; set; } = new();
    }

    /// <summary>
    /// نتيجة تحسين الاستعلام
    /// </summary>
    public class QueryOptimizationResult
    {
        public string OriginalQuery { get; set; } = string.Empty;
        public string OptimizedQuery { get; set; } = string.Empty;
        public double OriginalExecutionTimeMs { get; set; }
        public double OptimizedExecutionTimeMs { get; set; }
        public double ImprovementPercentage { get; set; }
        public List<string> AppliedOptimizations { get; set; } = new();
        public bool Success { get; set; }
    }

    /// <summary>
    /// معلومات استخدام الذاكرة
    /// </summary>
    public class MemoryUsageInfo
    {
        public long TotalMemoryBytes { get; set; }
        public long UsedMemoryBytes { get; set; }
        public long AvailableMemoryBytes { get; set; }
        public double UsagePercentage { get; set; }
        public long CacheMemoryBytes { get; set; }
        public long GCMemoryBytes { get; set; }
        public int GCCollections { get; set; }
        public List<string> MemoryRecommendations { get; set; } = new();
    }

    /// <summary>
    /// تقرير أداء التطبيق
    /// </summary>
    public class ApplicationPerformanceReport
    {
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public TimeSpan Uptime { get; set; }
        public double CpuUsagePercentage { get; set; }
        public MemoryUsageInfo MemoryUsage { get; set; } = new();
        public int ActiveConnections { get; set; }
        public double AverageResponseTimeMs { get; set; }
        public int TotalRequests { get; set; }
        public int ErrorCount { get; set; }
        public double ErrorRate { get; set; }
        public List<string> PerformanceIssues { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public PerformanceScore OverallScore { get; set; }
    }

    /// <summary>
    /// درجة الأداء
    /// </summary>
    public enum PerformanceScore
    {
        Excellent = 5,
        Good = 4,
        Average = 3,
        Poor = 2,
        Critical = 1
    }
}
