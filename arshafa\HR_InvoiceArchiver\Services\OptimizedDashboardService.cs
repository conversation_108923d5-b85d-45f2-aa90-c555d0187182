using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    public class OptimizedDashboardService : IDashboardService
    {
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly ISupplierRepository _supplierRepository;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IMemoryCache _cache;
        private readonly ILogger<OptimizedDashboardService> _logger;

        // Cache keys
        private const string STATISTICS_CACHE_KEY = "dashboard_statistics";
        private const string TRENDS_CACHE_KEY = "monthly_trends";
        private const string ALERTS_CACHE_KEY = "dashboard_alerts";

        // Cache expiry times
        private readonly TimeSpan _statisticsCacheExpiry = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _trendsCacheExpiry = TimeSpan.FromMinutes(15);
        private readonly TimeSpan _alertsCacheExpiry = TimeSpan.FromMinutes(2);

        public OptimizedDashboardService(
            IInvoiceRepository invoiceRepository,
            ISupplierRepository supplierRepository,
            IPaymentRepository paymentRepository,
            IMemoryCache cache,
            ILogger<OptimizedDashboardService> logger)
        {
            _invoiceRepository = invoiceRepository;
            _supplierRepository = supplierRepository;
            _paymentRepository = paymentRepository;
            _cache = cache;
            _logger = logger;
        }

        public async Task<DashboardStatistics> GetDashboardStatisticsAsync()
        {
            try
            {
                // Check cache first
                if (_cache.TryGetValue(STATISTICS_CACHE_KEY, out DashboardStatistics? cachedStats))
                {
                    return cachedStats!;
                }

                // Calculate statistics efficiently
                var statistics = await CalculateStatisticsOptimized();

                // Cache the result
                _cache.Set(STATISTICS_CACHE_KEY, statistics, _statisticsCacheExpiry);

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting optimized dashboard statistics");
                throw;
            }
        }

        private async Task<DashboardStatistics> CalculateStatisticsOptimized()
        {
            // Get all invoices (simplified for now)
            var invoices = await _invoiceRepository.GetAllAsync();
            var invoicesList = invoices.ToList();

            var totalInvoices = invoicesList.Count;
            var totalAmount = invoicesList.Sum(i => i.Amount);
            var paidAmount = invoicesList.Sum(i => i.PaidAmount);
            var outstandingAmount = totalAmount - paidAmount;

            var unpaidCount = invoicesList.Count(i => i.Status == InvoiceStatus.Unpaid);
            var partiallyPaidCount = invoicesList.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
            var paidCount = invoicesList.Count(i => i.Status == InvoiceStatus.Paid);

            // Calculate overdue invoices efficiently
            var thirtyDaysAgo = DateTime.Now.AddDays(-30);
            var overdueInvoices = invoicesList.Count(i =>
                i.Status != InvoiceStatus.Paid && i.InvoiceDate < thirtyDaysAgo);

            var paymentRate = totalInvoices > 0 ? (double)paidCount / totalInvoices * 100 : 0;

            return new DashboardStatistics
            {
                TotalInvoices = totalInvoices,
                TotalAmount = totalAmount,
                PaidAmount = paidAmount,
                OutstandingAmount = outstandingAmount,
                OverdueInvoices = overdueInvoices,
                UnpaidCount = unpaidCount,
                PartiallyPaidCount = partiallyPaidCount,
                PaidCount = paidCount,
                PaymentRate = paymentRate,
                LastUpdated = DateTime.Now
            };
        }

        public async Task<IEnumerable<MonthlyTrend>> GetMonthlyTrendsAsync(int months = 6)
        {
            try
            {
                var cacheKey = $"{TRENDS_CACHE_KEY}_{months}";
                
                if (_cache.TryGetValue(cacheKey, out IEnumerable<MonthlyTrend>? cachedTrends))
                {
                    return cachedTrends!;
                }

                var trends = await CalculateMonthlyTrendsOptimized(months);
                
                _cache.Set(cacheKey, trends, _trendsCacheExpiry);
                
                return trends;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly trends");
                throw;
            }
        }

        private async Task<IEnumerable<MonthlyTrend>> CalculateMonthlyTrendsOptimized(int months)
        {
            var startDate = DateTime.Now.AddMonths(-months);
            var invoices = await _invoiceRepository.GetAllAsync();
            var payments = await _paymentRepository.GetAllAsync();

            var trends = new List<MonthlyTrend>();

            for (int i = 0; i < months; i++)
            {
                var periodStart = startDate.AddMonths(i);
                var periodEnd = periodStart.AddMonths(1).AddDays(-1);

                var monthInvoices = invoices.Where(inv => 
                    inv.InvoiceDate >= periodStart && inv.InvoiceDate <= periodEnd);

                var monthPayments = payments.Where(p => 
                    p.PaymentDate >= periodStart && p.PaymentDate <= periodEnd);

                var totalAmount = monthInvoices.Sum(i => i.Amount);
                var paidAmount = monthPayments.Sum(p => p.Amount);

                trends.Add(new MonthlyTrend
                {
                    Month = periodStart.ToString("MMMM"),
                    Year = periodStart.Year,
                    TotalAmount = totalAmount,
                    PaidAmount = paidAmount,
                    PeriodStart = periodStart,
                    PeriodEnd = periodEnd
                });
            }

            return trends;
        }

        public async Task<IEnumerable<AlertItem>> GetDashboardAlertsAsync()
        {
            try
            {
                if (_cache.TryGetValue(ALERTS_CACHE_KEY, out IEnumerable<AlertItem>? cachedAlerts))
                {
                    return cachedAlerts!;
                }

                var alerts = await GenerateAlertsOptimized();
                
                _cache.Set(ALERTS_CACHE_KEY, alerts, _alertsCacheExpiry);
                
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard alerts");
                throw;
            }
        }

        private async Task<IEnumerable<AlertItem>> GenerateAlertsOptimized()
        {
            var alerts = new List<AlertItem>();

            // Get all invoices and count overdue ones
            var invoices = await _invoiceRepository.GetAllAsync();
            var thirtyDaysAgo = DateTime.Now.AddDays(-30);
            var overdueInvoices = invoices.Count(i =>
                i.Status != InvoiceStatus.Paid && i.InvoiceDate < thirtyDaysAgo);
            
            if (overdueInvoices > 0)
            {
                alerts.Add(new AlertItem
                {
                    Id = 1,
                    Type = AlertType.Warning,
                    Title = "فواتير متأخرة",
                    Message = $"لديك {overdueInvoices} فاتورة متأخرة السداد",
                    Icon = "AlertCircle",
                    Priority = AlertPriority.High,
                    Timestamp = DateTime.Now
                });
            }

            return alerts;
        }

        public void ClearCache()
        {
            _cache.Remove(STATISTICS_CACHE_KEY);
            _cache.Remove(TRENDS_CACHE_KEY);
            _cache.Remove(ALERTS_CACHE_KEY);
        }

        // Implement other interface methods with basic functionality
        public Task<IEnumerable<SupplierStatistics>> GetTopSuppliersAsync(int count = 10)
        {
            return Task.FromResult(Enumerable.Empty<SupplierStatistics>());
        }

        public Task<IEnumerable<PaymentTrend>> GetPaymentTrendsAsync(int months = 6)
        {
            return Task.FromResult(Enumerable.Empty<PaymentTrend>());
        }

        public Task<CashFlowAnalysis> GetCashFlowAnalysisAsync()
        {
            return Task.FromResult(new CashFlowAnalysis());
        }

        public Task<ExportResult> ExportDashboardDataAsync(ExportFormat format, ExportOptions options)
        {
            return Task.FromResult(new ExportResult { Success = false, ErrorMessage = "Not implemented" });
        }

        public Task<bool> MarkAlertAsReadAsync(int alertId)
        {
            return Task.FromResult(true);
        }

        public Task<bool> ClearAllAlertsAsync()
        {
            ClearCache();
            return Task.FromResult(true);
        }

        public Task<DashboardSettings> GetDashboardSettingsAsync()
        {
            return Task.FromResult(new DashboardSettings());
        }

        public Task<bool> UpdateDashboardSettingsAsync(DashboardSettings settings)
        {
            return Task.FromResult(true);
        }
    }

    // Note: AlertItem and AlertPriority are already defined in IDashboardService.cs
}
