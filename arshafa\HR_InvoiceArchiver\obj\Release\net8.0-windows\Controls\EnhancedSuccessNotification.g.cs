﻿#pragma checksum "..\..\..\..\Controls\EnhancedSuccessNotification.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E5DBF70A688F00A825FE5DA4860E6143DB551046"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Controls {
    
    
    /// <summary>
    /// EnhancedSuccessNotification
    /// </summary>
    public partial class EnhancedSuccessNotification : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 98 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationCard;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border IconContainer;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IconText;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MessageText;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ActionPanel;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SecondaryActionButton;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrimaryActionButton;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimestampText;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/controls/enhancedsuccessnotification.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 24 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
            ((System.Windows.Media.Animation.Storyboard)(target)).Completed += new System.EventHandler(this.SlideOutAnimation_Completed);
            
            #line default
            #line hidden
            return;
            case 2:
            this.NotificationCard = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.IconContainer = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.IconText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.MessageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ActionPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.SecondaryActionButton = ((System.Windows.Controls.Button)(target));
            
            #line 189 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
            this.SecondaryActionButton.Click += new System.Windows.RoutedEventHandler(this.SecondaryActionButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PrimaryActionButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\Controls\EnhancedSuccessNotification.xaml"
            this.PrimaryActionButton.Click += new System.Windows.RoutedEventHandler(this.PrimaryActionButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TimestampText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ProgressBar = ((System.Windows.Shapes.Rectangle)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

