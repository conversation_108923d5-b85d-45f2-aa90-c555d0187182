using ClosedXML.Excel;
using iTextSharp.text;
using iTextSharp.text.pdf;
using HR_InvoiceArchiver.Models;
using System.IO;
using System.Linq;

namespace HR_InvoiceArchiver.Utils
{
    public static class ExportHelper
    {
        public static async Task<string> ExportInvoicesToExcelAsync(IEnumerable<Invoice> invoices, string fileName = "")
        {
            if (string.IsNullOrEmpty(fileName))
                fileName = $"Invoices_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

            var exportsPath = FileHelper.GetExportsDirectory();
            var filePath = Path.Combine(exportsPath, fileName);

            await Task.Run(() =>
            {
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("الفواتير");

                // Set RTL direction
                worksheet.RightToLeft = true;

                // Headers
                worksheet.Cell(1, 1).Value = "رقم الفاتورة";
                worksheet.Cell(1, 2).Value = "المورد";
                worksheet.Cell(1, 3).Value = "تاريخ الفاتورة";
                worksheet.Cell(1, 4).Value = "المبلغ";
                worksheet.Cell(1, 5).Value = "المبلغ المدفوع";
                worksheet.Cell(1, 6).Value = "المبلغ المتبقي";
                worksheet.Cell(1, 7).Value = "الحالة";
                worksheet.Cell(1, 8).Value = "تاريخ الاستحقاق";
                worksheet.Cell(1, 9).Value = "الملاحظات";

                // Style headers
                var headerRange = worksheet.Range(1, 1, 1, 9);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
                headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                // Data
                int row = 2;
                foreach (var invoice in invoices)
                {
                    worksheet.Cell(row, 1).Value = invoice.InvoiceNumber;
                    worksheet.Cell(row, 2).Value = invoice.Supplier?.Name ?? "";
                    worksheet.Cell(row, 3).Value = invoice.InvoiceDate.ToString("yyyy/MM/dd");
                    worksheet.Cell(row, 4).Value = invoice.Amount;
                    worksheet.Cell(row, 5).Value = invoice.PaidAmount;
                    worksheet.Cell(row, 6).Value = invoice.RemainingAmount;
                    worksheet.Cell(row, 7).Value = invoice.StatusText;
                    worksheet.Cell(row, 8).Value = invoice.DueDate?.ToString("yyyy/MM/dd") ?? "";
                    worksheet.Cell(row, 9).Value = invoice.Notes ?? "";

                    // Color code based on status
                    var statusCell = worksheet.Cell(row, 7);
                    switch (invoice.Status)
                    {
                        case InvoiceStatus.Paid:
                            statusCell.Style.Fill.BackgroundColor = XLColor.LightGreen;
                            break;
                        case InvoiceStatus.PartiallyPaid:
                            statusCell.Style.Fill.BackgroundColor = XLColor.Yellow;
                            break;
                        case InvoiceStatus.Unpaid:
                            statusCell.Style.Fill.BackgroundColor = XLColor.LightPink;
                            break;
                    }

                    row++;
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                // Add totals row
                if (invoices.Any())
                {
                    row++;
                    worksheet.Cell(row, 3).Value = "الإجمالي:";
                    worksheet.Cell(row, 3).Style.Font.Bold = true;
                    worksheet.Cell(row, 4).Value = invoices.Sum(i => i.Amount);
                    worksheet.Cell(row, 5).Value = invoices.Sum(i => i.PaidAmount);
                    worksheet.Cell(row, 6).Value = invoices.Sum(i => i.RemainingAmount);

                    var totalsRange = worksheet.Range(row, 3, row, 6);
                    totalsRange.Style.Font.Bold = true;
                    totalsRange.Style.Fill.BackgroundColor = XLColor.LightGray;
                }

                workbook.SaveAs(filePath);
            });

            return filePath;
        }

        public static async Task<string> ExportPaymentsToExcelAsync(IEnumerable<Payment> payments, string fileName = "")
        {
            if (string.IsNullOrEmpty(fileName))
                fileName = $"Payments_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

            var exportsPath = FileHelper.GetExportsDirectory();
            var filePath = Path.Combine(exportsPath, fileName);

            await Task.Run(() =>
            {
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("المدفوعات");

                // Set RTL direction
                worksheet.RightToLeft = true;

                // Headers
                worksheet.Cell(1, 1).Value = "رقم الوصل";
                worksheet.Cell(1, 2).Value = "رقم الفاتورة";
                worksheet.Cell(1, 3).Value = "المورد";
                worksheet.Cell(1, 4).Value = "تاريخ الدفع";
                worksheet.Cell(1, 5).Value = "المبلغ";
                worksheet.Cell(1, 6).Value = "طريقة الدفع";
                worksheet.Cell(1, 7).Value = "الملاحظات";

                // Style headers
                var headerRange = worksheet.Range(1, 1, 1, 7);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
                headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                // Data
                int row = 2;
                foreach (var payment in payments)
                {
                    worksheet.Cell(row, 1).Value = payment.ReceiptNumber;
                    worksheet.Cell(row, 2).Value = payment.Invoice?.InvoiceNumber ?? "";
                    worksheet.Cell(row, 3).Value = payment.Invoice?.Supplier?.Name ?? "";
                    worksheet.Cell(row, 4).Value = payment.PaymentDate.ToString("yyyy/MM/dd");
                    worksheet.Cell(row, 5).Value = payment.Amount;
                    worksheet.Cell(row, 6).Value = payment.MethodText;
                    worksheet.Cell(row, 7).Value = payment.Notes ?? "";

                    row++;
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                // Add totals row
                if (payments.Any())
                {
                    row++;
                    worksheet.Cell(row, 4).Value = "الإجمالي:";
                    worksheet.Cell(row, 4).Style.Font.Bold = true;
                    worksheet.Cell(row, 5).Value = payments.Sum(p => p.Amount);

                    var totalsRange = worksheet.Range(row, 4, row, 5);
                    totalsRange.Style.Font.Bold = true;
                    totalsRange.Style.Fill.BackgroundColor = XLColor.LightGray;
                }

                workbook.SaveAs(filePath);
            });

            return filePath;
        }

        public static async Task<string> ExportSuppliersToExcelAsync(IEnumerable<Supplier> suppliers, string fileName = "")
        {
            if (string.IsNullOrEmpty(fileName))
                fileName = $"Suppliers_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

            var exportsPath = FileHelper.GetExportsDirectory();
            var filePath = Path.Combine(exportsPath, fileName);

            await Task.Run(() =>
            {
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("الموردين");

                // Set RTL direction
                worksheet.RightToLeft = true;

                // Headers
                worksheet.Cell(1, 1).Value = "اسم المورد";
                worksheet.Cell(1, 2).Value = "الشخص المسؤول";
                worksheet.Cell(1, 3).Value = "الهاتف";
                worksheet.Cell(1, 4).Value = "البريد الإلكتروني";
                worksheet.Cell(1, 5).Value = "العنوان";
                worksheet.Cell(1, 6).Value = "عدد الفواتير";
                worksheet.Cell(1, 7).Value = "إجمالي المبلغ";
                worksheet.Cell(1, 8).Value = "المبلغ المدفوع";
                worksheet.Cell(1, 9).Value = "المبلغ المتبقي";

                // Style headers
                var headerRange = worksheet.Range(1, 1, 1, 9);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
                headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                // Data
                int row = 2;
                foreach (var supplier in suppliers)
                {
                    worksheet.Cell(row, 1).Value = supplier.Name;
                    worksheet.Cell(row, 2).Value = supplier.ContactPerson ?? "";
                    worksheet.Cell(row, 3).Value = supplier.Phone ?? "";
                    worksheet.Cell(row, 4).Value = supplier.Email ?? "";
                    worksheet.Cell(row, 5).Value = supplier.Address ?? "";
                    worksheet.Cell(row, 6).Value = supplier.TotalInvoicesCount;
                    worksheet.Cell(row, 7).Value = supplier.TotalInvoicesAmount;
                    worksheet.Cell(row, 8).Value = supplier.TotalPaidAmount;
                    worksheet.Cell(row, 9).Value = supplier.RemainingAmount;

                    row++;
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                workbook.SaveAs(filePath);
            });

            return filePath;
        }

        public static async Task<string> ExportInvoicesToPdfAsync(IEnumerable<Invoice> invoices, string fileName = "")
        {
            if (string.IsNullOrEmpty(fileName))
                fileName = $"Invoices_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";

            var exportsPath = FileHelper.GetExportsDirectory();
            var filePath = Path.Combine(exportsPath, fileName);

            await Task.Run(() =>
            {
                using var document = new Document(PageSize.A4);
                using var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));

                document.Open();

                // Add Arabic font support
                var arabicFont = GetArabicFont();
                var headerFont = GetArabicFont(12, Font.BOLD);

                // Title
                document.Add(new Paragraph("تقرير الفواتير", headerFont));
                document.Add(new Paragraph($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}", arabicFont));
                document.Add(new Paragraph(" ", arabicFont)); // Empty line

                // Invoices table
                if (invoices.Any())
                {
                    var table = new PdfPTable(7) { WidthPercentage = 100 };
                    table.SetWidths(new float[] { 1, 1.5f, 1, 1, 1, 1, 1 });

                    // Headers
                    AddTableCell(table, "رقم الفاتورة", headerFont);
                    AddTableCell(table, "المورد", headerFont);
                    AddTableCell(table, "التاريخ", headerFont);
                    AddTableCell(table, "المبلغ", headerFont);
                    AddTableCell(table, "المدفوع", headerFont);
                    AddTableCell(table, "المتبقي", headerFont);
                    AddTableCell(table, "الحالة", headerFont);

                    // Data
                    foreach (var invoice in invoices)
                    {
                        AddTableCell(table, invoice.InvoiceNumber, arabicFont);
                        AddTableCell(table, invoice.Supplier?.Name ?? "", arabicFont);
                        AddTableCell(table, invoice.InvoiceDate.ToString("yyyy/MM/dd"), arabicFont);
                        AddTableCell(table, CurrencyHelper.FormatForPrint(invoice.Amount), arabicFont);
                        AddTableCell(table, CurrencyHelper.FormatForPrint(invoice.PaidAmount), arabicFont);
                        AddTableCell(table, CurrencyHelper.FormatForPrint(invoice.RemainingAmount), arabicFont);
                        AddTableCell(table, invoice.StatusText, arabicFont);
                    }

                    document.Add(table);
                }
                else
                {
                    document.Add(new Paragraph("لا توجد فواتير للعرض", arabicFont));
                }

                document.Close();
            });

            return filePath;
        }

        public static async Task<string> ExportAccountStatementToPdfAsync(Supplier supplier, IEnumerable<Invoice> invoices, string fileName = "")
        {
            if (string.IsNullOrEmpty(fileName))
                fileName = $"AccountStatement_{supplier.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";

            var exportsPath = FileHelper.GetExportsDirectory();
            var filePath = Path.Combine(exportsPath, fileName);

            await Task.Run(() =>
            {
                using var document = new Document(PageSize.A4);
                using var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
                
                document.Open();

                // Add Arabic font support
                var arabicFont = GetArabicFont();
                var titleFont = GetArabicFont(16, Font.BOLD);
                var headerFont = GetArabicFont(12, Font.BOLD);

                // Title
                var title = new Paragraph($"كشف حساب - {supplier.Name}", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // Supplier info
                document.Add(new Paragraph($"اسم المورد: {supplier.Name}", headerFont));
                if (!string.IsNullOrEmpty(supplier.ContactPerson))
                    document.Add(new Paragraph($"الشخص المسؤول: {supplier.ContactPerson}", arabicFont));
                if (!string.IsNullOrEmpty(supplier.Phone))
                    document.Add(new Paragraph($"الهاتف: {supplier.Phone}", arabicFont));
                if (!string.IsNullOrEmpty(supplier.Email))
                    document.Add(new Paragraph($"البريد الإلكتروني: {supplier.Email}", arabicFont));

                document.Add(new Paragraph($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}", arabicFont));
                document.Add(new Paragraph(" ", arabicFont)); // Empty line

                // Summary
                var summary = new PdfPTable(2) { WidthPercentage = 100 };
                summary.SetWidths(new float[] { 1, 1 });

                AddTableCell(summary, "إجمالي الفواتير:", headerFont);
                AddTableCell(summary, CurrencyHelper.FormatForPrint(supplier.TotalInvoicesAmount), arabicFont);
                AddTableCell(summary, "إجمالي المدفوع:", headerFont);
                AddTableCell(summary, CurrencyHelper.FormatForPrint(supplier.TotalPaidAmount), arabicFont);
                AddTableCell(summary, "المبلغ المتبقي:", headerFont);
                AddTableCell(summary, CurrencyHelper.FormatForPrint(supplier.RemainingAmount), arabicFont);

                document.Add(summary);
                document.Add(new Paragraph(" ", arabicFont)); // Empty line

                // Invoices table
                if (invoices.Any())
                {
                    document.Add(new Paragraph("تفاصيل الفواتير:", headerFont));
                    
                    var table = new PdfPTable(6) { WidthPercentage = 100 };
                    table.SetWidths(new float[] { 1, 1, 1, 1, 1, 1 });

                    // Headers
                    AddTableCell(table, "رقم الفاتورة", headerFont);
                    AddTableCell(table, "التاريخ", headerFont);
                    AddTableCell(table, "المبلغ", headerFont);
                    AddTableCell(table, "المدفوع", headerFont);
                    AddTableCell(table, "المتبقي", headerFont);
                    AddTableCell(table, "الحالة", headerFont);

                    // Data
                    foreach (var invoice in invoices.OrderBy(i => i.InvoiceDate))
                    {
                        AddTableCell(table, invoice.InvoiceNumber, arabicFont);
                        AddTableCell(table, invoice.InvoiceDate.ToString("yyyy/MM/dd"), arabicFont);
                        AddTableCell(table, CurrencyHelper.FormatForPrint(invoice.Amount), arabicFont);
                        AddTableCell(table, CurrencyHelper.FormatForPrint(invoice.PaidAmount), arabicFont);
                        AddTableCell(table, CurrencyHelper.FormatForPrint(invoice.RemainingAmount), arabicFont);
                        AddTableCell(table, invoice.StatusText, arabicFont);
                    }

                    document.Add(table);
                }

                document.Close();
            });

            return filePath;
        }

        private static Font GetArabicFont(int size = 12, int style = Font.NORMAL)
        {
            // For Arabic text support, you might need to use a specific Arabic font
            // This is a simplified version - in production, you'd want to embed an Arabic font
            return FontFactory.GetFont(FontFactory.HELVETICA, size, style);
        }

        private static void AddTableCell(PdfPTable table, string text, Font font)
        {
            var cell = new PdfPCell(new Phrase(text, font))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                Padding = 5
            };
            table.AddCell(cell);
        }

        public static void OpenExportsFolder()
        {
            var exportsPath = FileHelper.GetExportsDirectory();
            FileHelper.OpenDirectory(exportsPath);
        }

        public static void CleanupOldExports(int daysOld = 30)
        {
            var exportsPath = FileHelper.GetExportsDirectory();
            FileHelper.CleanupOldFiles(exportsPath, daysOld);
        }

        public static async Task ExportSupplierStatementToExcelAsync(object statementData, string fileName)
        {
            await Task.Run(() =>
            {
                // TODO: Implement Excel export for supplier statement
                throw new NotImplementedException("Excel export for supplier statement is not yet implemented");
            });
        }

        public static async Task ExportSupplierStatementToPdfAsync(object statementData, string fileName)
        {
            await Task.Run(() =>
            {
                // TODO: Implement PDF export for supplier statement
                throw new NotImplementedException("PDF export for supplier statement is not yet implemented");
            });
        }
    }
}
