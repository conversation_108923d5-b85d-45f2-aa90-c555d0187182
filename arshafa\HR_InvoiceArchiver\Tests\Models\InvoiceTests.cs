using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HR_InvoiceArchiver.Tests.Models
{
    public class InvoiceTests
    {
        [Fact]
        public void Invoice_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            var invoice = new Invoice();

            // Assert
            invoice.InvoiceNumber.Should().Be(string.Empty);
            invoice.Amount.Should().Be(0);
            invoice.PaidAmount.Should().Be(0);
            invoice.Status.Should().Be(InvoiceStatus.Unpaid);
            invoice.InvoiceDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
            invoice.CreatedDate.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
            invoice.IsActive.Should().BeTrue();
            invoice.Payments.Should().NotBeNull();
            invoice.Payments.Should().BeEmpty();
        }

        [Fact]
        public void Invoice_CalculatedPaidAmount_WithNoPayments_ShouldReturnZero()
        {
            // Arrange
            var invoice = new Invoice
            {
                Amount = 1000,
                Payments = new List<Payment>()
            };

            // Act
            var calculatedPaidAmount = invoice.CalculatedPaidAmount;

            // Assert
            calculatedPaidAmount.Should().Be(0);
        }

        [Fact]
        public void Invoice_CalculatedPaidAmount_WithActivePayments_ShouldReturnSum()
        {
            // Arrange
            var invoice = new Invoice
            {
                Amount = 1000,
                Payments = new List<Payment>
                {
                    new Payment { Amount = 300, IsActive = true },
                    new Payment { Amount = 200, IsActive = true },
                    new Payment { Amount = 100, IsActive = false } // غير نشط
                }
            };

            // Act
            var calculatedPaidAmount = invoice.CalculatedPaidAmount;

            // Assert
            calculatedPaidAmount.Should().Be(500); // 300 + 200 فقط
        }

        [Fact]
        public void Invoice_RemainingAmount_ShouldBeCalculatedCorrectly()
        {
            // Arrange
            var invoice = new Invoice
            {
                Amount = 1000,
                Payments = new List<Payment>
                {
                    new Payment { Amount = 300, IsActive = true }
                }
            };

            // Act
            var remainingAmount = invoice.RemainingAmount;

            // Assert
            remainingAmount.Should().Be(700); // 1000 - 300
        }

        [Fact]
        public void Invoice_IsOverdue_WithNoDueDate_ShouldReturnFalse()
        {
            // Arrange
            var invoice = new Invoice
            {
                DueDate = null,
                Status = InvoiceStatus.Unpaid
            };

            // Act
            var isOverdue = invoice.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void Invoice_IsOverdue_WithFutureDueDate_ShouldReturnFalse()
        {
            // Arrange
            var invoice = new Invoice
            {
                DueDate = DateTime.Now.AddDays(5),
                Status = InvoiceStatus.Unpaid
            };

            // Act
            var isOverdue = invoice.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void Invoice_IsOverdue_WithPastDueDateAndUnpaid_ShouldReturnTrue()
        {
            // Arrange
            var invoice = new Invoice
            {
                DueDate = DateTime.Now.AddDays(-5),
                Status = InvoiceStatus.Unpaid
            };

            // Act
            var isOverdue = invoice.IsOverdue;

            // Assert
            isOverdue.Should().BeTrue();
        }

        [Fact]
        public void Invoice_IsOverdue_WithPastDueDateButPaid_ShouldReturnFalse()
        {
            // Arrange
            var invoice = new Invoice
            {
                DueDate = DateTime.Now.AddDays(-5),
                Status = InvoiceStatus.Paid
            };

            // Act
            var isOverdue = invoice.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void Invoice_DaysOverdue_WithOverdueInvoice_ShouldReturnCorrectDays()
        {
            // Arrange
            var daysOverdue = 10;
            var invoice = new Invoice
            {
                DueDate = DateTime.Now.AddDays(-daysOverdue),
                Status = InvoiceStatus.Unpaid
            };

            // Act
            var actualDaysOverdue = invoice.DaysOverdue;

            // Assert
            actualDaysOverdue.Should().Be(daysOverdue);
        }

        [Fact]
        public void Invoice_StatusText_ShouldReturnCorrectArabicText()
        {
            // Arrange & Act & Assert
            var unpaidInvoice = new Invoice { Status = InvoiceStatus.Unpaid };
            unpaidInvoice.StatusText.Should().Be("غير مسددة");

            var partiallyPaidInvoice = new Invoice { Status = InvoiceStatus.PartiallyPaid };
            partiallyPaidInvoice.StatusText.Should().Be("تسديد جزئي");

            var paidInvoice = new Invoice { Status = InvoiceStatus.Paid };
            paidInvoice.StatusText.Should().Be("مسددة");

            var paidWithDiscountInvoice = new Invoice { Status = InvoiceStatus.PaidWithDiscount };
            paidWithDiscountInvoice.StatusText.Should().Be("مسددة وبخصم");
        }

        [Fact]
        public void Invoice_StatusColor_ShouldReturnCorrectColors()
        {
            // Arrange & Act & Assert
            var unpaidInvoice = new Invoice { Status = InvoiceStatus.Unpaid };
            unpaidInvoice.StatusColor.Should().Be("#FF6B6B");

            var partiallyPaidInvoice = new Invoice { Status = InvoiceStatus.PartiallyPaid };
            partiallyPaidInvoice.StatusColor.Should().Be("#FFD93D");

            var paidInvoice = new Invoice { Status = InvoiceStatus.Paid };
            paidInvoice.StatusColor.Should().Be("#6BCF7F");

            var paidWithDiscountInvoice = new Invoice { Status = InvoiceStatus.PaidWithDiscount };
            paidWithDiscountInvoice.StatusColor.Should().Be("#9C27B0");
        }

        [Theory]
        [InlineData("INV-001", "مورد تجريبي", 1000, "فاتورة #INV-001 - مورد تجريبي - 1,000 د.ع")]
        [InlineData("INV-002", "مورد آخر", 2500.50, "فاتورة #INV-002 - مورد آخر - 2,501 د.ع")]
        public void Invoice_DisplayText_ShouldFormatCorrectly(string invoiceNumber, string supplierName, decimal remainingAmount, string expected)
        {
            // Arrange
            var invoice = new Invoice
            {
                InvoiceNumber = invoiceNumber,
                Amount = remainingAmount,
                Supplier = new Supplier { Name = supplierName },
                Payments = new List<Payment>()
            };

            // Act
            var displayText = invoice.DisplayText;

            // Assert
            displayText.Should().Be(expected);
        }

        [Fact]
        public void Invoice_SupplierName_WithNullSupplier_ShouldReturnDefault()
        {
            // Arrange
            var invoice = new Invoice { Supplier = null! };

            // Act
            var supplierName = invoice.SupplierName;

            // Assert
            supplierName.Should().Be("غير محدد");
        }

        [Fact]
        public void Invoice_CanAddPayment_WithRemainingAmount_ShouldReturnTrue()
        {
            // Arrange
            var invoice = new Invoice
            {
                Amount = 1000,
                Payments = new List<Payment>
                {
                    new Payment { Amount = 300, IsActive = true }
                }
            };

            // Act
            var canAddPayment = invoice.CanAddPayment();

            // Assert
            canAddPayment.Should().BeTrue();
        }

        [Fact]
        public void Invoice_CanAddPayment_WithNoRemainingAmount_ShouldReturnFalse()
        {
            // Arrange
            var invoice = new Invoice
            {
                Amount = 1000,
                Payments = new List<Payment>
                {
                    new Payment { Amount = 1000, IsActive = true }
                }
            };

            // Act
            var canAddPayment = invoice.CanAddPayment();

            // Assert
            canAddPayment.Should().BeFalse();
        }
    }
}
