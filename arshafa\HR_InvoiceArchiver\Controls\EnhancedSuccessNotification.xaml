<UserControl x:Class="HR_InvoiceArchiver.Controls.EnhancedSuccessNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Width="400" Height="Auto"
             HorizontalAlignment="Right" VerticalAlignment="Top"
             Margin="20,20,20,0">

    <UserControl.Resources>
        <!-- Animation Resources -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetName="NotificationCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="450" To="0" Duration="0:0:0.4">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="NotificationCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>

        <Storyboard x:Key="SlideOutAnimation" Completed="SlideOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="NotificationCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="0" To="450" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="NotificationCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.2"/>
        </Storyboard>

        <Storyboard x:Key="PulseAnimation" RepeatBehavior="1x">
            <DoubleAnimation Storyboard.TargetName="IconContainer" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           From="1" To="1.2" Duration="0:0:0.15" AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="IconContainer" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           From="1" To="1.2" Duration="0:0:0.15" AutoReverse="True"/>
        </Storyboard>

        <!-- Progress Animation -->
        <Storyboard x:Key="ProgressAnimation">
            <DoubleAnimation Storyboard.TargetName="ProgressBar"
                           Storyboard.TargetProperty="Width"
                           From="400" To="0" Duration="0:0:5"/>
        </Storyboard>

        <!-- Success Gradient -->
        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#45A049" Offset="1"/>
        </LinearGradientBrush>

        <!-- Save Gradient -->
        <LinearGradientBrush x:Key="SaveGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#2196F3" Offset="0"/>
            <GradientStop Color="#1976D2" Offset="1"/>
        </LinearGradientBrush>

        <!-- Delete Gradient -->
        <LinearGradientBrush x:Key="DeleteGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF5722" Offset="0"/>
            <GradientStop Color="#D84315" Offset="1"/>
        </LinearGradientBrush>

        <!-- Update Gradient -->
        <LinearGradientBrush x:Key="UpdateGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF9800" Offset="0"/>
            <GradientStop Color="#F57C00" Offset="1"/>
        </LinearGradientBrush>

        <!-- Create Gradient -->
        <LinearGradientBrush x:Key="CreateGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#9C27B0" Offset="0"/>
            <GradientStop Color="#7B1FA2" Offset="1"/>
        </LinearGradientBrush>

        <!-- Icon Background -->
        <SolidColorBrush x:Key="SuccessIconBackground" Color="White" Opacity="0.2"/>

        <!-- Success Color -->
        <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50"/>

        <!-- Shadow Effect -->
        <DropShadowEffect x:Key="SuccessShadow"
                          Color="Black"
                          Opacity="0.3"
                          ShadowDepth="8"
                          BlurRadius="20"/>
    </UserControl.Resources>

    <!-- Main Notification Card -->
    <Border x:Name="NotificationCard" 
            Background="{DynamicResource SuccessGradient}"
            CornerRadius="12"
            BorderThickness="0"
            Effect="{DynamicResource SuccessShadow}">
        
        <Border.RenderTransform>
            <TranslateTransform X="450"/>
        </Border.RenderTransform>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="4"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <Grid Grid.Row="0" Margin="20,15,15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon Container -->
                <Border x:Name="IconContainer" 
                        Grid.Column="0"
                        Width="40" Height="40"
                        Background="{DynamicResource SuccessIconBackground}"
                        CornerRadius="20"
                        VerticalAlignment="Top">
                    <Border.RenderTransform>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Border.RenderTransform>
                    
                    <TextBlock x:Name="IconText" 
                               Text="✅"
                               FontSize="20"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>

                <!-- Title and Message -->
                <StackPanel Grid.Column="1" Margin="15,0,10,0">
                    <TextBlock x:Name="TitleText" 
                               Text="تم بنجاح!"
                               FontSize="16"
                               FontWeight="SemiBold"
                               Foreground="White"
                               TextWrapping="Wrap"/>
                    
                    <TextBlock x:Name="MessageText" 
                               Text="تمت العملية بنجاح"
                               FontSize="13"
                               Foreground="#E8F5E8"
                               TextWrapping="Wrap"
                               Margin="0,4,0,0"
                               LineHeight="18"/>
                </StackPanel>

                <!-- Close Button -->
                <Button x:Name="CloseButton"
                        Grid.Column="2"
                        Width="24" Height="24"
                        Style="{DynamicResource MaterialDesignIconButton}"
                        Foreground="White"
                        VerticalAlignment="Top"
                        Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                </Button>
            </Grid>

            <!-- Action Buttons -->
            <StackPanel x:Name="ActionPanel" 
                        Grid.Row="1" 
                        Orientation="Horizontal"
                        HorizontalAlignment="Right"
                        Margin="20,0,20,15"
                        Visibility="Collapsed">
                
                <Button x:Name="SecondaryActionButton"
                        Content="إضافة آخر"
                        Style="{DynamicResource MaterialDesignOutlinedButton}"
                        Foreground="White"
                        BorderBrush="White"
                        Margin="0,0,10,0"
                        Padding="16,6"
                        FontSize="12"
                        Click="SecondaryActionButton_Click"/>
                
                <Button x:Name="PrimaryActionButton"
                        Content="عرض التفاصيل"
                        Style="{DynamicResource MaterialDesignRaisedButton}"
                        Background="White"
                        Foreground="{DynamicResource SuccessColor}"
                        Padding="16,6"
                        FontSize="12"
                        Click="PrimaryActionButton_Click"/>
            </StackPanel>

            <!-- Timestamp -->
            <TextBlock x:Name="TimestampText"
                       Grid.Row="2"
                       Text="الآن"
                       FontSize="11"
                       Foreground="#B8E6B8"
                       HorizontalAlignment="Right"
                       Margin="0,0,20,10"/>

            <!-- Progress Bar -->
            <Rectangle x:Name="ProgressBar"
                       Grid.Row="3"
                       Height="4"
                       Fill="White"
                       Opacity="0.3"
                       HorizontalAlignment="Left"
                       Width="400"/>
        </Grid>
    </Border>
</UserControl>
