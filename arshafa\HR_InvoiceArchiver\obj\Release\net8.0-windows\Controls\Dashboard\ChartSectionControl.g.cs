﻿#pragma checksum "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DCB6FEFD3F7BAEFD205D5C6A8A36C0EE72C678A8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Controls.Dashboard {
    
    
    /// <summary>
    /// ChartSectionControl
    /// </summary>
    public partial class ChartSectionControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshChartButton;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportChartButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ChartPeriodComboBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowTotalAmountCheckBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowPaidAmountCheckBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart MonthlyChart;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ChartLoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NoDataPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/controls/dashboard/chartsectioncontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RefreshChartButton = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.RefreshChartButton.Click += new System.Windows.RoutedEventHandler(this.RefreshChartButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ExportChartButton = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.ExportChartButton.Click += new System.Windows.RoutedEventHandler(this.ExportChartButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ChartPeriodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 70 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.ChartPeriodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ChartPeriodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ShowTotalAmountCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 81 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.ShowTotalAmountCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            
            #line 82 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.ShowTotalAmountCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ShowPaidAmountCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 86 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.ShowPaidAmountCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            
            #line 87 "..\..\..\..\..\Controls\Dashboard\ChartSectionControl.xaml"
            this.ShowPaidAmountCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.MonthlyChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 7:
            this.ChartLoadingIndicator = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.NoDataPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

