<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="HR_InvoiceArchiver.Pages.DashboardPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.Resources>
        <ResourceDictionary>

            <!-- Modern Gradient Brushes -->
            <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#673AB7" Offset="0"/>
                <GradientStop Color="#9C27B0" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#4CAF50" Offset="0"/>
                <GradientStop Color="#8BC34A" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="WarningGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF9800" Offset="0"/>
                <GradientStop Color="#FFC107" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="ErrorGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#F44336" Offset="0"/>
                <GradientStop Color="#E91E63" Offset="1"/>
            </LinearGradientBrush>

            <!-- Modern Card Style -->
            <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#60000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Animated Statistics Card Style -->
            <Style x:Key="StatCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCardStyle}">
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1.05" Duration="0:0:0.2"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1.05" Duration="0:0:0.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                        <Trigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1" Duration="0:0:0.2"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1" Duration="0:0:0.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.ExitActions>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Modern Button Style -->
            <Style x:Key="ModernActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Height" Value="90"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1.03" Duration="0:0:0.15"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1.03" Duration="0:0:0.15"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                        <Trigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1" Duration="0:0:0.15"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1" Duration="0:0:0.15"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.ExitActions>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Loading Animation -->
            <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
                <DoubleAnimation Storyboard.TargetName="LoadingIcon"
                               Storyboard.TargetProperty="RenderTransform.Angle"
                               From="0" To="360" Duration="0:0:2"/>
            </Storyboard>

            <!-- Fade In Animation -->
            <Storyboard x:Key="FadeInAnimation">
                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                               From="0" To="1" Duration="0:0:0.5"/>
                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                               From="20" To="0" Duration="0:0:0.5"/>
            </Storyboard>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Main Container Grid -->
    <Grid>
        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Background="#80FFFFFF" Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="LoadingIcon" Kind="Loading" Width="48" Height="48"
                                       Foreground="{StaticResource PrimaryGradientBrush}">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
                <TextBlock Text="جاري تحميل البيانات..." Margin="0,16,0,0"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="24" x:Name="MainContent">
            <Grid.RenderTransform>
                <TranslateTransform Y="0"/>
            </Grid.RenderTransform>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Modern Welcome Header -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,24">
                <Grid>
                    <Grid.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#673AB7" Offset="0"/>
                            <GradientStop Color="#9C27B0" Offset="0.5"/>
                            <GradientStop Color="#E91E63" Offset="1"/>
                        </LinearGradientBrush>
                    </Grid.Background>
                    <Grid Margin="32">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <materialDesign:PackIcon Grid.Column="0" Kind="ViewDashboard" Width="64" Height="64"
                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,24,0"/>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="مرحباً بك في نظام أرشيف الفواتير"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="White" FontWeight="Bold"/>
                            <TextBlock x:Name="WelcomeSubtitle" Text="إدارة شاملة وذكية لفواتيرك ومدفوعاتك"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     Foreground="White" Opacity="0.9" Margin="0,8,0,0"/>
                            <TextBlock x:Name="LastUpdateText" Text="آخر تحديث: الآن"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="White" Opacity="0.7" Margin="0,4,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                            <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                  Foreground="White" ToolTip="تحديث البيانات" Click="RefreshButton_Click"
                                  Width="48" Height="48">
                                <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                            </Button>
                            <Button x:Name="SettingsQuickButton" Style="{StaticResource MaterialDesignIconButton}"
                                  Foreground="White" ToolTip="الإعدادات السريعة" Click="SettingsButton_Click"
                                  Width="48" Height="48" Margin="0,8,0,0">
                                <materialDesign:PackIcon Kind="Cog" Width="24" Height="24"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Quick Stats Summary -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,24">
                <Grid Margin="24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <TextBlock x:Name="QuickTotalInvoices" Text="0"
                                 Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                 Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="إجمالي الفواتير" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 HorizontalAlignment="Center" Opacity="0.7"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                        <TextBlock x:Name="QuickTotalAmount" Text="0 د.ع"
                                 Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                 Foreground="{StaticResource SecondaryGradientBrush}" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="إجمالي المبلغ" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 HorizontalAlignment="Center" Opacity="0.7"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <TextBlock x:Name="QuickPaidAmount" Text="0 د.ع"
                                 Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                 Foreground="#4CAF50" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="المبلغ المسدد" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 HorizontalAlignment="Center" Opacity="0.7"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                        <TextBlock x:Name="QuickOutstandingAmount" Text="0 د.ع"
                                 Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                 Foreground="{StaticResource ErrorGradientBrush}" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="المبلغ المتبقي" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 HorizontalAlignment="Center" Opacity="0.7"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Enhanced Statistics Cards -->
            <Grid Grid.Row="2" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Invoices Card -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCardStyle}" Cursor="Hand"
                                   MouseLeftButtonUp="TotalInvoicesCard_Click">
                    <Grid>
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#673AB7" Offset="0"/>
                                <GradientStop Color="#9C27B0" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <materialDesign:PackIcon Grid.Row="0" Kind="FileDocument" Width="40" Height="40"
                                                   Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Row="1" x:Name="TotalInvoicesText" Text="0" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignHeadline2TextBlock}"
                                     Foreground="White" FontWeight="Bold" Margin="0,12,0,0"/>
                            <TextBlock Grid.Row="2" Text="إجمالي الفواتير" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                            <ProgressBar Grid.Row="3" x:Name="TotalInvoicesProgress" Height="4" Margin="0,8,0,0"
                                       Background="#40FFFFFF" Foreground="White" Value="100"/>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Total Amount Card -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCardStyle}" Cursor="Hand"
                                   MouseLeftButtonUp="TotalAmountCard_Click">
                    <Grid>
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#4CAF50" Offset="0"/>
                                <GradientStop Color="#8BC34A" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <materialDesign:PackIcon Grid.Row="0" Kind="CurrencyUsd" Width="40" Height="40"
                                                   Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Row="1" x:Name="TotalAmountText" Text="0 د.ع" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignHeadline2TextBlock}"
                                     Foreground="White" FontWeight="Bold" Margin="0,12,0,0"/>
                            <TextBlock Grid.Row="2" Text="إجمالي المبلغ" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                            <ProgressBar Grid.Row="3" x:Name="TotalAmountProgress" Height="4" Margin="0,8,0,0"
                                       Background="#40FFFFFF" Foreground="White" Value="100"/>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Paid Amount Card -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCardStyle}" Cursor="Hand"
                                   MouseLeftButtonUp="PaidAmountCard_Click">
                    <Grid>
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#2196F3" Offset="0"/>
                                <GradientStop Color="#03DAC6" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <materialDesign:PackIcon Grid.Row="0" Kind="CheckCircle" Width="40" Height="40"
                                                   Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Row="1" x:Name="PaidAmountText" Text="0 د.ع" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignHeadline2TextBlock}"
                                     Foreground="White" FontWeight="Bold" Margin="0,12,0,0"/>
                            <TextBlock Grid.Row="2" Text="المبلغ المسدد" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                            <ProgressBar Grid.Row="3" x:Name="PaidAmountProgress" Height="4" Margin="0,8,0,0"
                                       Background="#40FFFFFF" Foreground="White" Value="0"/>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Outstanding Amount Card -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCardStyle}" Cursor="Hand"
                                   MouseLeftButtonUp="OutstandingAmountCard_Click">
                    <Grid>
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#F44336" Offset="0"/>
                                <GradientStop Color="#E91E63" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <materialDesign:PackIcon Grid.Row="0" Kind="AlertCircle" Width="40" Height="40"
                                                   Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Row="1" x:Name="OutstandingAmountText" Text="0 د.ع" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignHeadline2TextBlock}"
                                     Foreground="White" FontWeight="Bold" Margin="0,12,0,0"/>
                            <TextBlock Grid.Row="2" Text="المبلغ المتبقي" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                            <ProgressBar Grid.Row="3" x:Name="OutstandingAmountProgress" Height="4" Margin="0,8,0,0"
                                       Background="#40FFFFFF" Foreground="White" Value="0"/>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Overdue Invoices Card -->
                <materialDesign:Card Grid.Column="4" Style="{StaticResource StatCardStyle}" Cursor="Hand"
                                   MouseLeftButtonUp="OverdueInvoicesCard_Click">
                    <Grid>
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FF9800" Offset="0"/>
                                <GradientStop Color="#FFC107" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid Margin="24">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <materialDesign:PackIcon Grid.Row="0" Kind="ClockAlert" Width="40" Height="40"
                                                   Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Grid.Row="1" x:Name="OverdueInvoicesText" Text="0" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignHeadline2TextBlock}"
                                     Foreground="White" FontWeight="Bold" Margin="0,12,0,0"/>
                            <TextBlock Grid.Row="2" Text="فواتير متأخرة" HorizontalAlignment="Center"
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                            <ProgressBar Grid.Row="3" x:Name="OverdueInvoicesProgress" Height="4" Margin="0,8,0,0"
                                       Background="#40FFFFFF" Foreground="White" Value="0"/>
                        </Grid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Charts and Analytics Section -->
            <Grid Grid.Row="3" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Enhanced Monthly Trends Chart -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="32">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="350"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="الاتجاهات الشهرية"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="تحليل شامل للفواتير والمدفوعات خلال الأشهر الماضية"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,4,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="ChartTypeButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="تغيير نوع المخطط" Click="ChartTypeButton_Click">
                                    <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20"/>
                                </Button>
                                <Button x:Name="ExportChartButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="تصدير المخطط" Click="ExportChartButton_Click" Margin="8,0,0,0">
                                    <materialDesign:PackIcon Kind="Download" Width="20" Height="20"/>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- Chart Controls -->
                        <Grid Grid.Row="1" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الفترة:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Column="1" x:Name="ChartPeriodComboBox" Width="150" HorizontalAlignment="Left"
                                    SelectionChanged="ChartPeriodComboBox_SelectionChanged">
                                <ComboBoxItem Content="آخر 6 أشهر" IsSelected="True"/>
                                <ComboBoxItem Content="آخر 12 شهر"/>
                                <ComboBoxItem Content="هذا العام"/>
                                <ComboBoxItem Content="العام الماضي"/>
                            </ComboBox>

                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                <CheckBox x:Name="ShowTotalAmountCheckBox" Content="إجمالي المبلغ" IsChecked="True"
                                        Margin="0,0,16,0" Checked="ChartOptions_Changed" Unchecked="ChartOptions_Changed"/>
                                <CheckBox x:Name="ShowPaidAmountCheckBox" Content="المبلغ المسدد" IsChecked="True"
                                        Checked="ChartOptions_Changed" Unchecked="ChartOptions_Changed"/>
                            </StackPanel>
                        </Grid>

                        <!-- Enhanced Chart Container -->
                        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}"
                              CornerRadius="8" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1">
                            <Grid>
                                <lvc:CartesianChart x:Name="MonthlyChart" LegendLocation="Bottom" Margin="16"
                                                  AnimationsSpeed="0:0:0.5" Hoverable="True" DataTooltip="{x:Null}">
                                    <lvc:CartesianChart.AxisX>
                                        <lvc:Axis Title="الشهر" FontSize="12" Foreground="{DynamicResource MaterialDesignBody}"
                                                FontFamily="{DynamicResource MaterialDesignFont}">
                                            <lvc:Axis.Separator>
                                                <lvc:Separator StrokeThickness="1" StrokeDashArray="2"
                                                             Stroke="{DynamicResource MaterialDesignDivider}"/>
                                            </lvc:Axis.Separator>
                                        </lvc:Axis>
                                    </lvc:CartesianChart.AxisX>
                                    <lvc:CartesianChart.AxisY>
                                        <lvc:Axis Title="المبلغ (د.ع)" FontSize="12" Foreground="{DynamicResource MaterialDesignBody}"
                                                LabelFormatter="{Binding YFormatter}" FontFamily="{DynamicResource MaterialDesignFont}">
                                            <lvc:Axis.Separator>
                                                <lvc:Separator StrokeThickness="0.5" StrokeDashArray="4"
                                                             Stroke="{DynamicResource MaterialDesignDivider}"/>
                                            </lvc:Axis.Separator>
                                        </lvc:Axis>
                                    </lvc:CartesianChart.AxisY>
                                </lvc:CartesianChart>

                                <!-- Chart Loading Indicator -->
                                <Grid x:Name="ChartLoadingIndicator" Background="#80FFFFFF" Visibility="Collapsed">
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <ProgressBar IsIndeterminate="True" Width="100" Height="4"
                                                   Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                                        <TextBlock Text="جاري تحميل البيانات..." Margin="0,8,0,0"
                                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Enhanced Status Distribution -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="توزيع حالات الفواتير"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold" Margin="0,0,0,8"/>

                        <TextBlock Grid.Row="1" Text="نظرة عامة على حالة جميع الفواتير"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.7" Margin="0,0,0,16"/>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <!-- Enhanced Status Items -->
                                <Border Background="#FFF3E0" CornerRadius="8" Padding="16" Margin="0,0,0,12">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0" Kind="AlertCircle" Width="20" Height="20"
                                                               Foreground="#F44336" VerticalAlignment="Center"/>
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <TextBlock Text="غير مسددة" FontWeight="Medium"/>
                                            <ProgressBar x:Name="UnpaidProgressBar" Height="4" Margin="0,4,0,0"
                                                       Background="#FFCDD2" Foreground="#F44336" Value="0"/>
                                        </StackPanel>
                                        <TextBlock Grid.Column="2" x:Name="UnpaidCountText" Text="0" FontWeight="Bold"
                                                 FontSize="18" Foreground="#F44336" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <Border Background="#FFF8E1" CornerRadius="8" Padding="16" Margin="0,0,0,12">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0" Kind="ClockAlert" Width="20" Height="20"
                                                               Foreground="#FF9800" VerticalAlignment="Center"/>
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <TextBlock Text="تسديد جزئي" FontWeight="Medium"/>
                                            <ProgressBar x:Name="PartiallyPaidProgressBar" Height="4" Margin="0,4,0,0"
                                                       Background="#FFECB3" Foreground="#FF9800" Value="0"/>
                                        </StackPanel>
                                        <TextBlock Grid.Column="2" x:Name="PartiallyPaidCountText" Text="0" FontWeight="Bold"
                                                 FontSize="18" Foreground="#FF9800" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <Border Background="#E8F5E8" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0" Kind="CheckCircle" Width="20" Height="20"
                                                               Foreground="#4CAF50" VerticalAlignment="Center"/>
                                        <StackPanel Grid.Column="1" Margin="12,0,0,0">
                                            <TextBlock Text="مسددة" FontWeight="Medium"/>
                                            <ProgressBar x:Name="PaidProgressBar" Height="4" Margin="0,4,0,0"
                                                       Background="#C8E6C9" Foreground="#4CAF50" Value="0"/>
                                        </StackPanel>
                                        <TextBlock Grid.Column="2" x:Name="PaidCountText" Text="0" FontWeight="Bold"
                                                 FontSize="18" Foreground="#4CAF50" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- Overall Payment Rate -->
                                <Separator Margin="0,8,0,16"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="معدل التسديد الإجمالي" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="1" x:Name="PaymentRateText" Text="0%" FontWeight="Bold"
                                             FontSize="16" Foreground="{StaticResource PrimaryGradientBrush}"/>
                                </Grid>
                                <ProgressBar x:Name="PaymentRateProgress" Height="8" Value="0" Maximum="100" Margin="0,8,0,0"
                                           Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Recent Activities and Alerts -->
            <Grid Grid.Row="4" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Enhanced Recent Invoices -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="350"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="الفواتير الحديثة"
                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="آخر الفواتير المضافة إلى النظام"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="FilterRecentButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="فلترة الفواتير" Click="FilterRecentButton_Click">
                                    <materialDesign:PackIcon Kind="Filter" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                      Content="عرض الكل" Click="ViewAllInvoicesButton_Click" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Grid>

                        <!-- Filter Options -->
                        <Grid Grid.Row="1" x:Name="RecentInvoicesFilterPanel" Visibility="Collapsed" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Column="1" x:Name="RecentStatusFilterComboBox" Width="120"
                                    SelectionChanged="RecentStatusFilter_Changed">
                                <ComboBoxItem Content="الكل" IsSelected="True"/>
                                <ComboBoxItem Content="غير مسددة"/>
                                <ComboBoxItem Content="تسديد جزئي"/>
                                <ComboBoxItem Content="مسددة"/>
                            </ComboBox>

                            <TextBlock Grid.Column="2" Text="الفترة:" VerticalAlignment="Center" Margin="16,0,8,0"/>
                            <ComboBox Grid.Column="3" x:Name="RecentPeriodFilterComboBox" Width="120"
                                    SelectionChanged="RecentPeriodFilter_Changed">
                                <ComboBoxItem Content="آخر أسبوع" IsSelected="True"/>
                                <ComboBoxItem Content="آخر شهر"/>
                                <ComboBoxItem Content="آخر 3 أشهر"/>
                            </ComboBox>
                        </Grid>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="RecentInvoicesItemsControl">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Margin="0,0,0,12" Padding="16" Cursor="Hand"
                                              MouseLeftButtonUp="RecentInvoiceItem_Click">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#20000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                                            </Border.Effect>
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Grid.Column="0" Kind="FileDocument" Width="24" Height="24"
                                                                       Foreground="{StaticResource PrimaryGradientBrush}"
                                                                       VerticalAlignment="Top" Margin="0,0,12,0"/>

                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium" FontSize="14"/>
                                                    <TextBlock Text="{Binding Supplier.Name}" FontSize="12" Opacity="0.7" Margin="0,2,0,0"/>
                                                    <TextBlock Text="{Binding InvoiceDate, StringFormat='{}{0:yyyy/MM/dd}'}"
                                                             FontSize="11" Opacity="0.6" Margin="0,2,0,0"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                                    <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} د.ع'}"
                                                             FontWeight="Medium" HorizontalAlignment="Right" FontSize="14"/>
                                                    <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4" Margin="0,4,0,0">
                                                        <TextBlock Text="{Binding StatusText}" Foreground="White"
                                                                 FontSize="10" HorizontalAlignment="Center" FontWeight="Medium"/>
                                                    </Border>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- Enhanced Alerts and Notifications -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="التنبيهات والإشعارات"
                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="تنبيهات مهمة تتطلب انتباهك"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="AlertSettingsButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="إعدادات التنبيهات" Click="AlertSettingsButton_Click">
                                    <materialDesign:PackIcon Kind="BellSettings" Width="18" Height="18"/>
                                </Button>
                                <Button x:Name="ClearAlertsButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="مسح جميع التنبيهات" Click="ClearAlertsButton_Click" Margin="8,0,0,0">
                                    <materialDesign:PackIcon Kind="BellOff" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- Alert Summary -->
                        <Grid Grid.Row="1" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock x:Name="CriticalAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#F44336" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="حرجة" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock x:Name="WarningAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#FF9800" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="تحذيرية" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock x:Name="InfoAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#2196F3" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="معلوماتية" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>
                        </Grid>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <StackPanel x:Name="AlertsStackPanel">
                                <!-- Alerts will be populated dynamically -->
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Enhanced Quick Actions -->
            <materialDesign:Card Grid.Row="5" Style="{StaticResource ModernCardStyle}">
                <Grid Margin="32">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Margin="0,0,0,16">
                        <TextBlock Text="الإجراءات السريعة"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                        <TextBlock Text="الوصول السريع للمهام الأكثر استخداماً"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.7" Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Action Categories -->
                    <Grid Grid.Row="1" Margin="0,0,0,24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="InvoiceActionsTab" Content="الفواتير"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              Click="ActionTab_Click" Tag="invoices" IsDefault="True"/>
                        <Button Grid.Column="1" x:Name="PaymentActionsTab" Content="المدفوعات"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              Click="ActionTab_Click" Tag="payments"/>
                        <Button Grid.Column="2" x:Name="SystemActionsTab" Content="النظام"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              Click="ActionTab_Click" Tag="system"/>
                    </Grid>

                    <!-- Action Buttons Container -->
                    <Grid Grid.Row="2" x:Name="ActionsContainer">
                        <!-- Invoice Actions -->
                        <UniformGrid x:Name="InvoiceActionsPanel" Columns="3" Rows="2" Visibility="Visible">
                            <!-- Add Invoice -->
                            <Button x:Name="AddInvoiceButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AddInvoiceButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#673AB7" Offset="0"/>
                                        <GradientStop Color="#9C27B0" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Plus" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إضافة فاتورة" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إنشاء فاتورة جديدة" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- View Invoices -->
                            <Button x:Name="ViewInvoicesButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ViewInvoicesButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#2196F3" Offset="0"/>
                                        <GradientStop Color="#03DAC6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="FileDocument" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="عرض الفواتير" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استعراض جميع الفواتير" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Search Invoices -->
                            <Button x:Name="SearchInvoicesButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SearchButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#4CAF50" Offset="0"/>
                                        <GradientStop Color="#8BC34A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Magnify" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="البحث المتقدم" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="البحث في الفواتير" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Manage Suppliers -->
                            <Button x:Name="SuppliersButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SuppliersButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF9800" Offset="0"/>
                                        <GradientStop Color="#FFC107" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إدارة الموردين" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إضافة وتعديل الموردين" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Invoice Reports -->
                            <Button x:Name="InvoiceReportsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ReportsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E91E63" Offset="0"/>
                                        <GradientStop Color="#F44336" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ChartLine" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تقارير الفواتير" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إحصائيات وتقارير" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Import Invoices -->
                            <Button x:Name="ImportInvoicesButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ImportInvoicesButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#795548" Offset="0"/>
                                        <GradientStop Color="#8D6E63" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Import" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="استيراد فواتير" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استيراد من ملف Excel" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>

                        <!-- Payment Actions -->
                        <UniformGrid x:Name="PaymentActionsPanel" Columns="3" Rows="2" Visibility="Collapsed">
                            <!-- Add Payment -->
                            <Button x:Name="AddPaymentButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AddPaymentButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#4CAF50" Offset="0"/>
                                        <GradientStop Color="#8BC34A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CreditCard" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تسجيل دفعة" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إضافة دفعة جديدة" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Add Multi Payment -->
                            <Button x:Name="AddMultiPaymentButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AddMultiPaymentButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF6B73" Offset="0"/>
                                        <GradientStop Color="#009FFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CreditCardMultiple" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="وصل متعدد" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="دفع عدة فواتير معاً" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- View Payments -->
                            <Button x:Name="ViewPaymentsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ViewPaymentsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#2196F3" Offset="0"/>
                                        <GradientStop Color="#03DAC6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CashMultiple" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="عرض المدفوعات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استعراض جميع المدفوعات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Payment Reports -->
                            <Button x:Name="PaymentReportsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="PaymentReportsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E91E63" Offset="0"/>
                                        <GradientStop Color="#F44336" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ChartBar" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تقارير المدفوعات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إحصائيات المدفوعات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Reconciliation -->
                            <Button x:Name="ReconciliationButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ReconciliationButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF9800" Offset="0"/>
                                        <GradientStop Color="#FFC107" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ScaleBalance" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تسوية الحسابات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="مطابقة المدفوعات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Payment Reminders -->
                            <Button x:Name="PaymentRemindersButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="PaymentRemindersButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#673AB7" Offset="0"/>
                                        <GradientStop Color="#9C27B0" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="BellAlert" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تذكيرات الدفع" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إدارة التذكيرات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Export Payments -->
                            <Button x:Name="ExportPaymentsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ExportPaymentsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#795548" Offset="0"/>
                                        <GradientStop Color="#8D6E63" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Export" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تصدير المدفوعات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="تصدير إلى Excel" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>

                        <!-- System Actions -->
                        <UniformGrid x:Name="SystemActionsPanel" Columns="3" Rows="2" Visibility="Collapsed">
                            <!-- Settings -->
                            <Button x:Name="SettingsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SettingsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#607D8B" Offset="0"/>
                                        <GradientStop Color="#90A4AE" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Settings" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="الإعدادات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إعدادات النظام" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Backup -->
                            <Button x:Name="BackupButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="BackupButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#4CAF50" Offset="0"/>
                                        <GradientStop Color="#8BC34A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="DatabaseExport" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="نسخ احتياطي" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="حفظ البيانات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Restore -->
                            <Button x:Name="RestoreButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="RestoreButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF9800" Offset="0"/>
                                        <GradientStop Color="#FFC107" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="DatabaseImport" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="استعادة البيانات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استعادة من نسخة احتياطية" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- User Management -->
                            <Button x:Name="UserManagementButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="UserManagementButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#673AB7" Offset="0"/>
                                        <GradientStop Color="#9C27B0" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="AccountMultiple" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إدارة المستخدمين" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="المستخدمين والصلاحيات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- System Logs -->
                            <Button x:Name="SystemLogsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SystemLogsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#2196F3" Offset="0"/>
                                        <GradientStop Color="#03DAC6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="FileDocumentOutline" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="سجلات النظام" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="عرض سجلات العمليات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- About -->
                            <Button x:Name="AboutButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AboutButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E91E63" Offset="0"/>
                                        <GradientStop Color="#F44336" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Information" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="حول البرنامج" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="معلومات النسخة" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
