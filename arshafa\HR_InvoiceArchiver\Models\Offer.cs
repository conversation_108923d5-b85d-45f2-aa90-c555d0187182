using System;

namespace HR_InvoiceArchiver.Models
{
    public class Offer
    {
        public int Id { get; set; }
        public string? ScientificOffice { get; set; }
        public string? RepresentativeName { get; set; }
        public string? RepresentativePhone { get; set; }
        public string? ScientificName { get; set; }
        public string? TradeName { get; set; }
        public decimal Price { get; set; }
        public string? BonusOrDiscount { get; set; }
        public string? Notes { get; set; }
        public string? AttachmentPath { get; set; } // مسار الملف المرفق (قد يكون null)
        public DateTime CreatedAt { get; set; }
    }
} 